# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 提供中文项目指导，确保 AI 助手能使用中文与开发者高效交流。

## 交流语言
- **主要语言**：中文（简体中文）
- **技术术语**：可同时使用英文技术术语（如 Vue、TypeScript、API 等）
- **代码注释**：推荐使用中文注释，除非团队有其他约定

## 项目概览
这是一个基于 Vue 3 + TypeScript + Vite 的 CRM 管理系统，采用 Naive UI 框架构建。支持多平台（管理后台、商城、门店、代理）的完整业务解决方案。

## 技术栈
- **前端框架**：Vue 3.5.13 + TypeScript 5.8.3
- **构建工具**：Vite 5.4.11
- **UI 组件库**：Naive UI 2.41.0（现代化组件库）
- **状态管理**：Pinia 2.3.0（支持持久化存储）
- **路由管理**：Vue Router 4.5.0
- **HTTP 客户端**：Alova 3.2.13（基于 Axios 适配器）
- **样式方案**：SCSS + Tailwind CSS（原子化 CSS 框架）
- **国际化**：Vue I18n 9.9.1（多语言支持）

## 开发命令

### 环境搭建与安装
```bash
# 安装依赖（必须使用 pnpm，不可使用 npm 或 yarn）
pnpm install

# 启动开发服务器
pnpm dev

# 生产环境构建
pnpm build

# TypeScript 类型检查
pnpm type:check

# 代码格式检查与修复
pnpm lint
```

### 包管理器说明
**必须使用 pnpm**，项目已配置 pnpm-lock.yaml，使用 npm 或 yarn 会导致依赖冲突。

### 可用脚本详解
- `pnpm dev` - 启动开发服务器，支持热重载
- `pnpm build` - 生产环境构建，包含代码压缩和优化
- `pnpm type:check` - TypeScript 类型检查，确保类型安全
- `pnpm lint` - 运行所有代码检查工具（ESLint + Prettier + Stylelint）
- `pnpm preview` - 本地预览生产构建结果
- `pnpm clean:cache` - 清理构建缓存，解决构建异常问题

## 项目结构

### 核心目录结构
```
src/
├── api/           # API 服务层（按业务域组织）
├── components/    # 可复用 Vue 组件
├── views/         # 页面组件（按平台分类）
├── store/         # Pinia 状态管理模块
├── router/        # Vue 路由配置
├── assets/        # 静态资源（图片、字体、图标）
├── styles/        # 全局样式和主题变量
├── locales/       # 国际化翻译文件
├── utils/         # 工具函数和辅助类
├── hooks/         # 组合式函数和自定义钩子
└── plugins/       # Vue 插件和全局配置
```

### API 分层架构
API 按业务领域分类：
- `admin/` - 管理后台 API（客户、线索、商品、权限管理）
- `mall/` - 商城业务 API（商品、订单、营销活动）
- `system/` - 系统管理 API（用户管理、字典配置、附件管理）
- `common/` - 通用共享 API（公共接口）

### 状态管理模块
- `user.ts` - 用户认证和个人信息管理
- `asyncRoute.ts` - 动态路由和权限控制
- `designSetting.ts` - 主题和布局设置
- `projectSetting.ts` - 项目全局配置
- `decoration.ts` - 页面装饰和外观设置

## 架构设计模式

### 核心组件设计
- **AdvancedTable**: 综合表格组件，支持 CRUD 操作、高级筛选、分页
- **Form**: 动态表单组件，内置完整验证机制
- **Modal/Drawer**: 模态框和抽屉组件，支持多层嵌套
- **Upload**: 文件上传组件，支持多种上传模式（单图、多图、拖拽上传）

### 路由管理策略
- **动态路由**：根据用户权限动态加载路由配置
- **平台化路由**：不同平台（管理后台、商城、门店、代理）使用独立路由模块
- **权限控制**：基于用户角色和权限的精细化路由访问控制

### 状态管理架构
- **Pinia 模块化**：清晰的状态模块划分，职责单一
- **数据持久化**：关键状态使用 pinia-plugin-persistedstate 实现本地存储
- **状态重置**：登出时完整清理用户相关状态，避免数据残留

### API 服务层设计
- **Alova 请求管理**：基于 Alova 的请求管理，集成 Axios 适配器
- **Mock 数据支持**：开发环境内置 Mock 数据，支持无后端开发
- **类型安全**：完整的 TypeScript 类型定义，从 API 到组件全链路类型保护

## 开发规范指南

### 代码质量标准
- **ESLint**：基于 Airbnb 规范，集成 TypeScript 支持
- **Prettier**：统一代码格式化标准
- **Stylelint**：SCSS 样式规范检查
- **TypeScript**：严格模式启用，强制类型检查

### 组件开发规范
- **组合式 API**：优先使用 Composition API，避免 Options API
- **TypeScript**：所有组件必须使用 TypeScript 编写
- **Props 验证**：使用 vue-types 进行属性类型验证
- **插槽设计**：使用具名插槽提升组件扩展性

### 文件命名规范
- **组件文件**：PascalCase 命名（如：`UserProfile.vue`）
- **组合式函数**：camelCase 命名，使用 'use' 前缀（如：`useUser.ts`）
- **工具函数**：camelCase 命名（如：`dateUtil.ts`）
- **状态模块**：kebab-case 命名，使用 .store.ts 后缀（如：`user.store.ts`）

### 环境变量配置
查看 `.env` 系列文件获取配置信息，关键变量：
- `VITE_PORT` - 开发服务器端口配置
- `VITE_PROXY` - API 代理配置，解决跨域问题
- `VITE_PUBLIC_PATH` - 部署时的公共路径
- `VITE_DROP_CONSOLE` - 生产环境自动移除 console.log

## 核心功能特性
- **多平台支持**：统一管理后台、商城、门店、代理四大平台
- **动态权限系统**：基于角色的精细化权限控制（RBAC）
- **主题管理系统**：支持多套主题切换，可自定义主题变量
- **文件上传系统**：完整的文件上传解决方案，包含图片压缩处理
- **数据导出功能**：支持 Excel 格式数据导出，可自定义导出模板
- **实时数据推送**：WebSocket 实时数据更新，支持消息通知
- **微前端架构**：内置 Wujie 微前端集成，支持子应用动态加载