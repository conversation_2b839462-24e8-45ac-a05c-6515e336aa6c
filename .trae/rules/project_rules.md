# AI CRM Admin 项目规范文档

## 目录

- [项目介绍](#项目介绍)
- [技术栈](#技术栈)
- [开发环境](#开发环境)
- [项目结构](#项目结构)
- [编码规范](#编码规范)
  - [JavaScript/TypeScript 规范](#javascripttypescript-规范)
  - [Vue 组件规范](#vue-组件规范)
  - [CSS/SCSS 规范](#cssscss-规范)
  - [命名规范](#命名规范)
- [Git 工作流](#git-工作流)
  - [分支管理](#分支管理)
  - [提交规范](#提交规范)
- [构建与部署](#构建与部署)
- [国际化](#国际化)
- [最佳实践](#最佳实践)

## 项目介绍

AI CRM Admin 是一个基于 Vue3、Vite、Naive UI、TypeScript 的中后台解决方案，提供了完善的权限管理、动态路由、组件封装等功能，用于快速搭建企业级中后台项目。

## 路由管理

项目使用 [Vue Router](https://router.vuejs.org/) 进行路由管理，支持动态路由、路由守卫和权限控制。

### 目录结构

```
src/router/
├── index.ts                # 路由入口文件
├── routes/                 # 路由配置目录
│   ├── index.ts            # 路由汇总
│   ├── modules/            # 按模块划分的路由
│   │   ├── dashboard.ts    # 仪表盘路由
│   │   ├── system.ts       # 系统管理路由
│   │   └── ...             # 其他模块路由
│   ├── basic.ts            # 基础路由（登录、404等）
│   └── constants.ts        # 路由常量
├── guard/                  # 路由守卫
│   ├── index.ts            # 守卫入口
│   ├── permission.ts       # 权限守卫
│   └── ...                 # 其他守卫
└── helper.ts               # 路由辅助函数
```

### 路由配置

路由配置采用模块化管理，按功能模块拆分：

```typescript
// src/router/routes/modules/dashboard.ts
import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/layouts';

const dashboard: RouteRecordRaw = {
  path: '/dashboard',
  name: 'Dashboard',
  component: Layout,
  redirect: '/dashboard/analysis',
  meta: {
    title: 'routes.dashboard.dashboard',
    icon: 'DashboardOutlined',
    sort: 0,
    permissions: ['dashboard'], // 所需权限
  },
  children: [
    {
      path: 'analysis',
      name: 'Analysis',
      component: () => import('@/views/dashboard/analysis/index.vue'),
      meta: {
        title: 'routes.dashboard.analysis',
        permissions: ['dashboard.analysis'],
      },
    },
    {
      path: 'workbench',
      name: 'Workbench',
      component: () => import('@/views/dashboard/workbench/index.vue'),
      meta: {
        title: 'routes.dashboard.workbench',
        permissions: ['dashboard.workbench'],
      },
    },
  ],
};

export default dashboard;
```

### 路由元信息（Meta）

路由元信息用于配置路由的附加信息，支持以下字段：

```typescript
interface RouteMeta {
  // 路由标题，支持国际化
  title: string;
  // 菜单图标
  icon?: string;
  // 是否在菜单中隐藏
  hidden?: boolean;
  // 是否保持组件状态（缓存）
  keepAlive?: boolean;
  // 是否固定在标签页
  affix?: boolean;
  // 排序，数字越小越靠前
  sort?: number;
  // 所需权限
  permissions?: string[];
  // 外部链接
  externalLink?: string;
  // 是否在新窗口打开
  target?: '_blank' | '_self';
  // 是否为单级菜单
  single?: boolean;
  // 是否为父级菜单
  isParent?: boolean;
  // 是否为框架内页面
  frameSrc?: string;
  // 过渡动画名称
  transitionName?: string;
  // 是否不缓存
  noCache?: boolean;
  // 是否不添加到标签页
  hideTab?: boolean;
  // 是否不添加到面包屑
  hideBreadcrumb?: boolean;
}
```

### 动态路由

项目支持根据用户权限动态加载路由：

```typescript
// src/router/guard/permission.ts
import { Router } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { usePermissionStore } from '@/store/modules/permission';

export function createPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore();
    const permissionStore = usePermissionStore();
    
    // 检查用户是否已登录
    if (userStore.isLoggedIn) {
      // 检查是否已加载权限路由
      if (!permissionStore.isDynamicAddedRoute) {
        // 获取用户权限并生成动态路由
        const routes = await permissionStore.buildRoutesAction();
        
        // 添加动态路由
        routes.forEach(route => {
          router.addRoute(route);
        });
        
        // 标记动态路由已加载
        permissionStore.setDynamicAddedRoute(true);
        
        // 重定向到目标路由
        next({ ...to, replace: true });
      } else {
        next();
      }
    } else {
      // 未登录，重定向到登录页
      if (to.meta.requiresAuth !== false) {
        next({ name: 'Login', query: { redirect: to.fullPath } });
      } else {
        next();
      }
    }
  });
}
```

### 路由守卫

项目使用多个路由守卫处理不同的路由逻辑：

```typescript
// src/router/guard/index.ts
import { Router } from 'vue-router';
import { createPermissionGuard } from './permission';
import { createPageLoadingGuard } from './page-loading';
import { createPageTitleGuard } from './page-title';
import { createProgressGuard } from './progress';

export function setupRouterGuard(router: Router) {
  // 页面加载状态守卫
  createPageLoadingGuard(router);
  // 权限守卫
  createPermissionGuard(router);
  // 页面标题守卫
  createPageTitleGuard(router);
  // 进度条守卫
  createProgressGuard(router);
}
```

### 路由跳转

```typescript
// 声明式导航
<router-link to="/dashboard">仪表盘</router-link>

// 编程式导航
import { useRouter } from 'vue-router';

const router = useRouter();

// 基本跳转
router.push('/dashboard');

// 命名路由
router.push({ name: 'Dashboard' });

// 带参数
router.push({ path: '/user', query: { id: '123' } });

// 带路径参数
router.push({ name: 'UserDetail', params: { id: '123' } });

// 替换当前路由
router.replace('/dashboard');

// 前进/后退
router.go(-1); // 后退一步
router.go(1);  // 前进一步
```

### 路由参数获取

```typescript
import { useRoute } from 'vue-router';

const route = useRoute();

// 获取查询参数
const userId = route.query.id;

// 获取路径参数
const userId = route.params.id;

// 获取当前路由名称
const routeName = route.name;

// 获取当前路由元信息
const meta = route.meta;
```

### 最佳实践

1. **模块化管理**：按功能模块拆分路由配置，便于维护
2. **懒加载**：使用动态导入实现路由懒加载，提高首屏加载速度
3. **权限控制**：在路由元信息中配置所需权限，实现精细化的权限控制
4. **路由命名**：使用有意义的名称命名路由，便于编程式导航
5. **路由缓存**：合理使用 `keepAlive` 配置，优化用户体验
6. **国际化**：路由标题使用国际化键，支持多语言
7. **路由守卫**：使用路由守卫处理通用逻辑，如权限检查、页面标题设置等

## 技术栈

### 核心框架

- **前端框架**：Vue 3.5.x - 渐进式 JavaScript 框架
- **构建工具**：Vite 5.4.x - 下一代前端构建工具
- **UI 框架**：Naive UI 2.41.x - 基于 Vue 3 的组件库
- **类型检查**：TypeScript 5.8.x - JavaScript 的超集，提供静态类型检查

### 状态管理与路由

- **状态管理**：Pinia 2.3.x - Vue 的官方状态管理库
  - **持久化**：pinia-plugin-persistedstate 2.4.x - Pinia 状态持久化插件
- **路由**：Vue Router 4.5.x - Vue.js 的官方路由

### 网络请求

- **HTTP 客户端**：
  - Axios 1.7.x - 基于 Promise 的 HTTP 客户端
  - Alova 3.2.x - 轻量级请求策略库

## API 请求

项目使用 [Axios](https://axios-http.com/) 和 [Alova](https://alova.js.org/) 发送 HTTP 请求，提供了完善的请求封装、拦截器和错误处理机制。

### 目录结构

```
src/api/
├── index.ts                # API 入口文件
├── axios/                  # Axios 配置
│   ├── index.ts            # Axios 实例创建
│   ├── transform.ts        # 数据转换
│   ├── checkStatus.ts      # 状态码检查
│   └── Axios.ts            # Axios 封装类
├── alova/                  # Alova 配置
│   ├── index.ts            # Alova 实例创建
│   ├── adapter.ts          # 请求适配器
│   └── middleware.ts       # 中间件
└── modules/                # API 模块
    ├── user.ts             # 用户相关 API
    ├── system.ts           # 系统相关 API
    └── ...                 # 其他模块 API
```

### Axios 配置

项目对 Axios 进行了封装，提供了统一的请求方法和拦截器：

```typescript
// src/api/axios/index.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { AxiosTransform } from './transform';
import { checkStatus } from './checkStatus';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from '@/hooks/useMessage';

// 创建 axios 实例
const axiosInstance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: { 'Content-Type': 'application/json;charset=UTF-8' },
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    const token = userStore.token;
    
    // 添加 token 到请求头
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 处理响应数据
    const { data } = response;
    
    // 根据业务状态码处理响应
    if (data.code !== 0) {
      const { createMessage } = useMessage();
      createMessage.error(data.message || '请求失败');
      
      // 特定状态码处理，如 401 未授权
      if (data.code === 401) {
        const userStore = useUserStore();
        userStore.logout();
      }
      
      return Promise.reject(new Error(data.message || '请求失败'));
    }
    
    return data.data;
  },
  (error) => {
    const { response } = error;
    
    // 处理 HTTP 错误状态码
    if (response) {
      checkStatus(response.status);
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;
```

### Alova 配置

项目同时使用 Alova 作为轻量级请求策略库，提供更好的请求缓存和状态管理：

```typescript
// src/api/alova/index.ts
import { createAlova } from 'alova';
import { VueHook } from 'alova/vue';
import { axiosRequestAdapter } from '@alova/adapter-axios';
import axiosInstance from '../axios';

// 创建 alova 实例
const alovaInstance = createAlova({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  statesHook: VueHook,
  requestAdapter: axiosRequestAdapter({
    axios: axiosInstance,
  }),
  // 全局请求拦截器
  beforeRequest: (method) => {
    // 可以在这里修改请求配置
  },
  // 全局响应拦截器
  responded: {
    // 请求成功拦截器
    onSuccess: async (response, method) => {
      const data = response.data;
      
      // 处理响应数据
      if (data.code !== 0) {
        return Promise.reject(new Error(data.message || '请求失败'));
      }
      
      return data.data;
    },
    // 请求失败拦截器
    onError: (error, method) => {
      // 处理请求错误
      return Promise.reject(error);
    },
  },
});

export default alovaInstance;
```

### API 模块化

项目按功能模块组织 API 请求：

```typescript
// src/api/modules/user.ts
import axiosInstance from '../axios';
import alovaInstance from '../alova';
import { Method } from 'alova';

// 接口返回类型定义
export interface LoginParams {
  username: string;
  password: string;
}

export interface LoginResult {
  token: string;
  userInfo: UserInfo;
}

export interface UserInfo {
  id: string;
  username: string;
  realName: string;
  avatar: string;
  roles: string[];
  permissions: string[];
}

// Axios 方式
export const userApi = {
  // 用户登录
  login: (params: LoginParams) => {
    return axiosInstance.post<LoginResult>('/auth/login', params);
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return axiosInstance.get<UserInfo>('/user/info');
  },
  
  // 用户登出
  logout: () => {
    return axiosInstance.post('/auth/logout');
  },
};

// Alova 方式
export const useUserApi = {
  // 用户登录
  login: (params: LoginParams) => {
    return alovaInstance.Post<LoginResult>('/auth/login', params);
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return alovaInstance.Get<UserInfo>('/user/info', {
      // 启用缓存
      localCache: {
        expire: 5 * 60 * 1000, // 5分钟缓存
      },
    });
  },
  
  // 用户登出
  logout: () => {
    return alovaInstance.Post('/auth/logout');
  },
};
```

### 使用方式

#### Axios 方式

```typescript
import { userApi } from '@/api/modules/user';
import { useMessage } from '@/hooks/useMessage';

const { createMessage } = useMessage();

// 登录
async function handleLogin() {
  try {
    const result = await userApi.login({
      username: 'admin',
      password: '123456',
    });
    
    // 处理登录成功
    createMessage.success('登录成功');
    
    return result;
  } catch (error) {
    // 处理登录失败
    console.error('登录失败:', error);
  }
}
```

#### Alova 方式

```vue
<template>
  <div>
    <n-button :loading="loading" @click="handleLogin">登录</n-button>
  </div>
</template>

<script setup lang="ts">
import { useUserApi } from '@/api/modules/user';
import { useRequest } from 'alova';
import { useMessage } from '@/hooks/useMessage';

const { createMessage } = useMessage();

// 使用 useRequest 钩子
const {
  loading,
  data,
  send: login,
  onSuccess,
  onError,
} = useRequest(
  (username, password) => useUserApi.login({ username, password }),
  {
    immediate: false, // 不立即发送请求
  }
);

// 登录成功回调
onSuccess((result) => {
  createMessage.success('登录成功');
});

// 登录失败回调
onError((error) => {
  console.error('登录失败:', error);
});

// 登录处理函数
async function handleLogin() {
  await login('admin', '123456');
}
</script>
```

### 请求取消

#### Axios 方式

```typescript
import axios, { AxiosRequestConfig } from 'axios';
import axiosInstance from '@/api/axios';

// 创建取消令牌
const controller = new AbortController();

// 发送请求
axiosInstance.get('/api/data', {
  signal: controller.signal,
});

// 取消请求
controller.abort();
```

#### Alova 方式

```typescript
import { useUserApi } from '@/api/modules/user';
import { useRequest } from 'alova';

// 使用 useRequest 钩子
const {
  loading,
  data,
  send: getUserInfo,
  abort, // 取消请求方法
} = useRequest(() => useUserApi.getUserInfo());

// 发送请求
getUserInfo();

// 取消请求
abort();
```

### 错误处理

项目提供了统一的错误处理机制：

```typescript
// src/api/axios/checkStatus.ts
import { useMessage } from '@/hooks/useMessage';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';

const { createMessage } = useMessage();

export function checkStatus(status: number): void {
  switch (status) {
    case 400:
      createMessage.error('请求错误');
      break;
    case 401:
      createMessage.error('未授权，请重新登录');
      // 退出登录并重定向到登录页
      const userStore = useUserStore();
      userStore.logout();
      break;
    case 403:
      createMessage.error('拒绝访问');
      break;
    case 404:
      createMessage.error('请求地址不存在');
      break;
    case 500:
      createMessage.error('服务器错误');
      break;
    default:
      createMessage.error(`请求失败: ${status}`);
  }
}
```

### Mock 数据

项目支持使用 [Mock.js](http://mockjs.com/) 进行接口模拟：

```typescript
// mock/modules/user.ts
import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError } from '../_util';

const userMock: MockMethod[] = [
  {
    url: '/api/auth/login',
    method: 'post',
    response: ({ body }) => {
      const { username, password } = body;
      
      if (username === 'admin' && password === '123456') {
        return resultSuccess({
          token: 'mock-token',
          userInfo: {
            id: '1',
            username: 'admin',
            realName: '管理员',
            avatar: 'https://example.com/avatar.jpg',
            roles: ['admin'],
            permissions: ['*'],
          },
        });
      }
      
      return resultError('用户名或密码错误');
    },
  },
];

export default userMock;
```

### 最佳实践

1. **模块化管理**：按功能模块组织 API 请求，便于维护
2. **类型定义**：为请求参数和响应数据定义 TypeScript 接口，提供类型安全
3. **统一处理**：使用拦截器统一处理请求头、响应数据和错误信息
4. **缓存策略**：合理使用 Alova 的缓存功能，减少重复请求
5. **取消请求**：在组件卸载或路由切换时取消未完成的请求，避免内存泄漏
6. **错误处理**：提供友好的错误提示，并根据错误类型执行相应操作
7. **Mock 数据**：开发阶段使用 Mock 数据，减少对后端的依赖

### 样式与 UI

- **CSS 预处理器**：SCSS 1.83.x - CSS 扩展语言
- **CSS 框架**：Tailwind CSS 3.4.x - 实用优先的 CSS 框架
- **图标库**：
  - @vicons/antd 0.10.x - Ant Design 图标
  - @vicons/ionicons5 0.10.x - Ionicons 5 图标
  - @vicons/material 0.13.x - Material Design 图标

### 工具库

- **工具函数库**：
  - lodash-es 4.17.x - 现代化的 JavaScript 实用工具库
  - @vueuse/core 10.11.x - Vue 组合式 API 工具集
- **日期处理**：
  - date-fns 2.30.x - 现代 JavaScript 日期工具库
  - dayjs 1.11.x - 轻量级日期处理库
- **图表库**：ECharts 5.6.x - 功能丰富的交互式图表库

### 功能组件

- **富文本编辑器**：@vueup/vue-quill 1.0.0-beta.8 - Vue 3 的 Quill 编辑器
- **图片裁剪**：cropperjs 1.6.x - 图片裁剪工具
- **二维码**：qrcode 1.5.x - 二维码生成库
- **表格导出**：xlsx 0.18.x - Excel 文件处理库
- **虚拟滚动**：vue-virtual-scroll-list 2.3.x - 虚拟滚动列表组件
- **微前端**：wujie-vue3 1.0.x - 微前端框架

### 开发工具

- **代码规范**：
  - ESLint 8.57.x - JavaScript 代码检查工具
  - Prettier 3.4.x - 代码格式化工具
  - Stylelint 16.12.x - CSS 代码检查工具
- **Git 工作流**：
  - simple-git-hooks 2.11.x - Git 钩子管理
  - lint-staged 11.2.x - 对暂存的 Git 文件运行 linters
  - commitizen 4.3.x - 规范化的提交消息
- **构建优化**：
  - rollup-plugin-visualizer 5.14.x - 打包分析工具
  - vite-plugin-compression 0.5.x - 资源压缩插件

## 开发环境

### 基础环境

1. **Node.js**：^18 || >=20
   - 项目要求 Node.js 18.x 或 20.x 以上版本
   - 推荐使用 Node.js 20.x LTS 版本以获得更好的性能和稳定性

2. **包管理器**：pnpm
   - 使用 pnpm 进行依赖管理，速度更快，磁盘空间占用更少
   - 安装命令：`npm install -g pnpm@latest`
   - 项目初始化：`pnpm install`

### IDE 配置

1. **推荐 IDE**：Visual Studio Code
   - **必备插件**：
     - Vue Language Features (Volar) - Vue 3 语法支持
     - TypeScript Vue Plugin (Volar) - TypeScript 支持
     - ESLint - 代码检查
     - Prettier - Code formatter - 代码格式化
     - Stylelint - CSS 代码检查
     - Tailwind CSS IntelliSense - Tailwind CSS 智能提示
     - DotENV - 环境变量文件高亮
     - i18n Ally - 国际化支持
   
   - **工作区设置**：项目包含 `.vscode/settings.json` 文件，自动配置编辑器以符合项目规范
   - **代码片段**：项目包含 `.vscode/vue3.code-snippets` 文件，提供 Vue 3 组件快速创建模板

### 开发服务器

1. **开发服务器**：Vite
   - 默认端口：8008（可在 `.env.development` 中修改 `VITE_PORT`）
   - 启动命令：`pnpm dev` 或 `pnpm serve`
   - 自动热更新
   - API 代理配置（在 `.env.development` 中的 `VITE_PROXY` 配置）

### 环境配置

项目使用多环境配置文件管理不同环境的变量：

1. **`.env`**：所有环境通用的环境变量
   - `VITE_GLOB_APP_TITLE`：应用标题
   - `VITE_GLOB_APP_SHORT_NAME`：应用简称

2. **`.env.development`**：开发环境特定的环境变量
   - 开发服务器配置
   - API 代理设置
   - Mock 数据开关
   - 调试工具配置

3. **`.env.production`**：生产环境特定的环境变量
   - 生产 API 地址
   - 构建优化选项
   - 资源路径配置

4. **`.env.test`**：测试环境特定的环境变量

### 常用命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 构建测试版本
pnpm build:test

# 代码检查和格式化
pnpm lint

# 预览构建结果
pnpm preview

# 生成样式文件
pnpm generate:styles

# 更新图标字体
pnpm update:iconfont
```

## 项目结构

```
├── build/                # 构建相关配置
│   ├── constant.ts         # 构建常量
│   ├── getConfigFileName.ts # 配置文件名获取
│   ├── script/             # 构建脚本
│   ├── utils.ts            # 构建工具函数
│   └── vite/              # Vite 相关配置
│       ├── plugin/        # Vite 插件配置
│       └── proxy.ts       # API 代理配置
├── mock/                 # 模拟数据
│   ├── common/            # 通用模拟数据
│   ├── dashboard/         # 仪表盘模拟数据
│   ├── enums/             # 枚举模拟数据
│   ├── region/            # 区域模拟数据
│   ├── search/            # 搜索模拟数据
│   ├── select/            # 选择器模拟数据
│   ├── system/            # 系统模拟数据
│   ├── table/             # 表格模拟数据
│   ├── user/              # 用户模拟数据
│   └── util.ts            # 模拟数据工具函数
├── public/               # 静态资源
│   └── favicon.ico        # 网站图标
├── scripts                 # 项目脚本
│   ├── generateStyles.config.js # 样式生成配置
│   ├── generateStyles.js   # 样式生成脚本
│   ├── updateIconfont.mjs  # 图标字体更新脚本
│   └── verifyCommit.mjs    # Git 提交验证脚本
├── src/                  # 源代码
│   ├── api/              # API 接口
│   │   ├── addon/         # 插件相关接口
│   │   ├── admin/         # 管理员相关接口
│   │   ├── agent/         # 代理相关接口
│   │   ├── common/        # 通用接口
│   │   ├── dashboard/     # 仪表盘接口
│   │   ├── decoration/    # 装饰相关接口
│   │   ├── make/          # 制作相关接口
│   │   ├── mall/          # 商城相关接口
│   │   ├── region/        # 区域相关接口
│   │   ├── search/        # 搜索相关接口
│   │   ├── select/        # 选择器相关接口
│   │   ├── system/        # 系统相关接口
│   │   └── table/         # 表格相关接口
│   ├── assets/           # 静态资源
│   │   ├── fonts/         # 字体文件
│   │   ├── icons/         # 图标文件
│   │   └── images/        # 图片资源
│   ├── components/       # 全局组件
│   │   ├── AdvancedTable/ # 高级表格组件
│   │   ├── Form/          # 表单组件
│   │   ├── Icon/          # 图标组件
│   │   ├── Modal/         # 模态框组件
│   │   ├── Table/         # 表格组件
│   │   ├── Upload/        # 上传组件
│   │   └── ...            # 其他组件
│   ├── composables/      # 组合式函数
│   │   ├── useNotification.ts # 通知组合式函数
│   │   └── useThemeConfig.ts  # 主题配置组合式函数
│   ├── directives/       # 全局指令
│   │   ├── clickOutside.ts # 点击外部指令
│   │   ├── debounce.ts     # 防抖指令
│   │   ├── loading.ts      # 加载指令
│   │   ├── permission.ts   # 权限指令
│   │   ├── scrollBar.ts    # 滚动条指令
│   │   └── throttle.ts     # 节流指令
│   ├── enums/            # 枚举定义
│   │   ├── appEnums.ts     # 应用枚举
│   │   ├── cacheEnums.ts   # 缓存枚举
│   │   ├── httpEnum.ts     # HTTP 枚举
│   │   ├── pageEnum.ts     # 页面枚举
│   │   └── ...             # 其他枚举
│   ├── hooks/            # 钩子函数
│   │   ├── core/           # 核心钩子
│   │   ├── event/          # 事件钩子
│   │   ├── setting/        # 设置钩子
│   │   ├── web/            # Web 相关钩子
│   │   └── ...             # 其他钩子
│   ├── layout/           # 布局组件
│   │   ├── components/     # 布局子组件
│   │   ├── index.vue       # 主布局
│   │   └── parentLayout.vue # 父级布局
│   ├── locales/          # 国际化
│   │   ├── helper.ts       # 国际化辅助函数
│   │   ├── index.ts        # 国际化入口
│   │   ├── lang/           # 语言包
│   │   └── useLocale.ts    # 国际化组合式函数
│   ├── plugins/          # 插件
│   │   ├── customComponents.ts # 自定义组件注册
│   │   ├── directives.ts   # 指令注册
│   │   ├── globalMethods.ts # 全局方法
│   │   ├── i18n.ts         # 国际化插件
│   │   ├── index.ts        # 插件入口
│   │   ├── naive.ts        # Naive UI 插件
│   │   └── naiveDiscreteApi.ts # Naive UI 离散 API
│   ├── router/           # 路由配置
│   │   ├── base/           # 基础路由
│   │   ├── guard/          # 路由守卫
│   │   ├── helper/         # 路由辅助函数
│   │   ├── modules/        # 路由模块
│   │   └── index.ts        # 路由入口
│   ├── settings/         # 全局设置
│   │   ├── animateSetting.ts # 动画配置
│   │   ├── componentSetting.ts # 组件配置
│   │   ├── designSetting.ts # 设计配置
│   │   ├── localeSetting.ts # 国际化配置
│   │   └── projectSetting.ts # 项目配置
│   ├── store/            # 状态管理
│   │   ├── modules/        # 状态模块
│   │   └── index.ts        # 状态管理入口
│   ├── styles/           # 全局样式
│   │   ├── common.scss     # 通用样式
│   │   ├── generated/      # 生成的样式
│   │   ├── iconfont.scss   # 图标字体样式
│   │   ├── index.scss      # 样式入口
│   │   ├── tailwind.css    # Tailwind CSS
│   │   ├── transition/     # 过渡动画样式
│   │   └── var.scss        # 变量定义
│   ├── utils/            # 工具函数
│   │   ├── file/           # 文件处理工具
│   │   ├── function/       # 功能函数
│   │   ├── helper/         # 辅助函数
│   │   ├── http/           # HTTP 请求工具
│   │   ├── is/             # 类型判断工具
│   │   ├── lib/            # 第三方库封装
│   │   └── ...             # 其他工具函数
│   ├── views/            # 页面
│   │   ├── dashboard/      # 仪表盘页面
│   │   ├── login/          # 登录页面
│   │   ├── system/         # 系统管理页面
│   │   └── ...             # 其他页面
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── types/                # 类型定义
│   ├── alova.d.ts          # Alova 类型
│   ├── config.d.ts         # 配置类型
│   ├── global.d.ts         # 全局类型
│   ├── images.d.ts         # 图片类型
│   ├── index.d.ts          # 类型入口
│   ├── modules.d.ts        # 模块类型
│   └── utils.d.ts          # 工具类型
├── .editorconfig         # 编辑器配置
├── .env                  # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── .env.test             # 测试环境变量
├── .eslintrc.cjs         # ESLint 配置
├── .prettierrc.cjs       # Prettier 配置
├── .stylelintrc.cjs      # Stylelint 配置
├── index.html            # HTML 模板
├── package.json          # 项目依赖
├── postcss.config.cjs    # PostCSS 配置
├── tailwind.config.cjs   # Tailwind 配置
├── tsconfig.json         # TypeScript 配置
└── vite.config.ts        # Vite 配置
```

## 编码规范

### JavaScript/TypeScript 规范

项目使用 ESLint 和 Prettier 进行代码规范和格式化。

#### ESLint 配置要点

```javascript
// .eslintrc.cjs
module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2020,
    sourceType: 'module',
    jsxPragma: 'React',
    ecmaFeatures: {
      jsx: true,
    },
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  plugins: ['prettier'],
  rules: {
    // Prettier 集成
    'prettier/prettier': ['error', {}, { usePrettierrc: true }],
    // Vue 相关规则
    'vue/script-setup-uses-vars': 'error',
    'vue/multi-word-component-names': 'off',
    'vue/attributes-order': 'off',
    'vue/one-component-per-file': 'off',
    'vue/html-closing-bracket-newline': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/attribute-hyphenation': 'off',
    'vue/require-default-prop': 'off',
    'vue/require-explicit-emits': 'off',
    'vue/html-self-closing': [
      'error',
      {
        html: {
          void: 'always',
          normal: 'never',
          component: 'always',
        },
        svg: 'always',
        math: 'always',
      },
    ],
    'vue/valid-template-root': 'off',
    'vue/no-v-html': 'off',
    
    // TypeScript 相关规则
    '@typescript-eslint/ban-ts-ignore': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/no-use-before-define': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      },
    ],
    
    // 其他规则
    'no-use-before-define': 'off',
    'space-before-function-paren': 'off',
    'no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      },
    ],
  },
}
```

#### Prettier 配置要点

```javascript
// .prettierrc.cjs
module.exports = {
  // 使用单引号而不是双引号
  singleQuote: true,
  // 每行最大宽度，超过会自动换行
  printWidth: 100,
  // 缩进的空格数
  tabWidth: 2,
  // 使用空格而不是 tab 缩进
  useTabs: false,
  // 语句末尾不添加分号
  semi: false,
  // 在对象或数组的最后一个元素后面添加逗号
  // 'none' - 不添加
  // 'es5' - 在 ES5 中有效的地方添加
  // 'all' - 尽可能添加（包括函数参数）
  trailingComma: 'all',
  // 自动识别换行符（不同操作系统）
  // 'lf' - \n
  // 'crlf' - \r\n
  // 'cr' - \r
  // 'auto' - 保持现有的行尾
  endOfLine: 'auto',
  // HTML 空格敏感度
  // 'css' - 遵循 CSS display 属性的默认值
  // 'strict' - 空格敏感
  // 'ignore' - 空格不敏感
  htmlWhitespaceSensitivity: 'ignore',
  // 对象字面量中的括号之间添加空格
  // { foo: bar } 而不是 {foo: bar}
  bracketSpacing: true,
  // HTML 标签（以及 JSX，Vue 模板）的 > 放在最后一行的末尾，而不是单独放一行
  bracketSameLine: false,
  // 箭头函数参数始终使用括号
  // (x) => x 而不是 x => x
  arrowParens: 'always',
  // Vue 文件中的 script 和 style 标签内代码是否缩进
  vueIndentScriptAndStyle: true,
  // 是否格式化嵌入的代码块（比如 markdown 文件中的代码块）
  embeddedLanguageFormatting: 'auto',
  // 针对特定文件的覆盖配置
  overrides: [
    {
      // JSON 文件特殊处理
      files: '*.json',
      options: {
        // JSON 文件不允许尾随逗号
        trailingComma: 'none',
      },
    },
    {
      // Markdown 文件特殊处理
      files: '*.md',
      options: {
        // Markdown 文本换行方式
        // 'always' - 超过 printWidth 时换行
        // 'never' - 不换行
        // 'preserve' - 保持原样
        proseWrap: 'preserve',
      },
    },
  ],
}
```

### Vue 组件规范

1. **组件命名**：
   - 使用 PascalCase 命名组件文件和组件名
   - 基础组件以 `Pro` 或特定前缀开头（如 `ProTable`）

2. **组件结构**：
   - 使用 `<script setup>` 语法
   - 使用 TypeScript 定义 props 和 emits
   - 样式使用 SCSS 并添加 scoped 属性

3. **组件模板**：

```vue
<template>
  <div>
    <!-- 内容 -->
  </div>
</template>

<script setup>
// 导入

// 定义 props 和 emits

// 状态和逻辑

// 生命周期钩子

// 方法
</script>

<style lang="scss" scoped>
/* 样式 */
</style>
```

### CSS/SCSS 规范

项目使用 Stylelint 进行样式代码规范检查，同时集成了 Tailwind CSS。

#### Stylelint 配置要点

```javascript
// .stylelintrc.cjs
module.exports = {
  root: true,
  extends: [
    // stylelint-config-standard 替换成了更宽松的 stylelint-config-recommended
    'stylelint-config-recommended',
    // stylelint-config-standard-scss 替换成了更宽松的 stylelint-config-recommended-scss
    'stylelint-config-recommended-scss',
    // Vue 单文件组件中 SCSS 的规则
    'stylelint-config-recommended-vue/scss',
    // Vue 单文件组件中 HTML 的规则
    'stylelint-config-html/vue',
    // CSS 属性的排序规则（margin、padding、border 等）
    'stylelint-config-recess-order',
  ],
  // 使用的插件
  plugins: [
    // 集成 Prettier 的规则
    'stylelint-prettier',
  ],
  // 针对不同文件的特殊配置
  overrides: [
    // 处理 .vue/html 文件中的 <style> 标签内的样式
    {
      files: ['**/*.{vue,html}'],
      // 使用 PostCSS 解析 HTML 中的样式
      customSyntax: 'postcss-html',
    },
    // 处理 .css/.scss 文件
    {
      files: ['**/*.{css,scss}'],
      // 使用 PostCSS 解析 SCSS 语法
      customSyntax: 'postcss-scss',
    },
  ],
  rules: {
    // 启用 Prettier 的规则
    'prettier/prettier': true,
    // 忽略 v-deep 伪元素
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'deep'],
      },
    ],
    // 忽略 deep 伪类
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep', 'global'],
      },
    ],

    // 注释相关规则
    // 自定义属性前不需要空行
    'custom-property-empty-line-before': 'never',

    // 关闭一些不必要的规则
    'no-empty-source': null, // 允许空的样式文件
    'comment-no-empty': null, // 允许空注释
    'no-duplicate-selectors': null, // 允许重复的选择器（在 Vue scoped 中可能需要）
    'scss/comment-no-empty': null, // 允许 SCSS 中的空注释
    'selector-class-pattern': null, // 不限制类名的命名规则
    'font-family-no-missing-generic-family-keyword': null, // 允许缺少通用字体族关键字
    'no-descending-specificity': null, // 允许特异性降序，解决 Vue scoped 样式和嵌套样式的顺序问题
    
    // 添加对 Tailwind 的支持
    'scss/at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'tailwind', // 允许 @tailwind 指令
          'apply', // 允许 @apply 指令，用于应用工具类
          'variants', // 允许 @variants 指令，用于控制状态变体
          'responsive', // 允许 @responsive 指令，用于响应式设计
          'sm', // 允许 @sm 指令，用于响应式断点
          'md', // 允许 @md 指令，用于响应式断点
          'lg', // 允许 @lg 指令，用于响应式断点
          'xl', // 允许 @xl 指令，用于响应式断点
          '2xl', // 允许 @2xl 指令，用于响应式断点
          'layer', // 允许 @layer 指令，用于样式分层
        ],
      },
    ],
  },
}
```

#### 样式编写规范

1. **使用 SCSS 预处理器**
2. **遵循 BEM 命名规范**：Block__Element--Modifier
3. **优先使用 Tailwind CSS 工具类**
4. **CSS 属性顺序**：使用 stylelint-config-recess-order 规则
5. **避免使用 !important**
6. **使用变量管理颜色和尺寸**

### 命名规范

1. **文件和目录命名**：
   - 组件文件：PascalCase（如 `UserProfile.vue`）
   - 工具/钩子文件：camelCase（如 `useUserAuth.ts`）
   - 目录名：kebab-case（如 `user-management`）

2. **变量命名**：
   - 普通变量：camelCase（如 `userName`）
   - 常量：UPPER_SNAKE_CASE（如 `MAX_COUNT`）
   - 类/接口：PascalCase（如 `UserService`）
   - 私有属性：以下划线开头（如 `_privateVar`）

3. **CSS 类名**：
   - 使用 kebab-case（如 `user-avatar`）
   - 遵循 BEM 命名规范

## Git 工作流

### 分支管理

- **main/master**：主分支，保持稳定，只接受合并请求
  - 稳定的生产环境代码
  - 受保护分支，禁止直接推送
  - 只能通过合并请求（MR/PR）更新

- **develop**：开发分支，日常开发工作的基础分支
  - 最新的开发代码
  - 功能分支和修复分支合并到此
  - 用于集成测试

- **feature/xxx**：功能分支，用于开发新功能
  - 从 `develop` 分支创建
  - 命名规范：`feature/user-management`
  - 完成后合并回 `develop` 分支

- **fix/xxx**：修复分支，用于修复 bug
  - 从 `develop` 分支创建（非紧急问题）
  - 从 `main` 分支创建（生产环境紧急问题）
  - 命名规范：`fix/login-validation`

- **release/xxx**：发布分支，用于版本发布
  - 从 `develop` 分支创建
  - 命名规范：`release/1.2.0`
  - 只进行版本相关修改和文档更新
  - 完成后同时合并到 `main` 和 `develop`

- **hotfix/xxx**：热修复分支，用于紧急修复生产环境问题
  - 从 `main` 分支创建
  - 用于修复生产环境紧急问题
  - 完成后同时合并到 `main` 和 `develop`

### 提交规范

项目使用 [Commitizen](http://commitizen.github.io/cz-cli/) 和 [simple-git-hooks](https://github.com/toplenboren/simple-git-hooks) 规范提交信息，确保提交历史清晰可追踪。

#### 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型（type）

- **feat**：新功能
- **fix**：修复 bug
- **docs**：文档更新
- **style**：代码风格调整（不影响功能）
- **refactor**：代码重构（既不是新增功能，也不是修复 Bug 的代码变动）
- **perf**：性能优化
- **test**：测试相关
- **build**：构建系统或外部依赖变动
- **ci**：CI 配置相关
- **chore**：其他不修改源代码与测试的变动
- **revert**：撤销之前的提交

#### 影响范围（scope）

可选字段，用于说明本次提交影响的范围：

- **core**：核心模块
- **ui**：界面相关
- **api**：API 相关
- **auth**：认证相关
- **router**：路由相关
- **store**：状态管理相关
- **utils**：工具函数相关
- **deps**：依赖相关
- **config**：配置相关
- **i18n**：国际化相关

#### 提交说明（subject）

- 简短描述本次提交的内容
- 使用现在时态（"change"，而不是 "changed" 或 "changes"）
- 首字母不要大写
- 结尾不要加句号
- 不超过 50 个字符

#### 提交示例

```
feat(auth): add login with google oauth

fix(ui): correct button alignment in mobile view

docs: update README with new build instructions

refactor(api): simplify error handling logic
```

### Git 工作流程

1. **创建分支**
   ```bash
   git checkout -b feature/new-feature develop
   ```

2. **本地开发**
   - 编写代码
   - 运行测试
   - 确保代码符合规范

3. **提交更改**
   ```bash
   git add .
   git cz  # 使用 commitizen 交互式提交
   ```

4. **同步远程更改**
   ```bash
   git pull --rebase origin develop
   ```

5. **推送分支**
   ```bash
   git push origin feature/new-feature
   ```

6. **创建合并请求**
   - 在 GitLab/GitHub 上创建合并请求
   - 指定代码审查者
   - 等待 CI 流程完成

7. **代码审查**
   - 解决审查中提出的问题
   - 获得批准后合并

### 提交前检查

项目配置了 Git 钩子，在提交前会自动运行：

1. **lint-staged**: 对暂存的文件运行 linters
   - ESLint 检查 JavaScript/TypeScript 文件
   - Prettier 格式化代码
   - Stylelint 检查样式文件

2. **commit-msg**: 检查提交信息是否符合规范

这些检查确保所有提交的代码都符合项目规范。

## 构建与部署

### 开发环境

```bash
pnpm dev  # 启动开发服务器
```

### 测试环境构建

```bash
pnpm build:test  # 构建测试环境
```

### 生产环境构建

```bash
pnpm build  # 构建生产环境
```

### 代码检查

```bash
pnpm lint  # 运行所有代码检查
pnpm lint:eslint  # 仅运行 ESLint
pnpm lint:prettier  # 仅运行 Prettier
pnpm lint:stylelint  # 仅运行 Stylelint
```

### 环境变量配置

项目使用 `.env` 文件系列管理不同环境的配置：

- `.env`：所有环境的公共配置
- `.env.development`：开发环境配置
- `.env.test`：测试环境配置
- `.env.production`：生产环境配置

环境变量命名规范：

- 所有环境变量必须以 `VITE_` 开头才能在代码中通过 `import.meta.env.VITE_XXX` 访问
- 常用环境变量：
  - `VITE_PORT`：开发服务器端口
  - `VITE_PUBLIC_PATH`：部署路径
  - `VITE_DROP_CONSOLE`：是否移除 console 语句
  - `VITE_SERVER_BASEURL`：API 服务器地址
  - `VITE_USE_MOCK`：是否启用 Mock 数据
  - `VITE_GLOB_APP_TITLE`：应用标题

### 构建优化

项目在 `vite.config.ts` 中配置了多项构建优化：

- 使用 esbuild 进行代码压缩
- 配置 Rollup 手动分块，将常用库（Vue、Naive UI、Lodash 等）单独打包
- 启用构建并行处理
- 配置 CSS 代码分割
- 设置 chunk 大小警告限制

## 国际化

项目使用 [vue-i18n](https://vue-i18n.intlify.dev/) 实现国际化，支持中文和英文，并可根据需求扩展其他语言。

### 目录结构

```
src/locales/
├── index.ts          # 入口文件，配置 i18n 实例
├── lang/             # 语言文件目录
│   ├── en/           # 英文语言包目录
│   │   ├── common.ts # 通用翻译
│   │   ├── menu.ts   # 菜单翻译
│   │   ├── routes.ts # 路由翻译
│   │   ├── system.ts # 系统翻译
│   │   └── index.ts  # 英文语言包入口
│   ├── zh-CN/        # 中文语言包目录
│   │   ├── common.ts # 通用翻译
│   │   ├── menu.ts   # 菜单翻译
│   │   ├── routes.ts # 路由翻译
│   │   ├── system.ts # 系统翻译
│   │   └── index.ts  # 中文语言包入口
│   └── index.ts      # 语言包汇总
└── useLocale.ts      # 国际化 hook，提供切换语言等功能
```

### 语言包结构

语言包采用模块化管理，按功能区分不同的翻译文件：

```typescript
// src/locales/lang/zh-CN/common.ts
export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    // 其他通用翻译
  },
};

// src/locales/lang/zh-CN/index.ts
import common from './common';
import menu from './menu';
import routes from './routes';
import system from './system';

export default {
  ...common,
  ...menu,
  ...routes,
  ...system,
};
```

### 使用方式

#### 在模板中使用

```vue
<template>
  <div>{{ t('common.confirm') }}</div>
  <n-button>{{ t('common.save') }}</n-button>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>
```

#### 在 JS/TS 中使用

```typescript
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
console.log(t('common.confirm')); // 输出：确认
```

#### 切换语言

项目提供了 `useLocale` hook 用于切换语言：

```typescript
import { useLocale } from '@/locales/useLocale';

const { changeLocale, getLocale } = useLocale();

// 获取当前语言
const currentLocale = getLocale();

// 切换到英文
changeLocale('en');

// 切换到中文
changeLocale('zh-CN');
```

### 日期和数字格式化

vue-i18n 提供了日期和数字的国际化格式化功能：

```vue
<template>
  <div>{{ d(new Date(), 'long') }}</div>
  <div>{{ n(1234.56, 'currency') }}</div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { d, n } = useI18n();
</script>
```

### 复数和命名插值

```vue
<template>
  <!-- 复数 -->
  <div>{{ tc('cart.items', 2) }}</div>
  
  <!-- 命名插值 -->
  <div>{{ t('welcome', { name: userName }) }}</div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t, tc } = useI18n();
const userName = 'John';
</script>
```

### 添加新语言

1. 在 `src/locales/lang/` 目录下创建新的语言目录，如 `ja/`（日语）
2. 复制现有语言包的结构，创建对应的翻译文件
3. 在 `src/locales/lang/index.ts` 中导入并注册新语言
4. 在语言选择器中添加新语言选项

### 最佳实践

1. **使用层级结构**：使用点号分隔的层级结构组织翻译键，如 `user.profile.title`
2. **模块化管理**：按功能模块拆分翻译文件，便于维护
3. **提取公共文本**：将重复使用的文本提取到 `common.ts` 中
4. **使用变量**：对于包含动态内容的文本，使用命名插值而不是字符串拼接
5. **避免硬编码**：不要在代码中硬编码文本，始终使用翻译键
6. **保持键名一致**：在不同语言包中保持相同的键名结构
7. **使用 VSCode 插件**：推荐使用 i18n Ally 插件辅助开发

## 状态管理

项目使用 [Pinia](https://pinia.vuejs.org/) 进行状态管理，提供了模块化的状态管理方案和状态持久化功能。

### 目录结构

```
src/store/
├── index.ts                # Store 入口文件
├── modules/                # Store 模块
│   ├── app.ts              # 应用配置 Store
│   ├── user.ts             # 用户信息 Store
│   ├── permission.ts       # 权限 Store
│   ├── tabView.ts          # 标签页 Store
│   └── ...                 # 其他模块 Store
└── helper.ts               # Store 辅助函数
```

### Store 定义

项目使用 Pinia 的 Options API 风格定义 Store：

```typescript
// src/store/modules/user.ts
import { defineStore } from 'pinia';
import { userApi } from '@/api/modules/user';
import { useMessage } from '@/hooks/useMessage';
import { router } from '@/router';

interface UserState {
  token: string;
  userInfo: Nullable<UserInfo>;
  roles: string[];
  permissions: string[];
}

export const useUserStore = defineStore({
  id: 'user',
  
  // 状态
  state: (): UserState => ({
    token: '',
    userInfo: null,
    roles: [],
    permissions: [],
  }),
  
  // 计算属性
  getters: {
    isLoggedIn(): boolean {
      return !!this.token;
    },
    
    hasRole(state) {
      return (role: string) => state.roles.includes(role);
    },
    
    hasPermission(state) {
      return (permission: string) => state.permissions.includes(permission);
    },
  },
  
  // 操作方法
  actions: {
    // 设置 Token
    setToken(token: string) {
      this.token = token;
    },
    
    // 设置用户信息
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo;
      this.roles = userInfo?.roles || [];
      this.permissions = userInfo?.permissions || [];
    },
    
    // 登录
    async login(params: LoginParams) {
      try {
        const { createMessage } = useMessage();
        const result = await userApi.login(params);
        
        this.setToken(result.token);
        this.setUserInfo(result.userInfo);
        
        createMessage.success('登录成功');
        
        // 跳转到首页或指定页面
        const redirect = router.currentRoute.value.query.redirect as string;
        router.push(redirect || '/');
        
        return result;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const userInfo = await userApi.getUserInfo();
        this.setUserInfo(userInfo);
        return userInfo;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    
    // 登出
    async logout() {
      try {
        await userApi.logout();
      } catch (error) {
        console.error('登出失败:', error);
      } finally {
        this.setToken('');
        this.setUserInfo(null as any);
        router.push('/login');
      }
    },
  },
  
  // 持久化配置
  persist: {
    key: 'user',
    storage: localStorage,
    paths: ['token'],
  },
});
```

### 使用 Store

在组件中使用 Store：

```vue
<template>
  <div>
    <div v-if="userStore.isLoggedIn">
      欢迎，{{ userStore.userInfo?.realName }}
      <n-button @click="handleLogout">退出登录</n-button>
    </div>
    <div v-else>
      <n-button @click="router.push('/login')">登录</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';

const userStore = useUserStore();
const router = useRouter();

// 退出登录
async function handleLogout() {
  await userStore.logout();
}
</script>
```

### 组合式 API 风格

项目也支持使用 Pinia 的组合式 API 风格定义 Store：

```typescript
// src/store/modules/counter.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useCounterStore = defineStore('counter', () => {
  // 状态
  const count = ref(0);
  
  // 计算属性
  const doubleCount = computed(() => count.value * 2);
  
  // 操作方法
  function increment() {
    count.value++;
  }
  
  function decrement() {
    count.value--;
  }
  
  return {
    count,
    doubleCount,
    increment,
    decrement,
  };
}, {
  // 持久化配置
  persist: true,
});
```

### 状态持久化

项目使用 [pinia-plugin-persistedstate](https://github.com/prazdevs/pinia-plugin-persistedstate) 实现状态持久化：

```typescript
// src/store/index.ts
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

export default pinia;
```

在 Store 中配置持久化：

```typescript
// 基本配置，持久化所有状态
persist: true,

// 高级配置，指定持久化的字段和存储方式
persist: {
  key: 'app-settings', // 存储的键名
  storage: localStorage, // 存储方式，可选 localStorage 或 sessionStorage
  paths: ['theme', 'language'], // 需要持久化的字段，不指定则持久化所有字段
  beforeRestore: (context) => {
    // 恢复前的钩子
    console.log('即将恢复状态', context);
  },
  afterRestore: (context) => {
    // 恢复后的钩子
    console.log('状态已恢复', context);
  },
},
```

### Store 之间的交互

在一个 Store 中使用另一个 Store：

```typescript
// src/store/modules/permission.ts
import { defineStore } from 'pinia';
import { useUserStore } from './user';

export const usePermissionStore = defineStore({
  id: 'permission',
  
  state: () => ({
    routes: [],
    isDynamicAddedRoute: false,
  }),
  
  actions: {
    async buildRoutesAction() {
      const userStore = useUserStore();
      
      // 根据用户权限生成路由
      const routes = await generateRoutes(userStore.roles, userStore.permissions);
      
      this.routes = routes;
      this.isDynamicAddedRoute = true;
      
      return routes;
    },
    
    setDynamicAddedRoute(value: boolean) {
      this.isDynamicAddedRoute = value;
    },
  },
});
```

### 订阅状态变化

```typescript
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

// 订阅整个 Store 的变化
const unsubscribe = userStore.$subscribe((mutation, state) => {
  console.log('Store 发生变化:', mutation, state);
});

// 订阅特定 action
const unsubscribeAction = userStore.$onAction(({ name, args, after, onError }) => {
  console.log(`Action ${name} 被调用，参数:`, args);
  
  after((result) => {
    console.log(`Action ${name} 执行完成，结果:`, result);
  });
  
  onError((error) => {
    console.error(`Action ${name} 执行出错:`, error);
  });
});

// 取消订阅
onBeforeUnmount(() => {
  unsubscribe();
  unsubscribeAction();
});
```

### 重置 Store

```typescript
const userStore = useUserStore();

// 重置 Store 到初始状态
userStore.$reset();
```

### 最佳实践

1. **模块化管理**：按功能模块拆分 Store，每个模块负责特定的功能
2. **类型安全**：使用 TypeScript 定义 Store 的状态类型，提供类型安全
3. **状态持久化**：合理配置持久化，只持久化必要的状态，避免敏感信息泄露
4. **计算属性**：使用 getters 派生状态，避免冗余状态
5. **异步操作**：在 actions 中处理异步操作，保持状态变更的可追踪性
6. **状态隔离**：避免在组件中直接修改 Store 状态，始终通过 actions 修改状态
7. **按需加载**：使用动态导入按需加载 Store，减少首屏加载时间

## 最佳实践

### 组件设计

组件是应用程序的基本构建块，良好的组件设计可以提高代码的可复用性、可维护性和可测试性。

#### 组件分类

1. **基础组件**：封装基本 UI 元素，如按钮、输入框、选择器等
2. **业务组件**：封装特定业务逻辑的组件，如用户信息卡、订单列表等
3. **布局组件**：用于页面布局的组件，如页头、页脚、侧边栏等
4. **功能组件**：提供特定功能的组件，如文件上传、图表、表单等

#### 组件设计原则

1. **单一职责**：每个组件只负责一个功能，避免过于复杂
2. **可复用性**：组件应该设计为可在多处使用，避免硬编码业务逻辑
3. **可配置性**：通过 props 提供配置选项，使组件适应不同场景
4. **可扩展性**：预留扩展点，如插槽、事件等，方便扩展功能
5. **可测试性**：组件应该易于测试，避免依赖外部状态

#### 组件通信

1. **Props 向下传递**：父组件通过 props 向子组件传递数据
2. **Events 向上传递**：子组件通过 emit 事件向父组件传递信息
3. **Provide/Inject**：跨多级组件传递数据
4. **Store**：使用 Pinia 进行全局状态管理
5. **Refs**：直接访问子组件实例（谨慎使用）

#### 组件示例

```vue
<!-- src/components/business/UserCard.vue -->
<template>
  <div class="user-card" :class="{ 'is-active': isActive }">
    <div class="user-card__avatar">
      <n-avatar
        :src="user.avatar"
        :size="size"
        :round="round"
        fallback-src="/placeholder-avatar.png"
      />
    </div>
    
    <div class="user-card__info">
      <div class="user-card__name">
        {{ user.realName }}
        <n-tag v-if="user.isVip" type="success" size="small">VIP</n-tag>
      </div>
      
      <div class="user-card__role">{{ user.roleName }}</div>
      
      <div class="user-card__extra">
        <slot name="extra"></slot>
      </div>
    </div>
    
    <div class="user-card__actions">
      <slot name="actions">
        <n-button size="small" @click="handleEdit">编辑</n-button>
        <n-button size="small" @click="$emit('view', user.id)">查看</n-button>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface UserCardProps {
  user: UserInfo;
  size?: 'small' | 'medium' | 'large';
  round?: boolean;
  active?: boolean;
}

interface UserInfo {
  id: string | number;
  realName: string;
  avatar: string;
  roleName: string;
  isVip?: boolean;
}

// 定义 props
const props = withDefaults(defineProps<UserCardProps>(), {
  size: 'medium',
  round: true,
  active: false,
});

// 定义 emits
const emit = defineEmits<{
  (e: 'edit', id: string | number): void;
  (e: 'view', id: string | number): void;
}>();

// 计算属性
const isActive = computed(() => props.active);

// 方法
function handleEdit() {
  emit('edit', props.user.id);
}

// 暴露方法给父组件
defineExpose({
  focus: () => {
    // 实现聚焦逻辑
    console.log('UserCard focused');
  },
});
</script>

<style lang="scss" scoped>
.user-card {
  display: flex;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--card-bg-color);
  transition: all 0.3s ease;
  
  &.is-active {
    background-color: var(--card-active-bg-color);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
  
  &__avatar {
    margin-right: 16px;
  }
  
  &__info {
    flex: 1;
  }
  
  &__name {
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    
    .n-tag {
      margin-left: 8px;
    }
  }
  
  &__role {
    font-size: 14px;
    color: var(--text-color-secondary);
    margin-top: 4px;
  }
  
  &__extra {
    margin-top: 8px;
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>
```

### 组件使用

#### 组件使用原则

1. **遵循单一职责原则**
   - 每个组件只负责一个功能或视图
   - 避免创建过于复杂的组件

2. **组件命名规范**
   - 使用 PascalCase 命名组件文件和组件名
   - 基础组件使用 `Base` 前缀，如 `BaseButton`
   - 单例组件使用 `The` 前缀，如 `TheHeader`
   - 紧密耦合的组件使用父组件名作为前缀，如 `TodoList`、`TodoItem`

3. **组件注册**
   - 全局组件在 `src/components/index.ts` 中注册
   - 局部组件在使用的组件中导入注册

4. **组件导入顺序**
   - 第三方库组件
   - 全局组件
   - 局部组件

#### 组件使用示例

```vue
<!-- src/views/user/UserDetail.vue -->
<template>
  <div class="user-detail">
    <page-header title="用户详情" @back="router.back()" />
    
    <n-card>
      <user-card
        :user="userInfo"
        :active="isActive"
        @edit="handleEdit"
        @view="handleView"
      >
        <template #extra>
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userInfo.orderCount }}</div>
              <div class="stat-label">订单数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(userInfo.totalSpent) }}</div>
              <div class="stat-label">消费总额</div>
            </div>
          </div>
        </template>
        
        <template #actions>
          <n-button-group>
            <n-button @click="handleContact">联系用户</n-button>
            <n-button type="primary" @click="handleEdit">编辑信息</n-button>
          </n-button-group>
        </template>
      </user-card>
      
      <n-tabs type="line" class="mt-4">
        <n-tab-pane name="orders" tab="订单记录">
          <order-list :user-id="userId" />
        </n-tab-pane>
        <n-tab-pane name="logs" tab="操作日志">
          <user-log-list :user-id="userId" />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
// 第三方库组件
import { NCard, NTabs, NTabPane, NButtonGroup, NButton } from 'naive-ui';
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';

// 全局组件
import PageHeader from '@/components/layout/PageHeader.vue';

// 局部组件
import UserCard from '@/components/business/UserCard.vue';
import OrderList from '@/components/business/OrderList.vue';
import UserLogList from '@/components/business/UserLogList.vue';

// API 和工具函数
import { userApi } from '@/api/modules/user';
import { formatMoney } from '@/utils/format';
import { useMessage } from '@/hooks/useMessage';

const route = useRoute();
const router = useRouter();
const { createMessage } = useMessage();

// 状态
const userId = route.params.id as string;
const userInfo = ref({} as any);
const isActive = ref(false);

// 方法
async function fetchUserInfo() {
  try {
    const data = await userApi.getUserDetail(userId);
    userInfo.value = data;
  } catch (error) {
    createMessage.error('获取用户信息失败');
  }
}

function handleEdit() {
  router.push(`/user/edit/${userId}`);
}

function handleView() {
  // 查看详情逻辑
}

function handleContact() {
  // 联系用户逻辑
}

// 生命周期
onMounted(() => {
  fetchUserInfo();
});
</script>

<style lang="scss" scoped>
.user-detail {
  padding: 16px;
  
  .user-stats {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 18px;
        font-weight: 500;
        color: var(--primary-color);
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--text-color-secondary);
      }
    }
  }
  
  .mt-4 {
    margin-top: 16px;
  }
}
</style>
```

#### Props 定义最佳实践

1. **使用 TypeScript 类型**：为 props 定义明确的类型

```typescript
// 推荐
const props = defineProps<{
  title: string;
  count: number;
  items: Item[];
  config?: Config;
}>();

// 或使用 withDefaults 设置默认值
const props = withDefaults(defineProps<{
  title: string;
  count: number;
  items: Item[];
  config?: Config;
}>(), {
  count: 0,
  config: () => ({
    showHeader: true,
    maxItems: 10
  })
});
```

2. **使用 Boolean 类型的 Props**：遵循 Vue 的约定，使用 `is` 或 `has` 前缀

```vue
<template>
  <user-card :is-active="true" :has-badge="false" />
</template>
```

3. **Props 验证**：使用 TypeScript 类型和 Zod 等库进行 props 验证

#### 事件处理最佳实践

1. **事件命名**：使用 kebab-case 命名事件

```typescript
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'item-click', item: Item): void;
  (e: 'status-change', status: Status): void;
}>();
```

2. **使用 v-model**：遵循 Vue 3 的 v-model 约定

```vue
<!-- 父组件 -->
<template>
  <custom-input v-model="searchText" />
</template>

<!-- 子组件 CustomInput.vue -->
<template>
  <input 
    :value="modelValue"
    @input="emit('update:modelValue', ($event.target as HTMLInputElement).value)"
  />
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();
</script>
```

#### 插槽使用最佳实践

1. **命名插槽**：为插槽提供有意义的名称

```vue
<!-- 组件定义 -->
<template>
  <div class="card">
    <div class="card-header">
      <slot name="header">
        <h3>{{ title }}</h3>
      </slot>
    </div>
    
    <div class="card-body">
      <slot></slot>
    </div>
    
    <div class="card-footer">
      <slot name="footer">
        <button @click="$emit('close')">关闭</button>
      </slot>
    </div>
  </div>
</template>

<!-- 组件使用 -->
<template>
  <base-card title="用户信息">
    <template #header>
      <div class="custom-header">自定义头部</div>
    </template>
    
    <p>这是卡片内容</p>
    
    <template #footer>
      <div class="custom-footer">
        <button @click="save">保存</button>
        <button @click="cancel">取消</button>
      </div>
    </template>
  </base-card>
</template>
```

2. **作用域插槽**：通过插槽 props 向父组件传递数据

```vue
<!-- 组件定义 -->
<template>
  <ul>
    <li v-for="item in items" :key="item.id">
      <slot :item="item" :index="index">
        {{ item.name }}
      </slot>
    </li>
  </ul>
</template>

<!-- 组件使用 -->
<template>
  <item-list :items="users">
    <template #default="{ item, index }">
      <div class="user-item">
        <span>{{ index + 1 }}. {{ item.name }}</span>
        <span>{{ item.email }}</span>
      </div>
    </template>
  </item-list>
</template>
```

#### 组件通信最佳实践

1. **Props 和 Events**：父子组件通信的首选方式
2. **Provide/Inject**：适用于深层嵌套组件通信

```typescript
// 父组件
import { provide } from 'vue';

provide('theme', {
  color: 'dark',
  fontSize: 'medium'
});

// 子组件
import { inject } from 'vue';

const theme = inject('theme', { color: 'light', fontSize: 'small' });
```

3. **Pinia Store**：适用于跨组件通信和全局状态管理
4. **EventBus**：Vue 3 中可以使用 mitt 库实现事件总线

```typescript
// src/utils/eventBus.ts
import mitt from 'mitt';

type Events = {
  'refresh-data': void;
  'user-logged-in': { userId: string };
};

export const eventBus = mitt<Events>();

// 组件 A
import { eventBus } from '@/utils/eventBus';

eventBus.emit('refresh-data');

// 组件 B
import { eventBus } from '@/utils/eventBus';
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  eventBus.on('refresh-data', handleRefresh);
});

onUnmounted(() => {
  eventBus.off('refresh-data', handleRefresh);
});

function handleRefresh() {
  // 刷新数据逻辑
}
```
   - 复杂组件拆分为多个小组件
   - 避免超过 300 行的大型组件

2. **使用组合式 API（Composition API）**
   - 优先使用 `<script setup>` 语法
   - 使用 `defineProps` 和 `defineEmits` 定义属性和事件
   - 使用 `defineExpose` 暴露组件方法和属性
   - 使用 `ref`、`reactive`、`computed` 和 `watch` 管理状态

3. **抽取可复用逻辑到 composables**
   - 将业务逻辑封装为可复用的组合式函数
   - 命名以 `use` 开头，如 `useUserAuth`
   - 遵循输入参数、处理逻辑、返回结果的模式
   - 示例：
   ```typescript
   // useCounter.ts
   import { ref } from 'vue'
   
   export function useCounter(initialValue = 0) {
     const count = ref(initialValue)
     const increment = () => count.value++
     const decrement = () => count.value--
     const reset = () => (count.value = initialValue)
     
     return {
       count,
       increment,
       decrement,
       reset
     }
   }
   ```

### 状态管理

1. **使用 Pinia 进行状态管理**
   - 按功能模块组织 store
   - 使用 `defineStore` 创建 store
   - 使用 `setup` 风格的 store 定义
   - 示例：
   ```typescript
   // useUserStore.ts
   export const useUserStore = defineStore('user', () => {
     const user = ref(null)
     const token = ref('')
     
     const login = async (credentials) => {
       // 登录逻辑
     }
     
     const logout = () => {
       // 登出逻辑
     }
     
     return {
       user,
       token,
       login,
       logout
     }
   })
   ```

2. **按模块组织 store**
   - 每个功能模块创建独立的 store
   - 避免创建全局状态的大型 store
   - 使用 store 之间的组合实现复杂状态管理

3. **使用 pinia-plugin-persistedstate 进行持久化**
   - 只持久化必要的状态（如用户信息、token）
   - 配置适当的存储策略（localStorage、sessionStorage）
   - 设置合理的过期时间

### 性能优化

性能优化是提升用户体验的关键因素，本项目采用多种策略优化应用性能。

#### 代码层面优化

1. **按需加载**
   - 路由懒加载
   ```typescript
   // src/router/modules/dashboard.ts
   const routes = [
     {
       path: '/dashboard',
       component: () => import('@/layout/index.vue'),
       children: [
         {
           path: '',
           name: 'Dashboard',
           component: () => import('@/views/dashboard/index.vue'),
           meta: {
             title: '仪表盘',
             icon: 'dashboard',
             affix: true
           }
         }
       ]
     }
   ];
   ```
   
   - 组件懒加载
   ```vue
   <script setup lang="ts">
   import { defineAsyncComponent } from 'vue';
   
   // 异步加载大型组件
   const HeavyChart = defineAsyncComponent(() => 
     import('@/components/charts/HeavyChart.vue')
   );
   
   // 带加载状态的异步组件
   const ComplexTable = defineAsyncComponent({
     loader: () => import('@/components/tables/ComplexTable.vue'),
     loadingComponent: () => import('@/components/common/LoadingComponent.vue'),
     errorComponent: () => import('@/components/common/ErrorComponent.vue'),
     delay: 200,
     timeout: 10000
   });
   </script>
   ```

2. **虚拟列表**
   - 使用 `vue-virtual-scroller` 处理大数据列表
   ```vue
   <template>
     <RecycleScroller
       class="scroller"
       :items="items"
       :item-size="50"
       key-field="id"
     >
       <template #item="{ item }">
         <div class="user-item">
           {{ item.name }}
         </div>
       </template>
     </RecycleScroller>
   </template>
   
   <script setup lang="ts">
   import { RecycleScroller } from 'vue-virtual-scroller';
   import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
   
   // 大数据列表
   const items = ref(Array.from({ length: 10000 }, (_, i) => ({
     id: i,
     name: `User ${i}`,
   })));
   </script>
   ```

3. **防抖和节流**
   - 使用 `@vueuse/core` 提供的工具函数
   ```typescript
   import { useDebounceFn, useThrottleFn } from '@vueuse/core';
   
   // 防抖：用于搜索输入等场景
   const debouncedSearch = useDebounceFn((query) => {
     searchApi.search(query);
   }, 300);
   
   // 节流：用于滚动事件等高频触发场景
   const throttledScroll = useThrottleFn(() => {
     // 滚动处理逻辑
     checkScrollPosition();
   }, 100);
   
   // 使用
   function onInputChange(e) {
     debouncedSearch(e.target.value);
   }
   
   function onScroll(e) {
     throttledScroll();
   }
   ```

4. **计算属性缓存**
   - 合理使用计算属性缓存派生值
   ```typescript
   // 推荐：使用计算属性
   const filteredItems = computed(() => {
     return items.value.filter(item => item.status === status.value);
   });
   
   // 不推荐：在模板中直接过滤
   // <div v-for="item in items.filter(item => item.status === status)">
   ```

5. **避免不必要的组件渲染**
   - 使用 `v-once` 渲染静态内容
   ```vue
   <template>
     <div v-once>{{ staticContent }}</div>
   </template>
   ```
   
   - 使用 `v-memo` 缓存模板部分
   ```vue
   <template>
     <div v-for="item in list" :key="item.id" v-memo="[item.id, item.updated]">
       <!-- 只有当 item.id 或 item.updated 变化时才会重新渲染 -->
       {{ item.name }}
     </div>
   </template>
   ```
   
   - 使用 `shallowRef` 和 `shallowReactive` 减少深层响应
   ```typescript
   // 大型数据对象，但只关心引用变化
   const tableData = shallowRef(largeDataObject);
   
   // 更新时替换整个引用
   function updateData(newData) {
     tableData.value = newData;
   }
   ```

#### 构建优化

1. **代码分割**
   - 基于路由的代码分割
   - 将第三方库拆分为单独的 chunk
   ```javascript
   // vite.config.ts
   export default defineConfig({
     build: {
       rollupOptions: {
         output: {
           manualChunks: {
             'vue-vendor': ['vue', 'vue-router', 'pinia'],
             'ui-vendor': ['naive-ui'],
             'chart-vendor': ['echarts'],
             'utils': ['lodash-es', '@vueuse/core']
           }
         }
       }
     }
   });
   ```

2. **资源压缩**
   - 使用 gzip 或 brotli 压缩
   ```javascript
   // vite.config.ts
   import viteCompression from 'vite-plugin-compression';
   
   export default defineConfig({
     plugins: [
       viteCompression({
         algorithm: 'gzip',
         threshold: 10240 // 10kb
       })
     ]
   });
   ```

3. **Tree Shaking**
   - 使用 ES 模块语法确保有效的 Tree Shaking
   ```typescript
   // 推荐：命名导入，便于 Tree Shaking
   import { debounce, throttle } from 'lodash-es';
   
   // 不推荐：整体导入
   // import _ from 'lodash';
   ```

4. **图片优化**
   - 使用 WebP 格式
   - 使用响应式图片
   - 图片懒加载
   ```vue
   <template>
     <img
       v-for="img in images"
       :key="img.id"
       v-lazy="img.url"
       :alt="img.alt"
     />
   </template>
   
   <script setup lang="ts">
   import { useLazyload } from '@/directives/lazyload';
   
   const vLazy = useLazyload();
   </script>
   ```

#### 运行时优化

1. **Web Worker**
   - 将耗时计算放入 Web Worker
   ```typescript
   // src/workers/calculation.worker.ts
   self.onmessage = (e) => {
     const { data } = e;
     // 执行耗时计算
     const result = performHeavyCalculation(data);
     self.postMessage(result);
   };
   
   // 使用 Worker
   const worker = new Worker(new URL('./calculation.worker.ts', import.meta.url));
   
   worker.onmessage = (e) => {
     const result = e.data;
     // 处理结果
     processResult(result);
   };
   
   // 发送数据到 Worker
   worker.postMessage(inputData);
   ```

2. **缓存策略**
   - 使用 localStorage 缓存不常变化的数据
   ```typescript
   // src/utils/cache.ts
   import { useStorage } from '@vueuse/core';
   
   export function useLocalCache<T>(key: string, defaultValue: T, expires = 3600000) {
     const data = useStorage<{
       value: T;
       expires: number;
     }>(key, {
       value: defaultValue,
       expires: Date.now() + expires
     });
     
     const isExpired = computed(() => {
       return data.value.expires < Date.now();
     });
     
     const cachedValue = computed({
       get: () => isExpired.value ? defaultValue : data.value.value,
       set: (value) => {
         data.value = {
           value,
           expires: Date.now() + expires
         };
       }
     });
     
     return cachedValue;
   }
   
   // 使用
   const userSettings = useLocalCache('user-settings', defaultSettings, 24 * 3600000);
   ```

3. **预加载关键资源**
   - 使用 `<link rel="prefetch">` 预加载可能需要的资源
   ```html
   <!-- index.html -->
   <link rel="prefetch" href="/assets/dashboard.js">
   ```
   
   - 在路由切换前预加载组件
   ```typescript
   // src/router/index.ts
   router.beforeEach((to, from, next) => {
     // 预加载目标路由的组件
     if (to.matched.length > 0) {
       to.matched.forEach((record) => {
         if (record.components) {
           Object.keys(record.components).forEach((name) => {
             const component = record.components[name];
             // 触发组件预加载
             if (typeof component === 'function') {
               component();
             }
           });
         }
       });
     }
     next();
   });
   ```

#### 性能监控

1. **使用 Performance API**
   ```typescript
   // src/utils/performance.ts
   export function measurePerformance(name: string, fn: () => void) {
     performance.mark(`${name}-start`);
     fn();
     performance.mark(`${name}-end`);
     performance.measure(name, `${name}-start`, `${name}-end`);
     
     const measures = performance.getEntriesByName(name);
     console.log(`${name} took ${measures[0].duration.toFixed(2)}ms`);
   }
   
   // 使用
   measurePerformance('data-processing', () => {
     processLargeDataSet(data);
   });
   ```

2. **监控关键指标**
   - 首次内容绘制 (FCP)
   - 最大内容绘制 (LCP)
   - 首次输入延迟 (FID)
   - 累积布局偏移 (CLS)
   ```typescript
   // src/utils/vitals.ts
   import { onCLS, onFID, onLCP } from 'web-vitals';
   
   export function reportWebVitals(analyticsTracker) {
     onCLS((metric) => analyticsTracker('CLS', metric.value));
     onFID((metric) => analyticsTracker('FID', metric.value));
     onLCP((metric) => analyticsTracker('LCP', metric.value));
   }
   ```

#### 性能优化检查清单

1. **代码层面**
   - [ ] 使用路由懒加载
   - [ ] 大型组件使用异步加载
   - [ ] 大数据列表使用虚拟滚动
   - [ ] 高频事件使用防抖/节流
   - [ ] 合理使用计算属性缓存
   - [ ] 避免不必要的组件渲染

2. **构建优化**
   - [ ] 配置合理的代码分割
   - [ ] 启用资源压缩
   - [ ] 确保有效的 Tree Shaking
   - [ ] 优化图片资源

3. **运行时优化**
   - [ ] 耗时计算使用 Web Worker
   - [ ] 实现合理的缓存策略
   - [ ] 预加载关键资源

4. **性能监控**
   - [ ] 监控关键性能指标
   - [ ] 建立性能基准
   - [ ] 定期进行性能审计

1. **减少不必要的渲染**
   - 使用 `v-memo` 缓存基于条件的渲染结果
   - 使用 `v-once` 渲染只需渲染一次的内容
   - 使用 `computed` 缓存计算结果
   - 合理使用 `v-show` 和 `v-if`

2. **优化大型数据处理**
   - 使用 `shallowRef` 和 `shallowReactive` 优化大型对象
   - 使用虚拟滚动处理长列表（如 `vue-virtual-scroller`）
   - 实现分页加载而非一次加载全部数据

3. **代码分割与懒加载**
   - 使用动态导入和路由懒加载
   - 按路由分割代码
   - 使用 Suspense 处理异步组件
   ```javascript
   // 路由懒加载示例
   const routes = [
     {
       path: '/dashboard',
       component: () => import('@/views/dashboard/index.vue'),
       meta: { title: '仪表盘' }
     }
   ]
   ```

4. **资源优化**
   - 压缩图片和使用适当的图片格式（WebP、SVG）
   - 使用 CDN 加载第三方库
   - 使用 Gzip/Brotli 压缩静态资源

### 安全实践

安全是应用开发中的重要环节，本项目采用多种安全实践保护应用和用户数据。

#### 前端安全基础

1. **输入验证与过滤**
   - 对所有用户输入进行验证和过滤
   - 使用 TypeScript 类型系统增强类型安全
   - 使用验证库（如 Zod、Vuelidate）进行数据验证
   ```typescript
   // src/utils/validators.ts
   import { z } from 'zod';
   
   // 定义验证模式
   export const userSchema = z.object({
     username: z.string().min(3).max(20),
     email: z.string().email(),
     password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*$/)
   });
   
   // 使用验证
   export function validateUserData(data: unknown) {
     try {
       const result = userSchema.parse(data);
       return { valid: true, data: result };
     } catch (error) {
       return { valid: false, errors: error.errors };
     }
   }
   ```

2. **XSS 防护**
   - Vue 默认转义模板中的 HTML
   - 谨慎使用 `v-html` 指令
   - 使用 DOMPurify 清理不可信内容
   ```typescript
   // src/utils/sanitize.ts
   import DOMPurify from 'dompurify';
   
   export function sanitizeHtml(html: string): string {
     return DOMPurify.sanitize(html, {
       ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'ul', 'ol', 'li', 'br'],
       ALLOWED_ATTR: ['href', 'target', 'rel'],
     });
   }
   ```
   
   ```vue
   <template>
     <!-- 不安全 -->
     <!-- <div v-html="userProvidedContent"></div> -->
     
     <!-- 安全 -->
     <div v-html="sanitizedContent"></div>
   </template>
   
   <script setup lang="ts">
   import { computed } from 'vue';
   import { sanitizeHtml } from '@/utils/sanitize';
   
   const props = defineProps<{
     userProvidedContent: string;
   }>();
   
   const sanitizedContent = computed(() => {
     return sanitizeHtml(props.userProvidedContent);
   });
   </script>
   ```

3. **CSRF 防护**
   - 在请求中包含 CSRF Token
   - 使用 SameSite Cookie 属性
   ```typescript
   // src/api/axios.ts
   import axios from 'axios';
   
   const instance = axios.create({
     baseURL: import.meta.env.VITE_API_URL,
     timeout: 10000,
     withCredentials: true, // 允许跨域请求携带 Cookie
   });
   
   // 请求拦截器添加 CSRF Token
   instance.interceptors.request.use((config) => {
     const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
     if (token) {
       config.headers['X-CSRF-Token'] = token;
     }
     return config;
   });
   ```

4. **敏感信息处理**
   - 不在前端存储敏感信息
   - 不在 URL 中传递敏感参数
   - 使用 HTTPS 传输数据
   - 敏感信息脱敏显示
   ```typescript
   // src/utils/mask.ts
   export function maskPhoneNumber(phone: string): string {
     return phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
   }
   
   export function maskEmail(email: string): string {
     const [username, domain] = email.split('@');
     const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
     return `${maskedUsername}@${domain}`;
   }
   ```

5. **安全的存储机制**
   - 敏感数据不存储在 localStorage/sessionStorage
   - 使用 Cookie 的 HttpOnly 和 Secure 属性
   - 考虑使用 Web Crypto API 进行客户端加密
   ```typescript
   // src/utils/storage.ts
   import CryptoJS from 'crypto-js';
   
   const SECRET_KEY = import.meta.env.VITE_STORAGE_KEY || 'default-secret-key';
   
   export const secureStorage = {
     setItem(key: string, value: any) {
       const encryptedValue = CryptoJS.AES.encrypt(
         JSON.stringify(value),
         SECRET_KEY
       ).toString();
       localStorage.setItem(key, encryptedValue);
     },
     
     getItem(key: string) {
       const encryptedValue = localStorage.getItem(key);
       if (!encryptedValue) return null;
       
       try {
         const decrypted = CryptoJS.AES.decrypt(encryptedValue, SECRET_KEY).toString(CryptoJS.enc.Utf8);
         return JSON.parse(decrypted);
       } catch (error) {
         console.error('Failed to decrypt value:', error);
         return null;
       }
     },
     
     removeItem(key: string) {
       localStorage.removeItem(key);
     },
     
     clear() {
       localStorage.clear();
     }
   };
   ```

#### API 安全

1. **请求与响应安全**
   - 实现请求超时机制
   - 处理错误响应
   - 验证响应数据结构
   ```typescript
   // src/api/axios.ts
   import axios, { AxiosResponse, AxiosError } from 'axios';
   import { z } from 'zod';
   
   // 响应数据结构验证
   const responseSchema = z.object({
     code: z.number(),
     data: z.any(),
     message: z.string().optional(),
   });
   
   const instance = axios.create({
     baseURL: import.meta.env.VITE_API_URL,
     timeout: 15000,
   });
   
   // 响应拦截器
   instance.interceptors.response.use(
     (response: AxiosResponse) => {
       try {
         // 验证响应结构
         const validatedData = responseSchema.parse(response.data);
         
         // 检查业务状态码
         if (validatedData.code !== 0) {
           return Promise.reject(new Error(validatedData.message || '请求失败'));
         }
         
         return validatedData.data;
       } catch (error) {
         return Promise.reject(new Error('响应数据结构异常'));
       }
     },
     (error: AxiosError) => {
       if (error.code === 'ECONNABORTED') {
         // 超时处理
         console.error('请求超时');
       }
       
       return Promise.reject(error);
     }
   );
   ```

2. **API 权限控制**
   - 基于 Token 的认证
   - 实现请求重试和 Token 刷新机制
   ```typescript
   // src/api/auth.ts
   import axios from 'axios';
   import { useUserStore } from '@/store/modules/user';
   
   let isRefreshing = false;
   let refreshSubscribers: ((token: string) => void)[] = [];
   
   // 订阅 Token 刷新
   function subscribeTokenRefresh(cb: (token: string) => void) {
     refreshSubscribers.push(cb);
   }
   
   // 执行订阅
   function onTokenRefreshed(token: string) {
     refreshSubscribers.forEach((cb) => cb(token));
     refreshSubscribers = [];
   }
   
   // Token 刷新
   async function refreshToken() {
     try {
       const userStore = useUserStore();
       const refreshToken = userStore.refreshToken;
       
       const response = await axios.post('/auth/refresh', { refreshToken });
       const { token, refreshToken: newRefreshToken } = response.data;
       
       userStore.setToken(token);
       userStore.setRefreshToken(newRefreshToken);
       
       return token;
     } catch (error) {
       // 刷新失败，登出处理
       const userStore = useUserStore();
       userStore.logout();
       return Promise.reject(error);
     }
   }
   
   // 请求拦截器
   instance.interceptors.response.use(
     (response) => response,
     async (error) => {
       const originalRequest = error.config;
       
       // 如果是 401 错误且不是刷新 Token 的请求且没有重试过
       if (
         error.response?.status === 401 &&
         !originalRequest._retry &&
         !originalRequest.url.includes('/auth/refresh')
       ) {
         if (isRefreshing) {
           // 等待 Token 刷新完成
           return new Promise((resolve) => {
             subscribeTokenRefresh((token) => {
               originalRequest.headers.Authorization = `Bearer ${token}`;
               resolve(axios(originalRequest));
             });
           });
         }
         
         originalRequest._retry = true;
         isRefreshing = true;
         
         try {
           const newToken = await refreshToken();
           originalRequest.headers.Authorization = `Bearer ${newToken}`;
           onTokenRefreshed(newToken);
           isRefreshing = false;
           return axios(originalRequest);
         } catch (refreshError) {
           isRefreshing = false;
           return Promise.reject(refreshError);
         }
       }
       
       return Promise.reject(error);
     }
   );
   ```

#### 其他安全实践

1. **依赖安全**
   - 定期更新依赖包
   - 使用 npm audit 检查依赖安全性
   - 使用 Snyk 或 Dependabot 监控依赖漏洞
   ```bash
   # 检查依赖安全性
   npm audit
   
   # 修复可自动修复的漏洞
   npm audit fix
   ```

2. **安全的配置管理**
   - 使用环境变量存储配置
   - 不在代码中硬编码敏感信息
   - 区分开发和生产环境配置
   ```typescript
   // src/config/index.ts
   export const config = {
     apiUrl: import.meta.env.VITE_API_URL,
     timeout: import.meta.env.VITE_API_TIMEOUT ? parseInt(import.meta.env.VITE_API_TIMEOUT) : 10000,
     enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
     sentryDsn: import.meta.env.VITE_SENTRY_DSN,
   };
   ```

3. **错误处理与日志**
   - 实现全局错误处理
   - 使用 Sentry 等工具记录前端错误
   - 不向用户展示敏感的错误信息
   ```typescript
   // src/utils/error.ts
   import * as Sentry from '@sentry/vue';
   
   export function initErrorTracking(app) {
     if (import.meta.env.PROD && import.meta.env.VITE_SENTRY_DSN) {
       Sentry.init({
         app,
         dsn: import.meta.env.VITE_SENTRY_DSN,
         environment: import.meta.env.MODE,
         release: import.meta.env.VITE_APP_VERSION,
         tracesSampleRate: 0.2,
       });
     }
     
     // 全局错误处理
     window.addEventListener('error', (event) => {
       console.error('Global error:', event.error);
       // 可以在这里添加自定义错误处理逻辑
       return false;
     });
     
     window.addEventListener('unhandledrejection', (event) => {
       console.error('Unhandled promise rejection:', event.reason);
       // 可以在这里添加自定义 Promise 错误处理逻辑
       return false;
     });
   }
   
   // 安全地显示错误信息给用户
   export function displayUserFriendlyError(error: any) {
     // 开发环境显示详细错误
     if (import.meta.env.DEV) {
       console.error('Error details:', error);
     }
     
     // 生产环境显示通用错误信息
     const message = import.meta.env.PROD
       ? '操作失败，请稍后重试'
       : error.message || '未知错误';
     
     return message;
   }
   ```

4. **安全的部署流程**
   - 使用 CI/CD 自动化部署
   - 部署前进行安全检查
   - 使用内容安全策略 (CSP)
   ```html
   <!-- public/index.html -->
   <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' https://trusted-cdn.com; style-src 'self' https://trusted-cdn.com; img-src 'self' data: https://trusted-cdn.com; connect-src 'self' https://api.example.com;">
   ```

#### 安全检查清单

1. **输入验证**
   - [ ] 所有用户输入经过验证
   - [ ] 使用类型安全的验证库
   - [ ] 实现前端和后端双重验证

2. **XSS 防护**
   - [ ] 避免直接使用 `v-html`
   - [ ] 使用 DOMPurify 清理 HTML
   - [ ] 实现内容安全策略 (CSP)

3. **认证与授权**
   - [ ] 实现安全的登录机制
   - [ ] 使用 JWT 或其他安全令牌
   - [ ] 实现令牌刷新机制
   - [ ] 敏感操作需要二次验证

4. **数据保护**
   - [ ] 敏感数据加密存储
   - [ ] 使用 HTTPS 传输数据
   - [ ] 实现数据脱敏显示
   - [ ] 定期清理不需要的数据

5. **依赖安全**
   - [ ] 定期更新依赖包
   - [ ] 使用工具监控依赖漏洞
   - [ ] 审查第三方库的安全性

1. **前端安全**
   - 避免使用 `v-html` 处理不可信内容
   - 对用户输入进行验证和转义
   - 使用 CSP (Content Security Policy)
   - 防范 XSS 和 CSRF 攻击

2. **API 安全**
   - 使用 HTTPS
   - 实施适当的认证和授权
   - 使用 JWT 并设置合理的过期时间
   - 敏感信息不在前端存储或传输

3. **数据保护**
   - 敏感数据加密存储
   - 清除会话数据和本地存储中的敏感信息
   - 实现自动登出机制

### 可访问性

可访问性（Accessibility，简称 a11y）是确保所有用户，包括残障人士，都能使用我们的应用程序的重要方面。本项目致力于遵循 WCAG 2.1 AA 级标准。

#### 可访问性基础

1. **语义化 HTML**
   - 使用正确的 HTML 元素表达内容的含义
   - 使用适当的 ARIA 属性补充语义
   - 确保页面结构合理（标题层级、区域划分）
   ```vue
   <!-- 不推荐 -->
   <div class="button" @click="submit">提交</div>
   
   <!-- 推荐 -->
   <button type="submit" @click="submit">提交</button>
   ```

2. **键盘可访问性**
   - 确保所有交互元素可通过键盘访问
   - 实现合理的焦点顺序
   - 提供可见的焦点指示器
   ```scss
   // src/styles/accessibility.scss
   :focus {
     outline: 2px solid #1890ff;
     outline-offset: 2px;
   }
   
   // 仅在使用键盘时显示焦点样式
   :focus:not(:focus-visible) {
     outline: none;
   }
   
   :focus-visible {
     outline: 2px solid #1890ff;
     outline-offset: 2px;
   }
   ```

3. **颜色与对比度**
   - 确保文本与背景的对比度符合 WCAG AA 标准（4.5:1）
   - 不仅依靠颜色传达信息
   - 提供高对比度模式
   ```typescript
   // src/utils/contrast.ts
   export function checkContrast(foreground: string, background: string): number {
     // 计算对比度的函数
     // 返回对比度比值
     // ...
   }
   
   export function isContrastSufficient(foreground: string, background: string): boolean {
     const ratio = checkContrast(foreground, background);
     return ratio >= 4.5; // WCAG AA 标准
   }
   ```

4. **表单可访问性**
   - 为表单控件提供关联的标签
   - 提供清晰的错误提示
   - 使用 fieldset 和 legend 分组表单控件
   ```vue
   <template>
     <form @submit.prevent="submitForm">
       <div class="form-group">
         <label for="username" class="form-label">用户名</label>
         <input 
           id="username" 
           v-model="form.username" 
           type="text" 
           class="form-input"
           aria-required="true"
           aria-invalid="!!errors.username"
           aria-describedby="username-error"
         >
         <div v-if="errors.username" id="username-error" class="form-error" role="alert">
           {{ errors.username }}
         </div>
       </div>
       
       <fieldset>
         <legend>通知设置</legend>
         <div class="checkbox-group">
           <input id="email-notify" v-model="form.emailNotify" type="checkbox">
           <label for="email-notify">接收邮件通知</label>
         </div>
         <div class="checkbox-group">
           <input id="sms-notify" v-model="form.smsNotify" type="checkbox">
           <label for="sms-notify">接收短信通知</label>
         </div>
       </fieldset>
       
       <button type="submit" class="submit-btn">提交</button>
     </form>
   </template>
   ```

5. **图像可访问性**
   - 为所有图像提供替代文本
   - 对于装饰性图像，使用空的 alt 属性
   - 对于复杂图像，提供详细描述
   ```vue
   <!-- 信息性图像 -->
   <img src="/images/chart.png" alt="2023年第一季度销售数据柱状图，显示各月销售额">
   
   <!-- 装饰性图像 -->
   <img src="/images/decoration.png" alt="" role="presentation">
   
   <!-- 使用 SVG 时 -->
   <svg aria-labelledby="chart-title chart-desc" role="img">
     <title id="chart-title">销售趋势图</title>
     <desc id="chart-desc">展示过去12个月的销售趋势，呈现上升态势</desc>
     <!-- SVG 内容 -->
   </svg>
   ```

#### 可访问性组件

1. **模态框**
   ```vue
   <template>
     <div v-if="isOpen" class="modal-overlay" @click="closeOnOverlay && close()">
       <div 
         class="modal" 
         role="dialog" 
         aria-labelledby="modal-title"
         aria-describedby="modal-desc"
         aria-modal="true"
         @click.stop
         ref="modalRef"
       >
         <header class="modal-header">
           <h2 id="modal-title" class="modal-title">{{ title }}</h2>
           <button 
             type="button" 
             class="modal-close" 
             aria-label="关闭"
             @click="close"
           >
             &times;
           </button>
         </header>
         
         <div id="modal-desc" class="modal-body">
           <slot></slot>
         </div>
         
         <footer class="modal-footer">
           <slot name="footer">
             <button type="button" class="btn-cancel" @click="close">取消</button>
             <button type="button" class="btn-confirm" @click="confirm">确认</button>
           </slot>
         </footer>
       </div>
     </div>
   </template>
   
   <script setup lang="ts">
   import { ref, onMounted, onUnmounted, watch } from 'vue';
   
   const props = defineProps<{
     isOpen: boolean;
     title: string;
     closeOnOverlay?: boolean;
   }>();
   
   const emit = defineEmits<{
     (e: 'close'): void;
     (e: 'confirm'): void;
   }>();
   
   const modalRef = ref<HTMLElement | null>(null);
   const previouslyFocused = ref<HTMLElement | null>(null);
   
   const close = () => emit('close');
   const confirm = () => emit('confirm');
   
   // 处理 ESC 键关闭
   const handleKeyDown = (event: KeyboardEvent) => {
     if (event.key === 'Escape' && props.isOpen) {
       close();
     }
   };
   
   // 焦点陷阱
   const handleTabKey = (event: KeyboardEvent) => {
     if (!modalRef.value || event.key !== 'Tab') return;
     
     const focusableElements = modalRef.value.querySelectorAll(
       'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
     );
     
     const firstElement = focusableElements[0] as HTMLElement;
     const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
     
     // 如果按下 Shift+Tab 且当前焦点在第一个元素上，则将焦点移至最后一个元素
     if (event.shiftKey && document.activeElement === firstElement) {
       lastElement.focus();
       event.preventDefault();
     }
     // 如果按下 Tab 且当前焦点在最后一个元素上，则将焦点移至第一个元素
     else if (!event.shiftKey && document.activeElement === lastElement) {
       firstElement.focus();
       event.preventDefault();
     }
   };
   
   watch(() => props.isOpen, (newVal) => {
     if (newVal) {
       // 保存当前焦点元素
       previouslyFocused.value = document.activeElement as HTMLElement;
       // 模态框打开后，将焦点移至模态框
       setTimeout(() => {
         const focusableElement = modalRef.value?.querySelector(
           'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
         ) as HTMLElement;
         focusableElement?.focus();
       }, 50);
       
       // 添加键盘事件监听
       document.addEventListener('keydown', handleKeyDown);
       document.addEventListener('keydown', handleTabKey);
     } else if (previouslyFocused.value) {
       // 模态框关闭后，恢复之前的焦点
       previouslyFocused.value.focus();
       
       // 移除键盘事件监听
       document.removeEventListener('keydown', handleKeyDown);
       document.removeEventListener('keydown', handleTabKey);
     }
   });
   
   onMounted(() => {
     if (props.isOpen) {
       previouslyFocused.value = document.activeElement as HTMLElement;
       document.addEventListener('keydown', handleKeyDown);
       document.addEventListener('keydown', handleTabKey);
     }
   });
   
   onUnmounted(() => {
     document.removeEventListener('keydown', handleKeyDown);
     document.removeEventListener('keydown', handleTabKey);
   });
   </script>
   ```

2. **下拉菜单**
   ```vue
   <template>
     <div class="dropdown" ref="dropdownRef">
       <button 
         type="button" 
         class="dropdown-toggle" 
         :aria-expanded="isOpen" 
         aria-haspopup="true"
         @click="toggle"
         ref="toggleRef"
       >
         {{ label }}
         <span class="dropdown-arrow" aria-hidden="true">▼</span>
       </button>
       
       <ul 
         v-if="isOpen" 
         class="dropdown-menu" 
         role="menu"
         ref="menuRef"
       >
         <li v-for="(item, index) in items" :key="index" role="none">
           <button 
             type="button" 
             class="dropdown-item" 
             role="menuitem"
             tabindex="-1"
             @click="select(item)"
           >
             {{ item.label }}
           </button>
         </li>
       </ul>
     </div>
   </template>
   
   <script setup lang="ts">
   import { ref, onMounted, onUnmounted } from 'vue';
   
   interface DropdownItem {
     label: string;
     value: any;
   }
   
   const props = defineProps<{
     label: string;
     items: DropdownItem[];
   }>();
   
   const emit = defineEmits<{
     (e: 'select', item: DropdownItem): void;
   }>();
   
   const isOpen = ref(false);
   const dropdownRef = ref<HTMLElement | null>(null);
   const toggleRef = ref<HTMLElement | null>(null);
   const menuRef = ref<HTMLElement | null>(null);
   
   const toggle = () => {
     isOpen.value = !isOpen.value;
   };
   
   const select = (item: DropdownItem) => {
     emit('select', item);
     isOpen.value = false;
     toggleRef.value?.focus();
   };
   
   const handleClickOutside = (event: MouseEvent) => {
     if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
       isOpen.value = false;
     }
   };
   
   const handleKeyDown = (event: KeyboardEvent) => {
     if (!isOpen.value) return;
     
     const menuItems = menuRef.value?.querySelectorAll('[role="menuitem"]');
     if (!menuItems?.length) return;
     
     const currentIndex = Array.from(menuItems).findIndex(
       item => item === document.activeElement
     );
     
     switch (event.key) {
       case 'Escape':
         isOpen.value = false;
         toggleRef.value?.focus();
         event.preventDefault();
         break;
         
       case 'ArrowDown':
         if (currentIndex < 0) {
           (menuItems[0] as HTMLElement).focus();
         } else if (currentIndex < menuItems.length - 1) {
           (menuItems[currentIndex + 1] as HTMLElement).focus();
         }
         event.preventDefault();
         break;
         
       case 'ArrowUp':
         if (currentIndex > 0) {
           (menuItems[currentIndex - 1] as HTMLElement).focus();
         } else {
           (menuItems[menuItems.length - 1] as HTMLElement).focus();
         }
         event.preventDefault();
         break;
         
       case 'Home':
         (menuItems[0] as HTMLElement).focus();
         event.preventDefault();
         break;
         
       case 'End':
         (menuItems[menuItems.length - 1] as HTMLElement).focus();
         event.preventDefault();
         break;
     }
   };
   
   onMounted(() => {
     document.addEventListener('click', handleClickOutside);
     document.addEventListener('keydown', handleKeyDown);
   });
   
   onUnmounted(() => {
     document.removeEventListener('click', handleClickOutside);
     document.removeEventListener('keydown', handleKeyDown);
   });
   </script>
   ```

#### 可访问性测试

1. **自动化测试**
   - 使用 axe-core 进行自动化可访问性测试
   - 集成到 CI/CD 流程中
   ```typescript
   // tests/accessibility.spec.ts
   import { mount } from '@vue/test-utils';
   import { axe, toHaveNoViolations } from 'jest-axe';
   import MyComponent from '@/components/MyComponent.vue';
   
   expect.extend(toHaveNoViolations);
   
   describe('可访问性测试', () => {
     it('MyComponent 应该没有可访问性违规', async () => {
       const wrapper = mount(MyComponent);
       const html = wrapper.element;
       
       const results = await axe(html);
       expect(results).toHaveNoViolations();
     });
   });
   ```

2. **手动测试**
   - 使用屏幕阅读器测试（如 NVDA、VoiceOver）
   - 仅使用键盘导航测试
   - 使用高对比度模式测试

#### 可访问性检查清单

1. **语义化结构**
   - [ ] 使用语义化 HTML 元素
   - [ ] 页面有清晰的标题层级（h1-h6）
   - [ ] 使用适当的 ARIA 角色和属性
   - [ ] 使用列表元素表示列表内容

2. **键盘可访问性**
   - [ ] 所有功能可通过键盘访问
   - [ ] 焦点顺序合理且可预测
   - [ ] 提供可见的焦点指示器
   - [ ] 没有键盘陷阱

3. **表单可访问性**
   - [ ] 所有表单控件有关联的标签
   - [ ] 表单错误清晰且可访问
   - [ ] 表单分组使用 fieldset 和 legend
   - [ ] 必填字段有明确标识

4. **颜色与对比度**
   - [ ] 文本对比度符合 WCAG AA 标准
   - [ ] 不仅依靠颜色传达信息
   - [ ] 提供高对比度模式

5. **图像与媒体**
   - [ ] 所有图像有适当的替代文本
   - [ ] 视频有字幕和文字记录
   - [ ] 音频内容有文字记录

6. **动态内容**
   - [ ] 动态更新的内容对屏幕阅读器可访问
   - [ ] 使用适当的 ARIA live 区域
   - [ ] 提供暂停、停止或隐藏动画的选项

1. **语义化开发**
   - 使用语义化 HTML 元素（`<header>`, `<nav>`, `<main>`, `<section>` 等）
   - 提供适当的 ARIA 属性
   - 确保表单元素有关联的标签

2. **键盘可访问性**
   - 确保所有交互元素可通过键盘访问
   - 实现合理的焦点管理
   - 提供键盘快捷键

3. **视觉设计**
   - 考虑颜色对比度（符合 WCAG 标准）
   - 提供足够大的点击区域
   - 支持文本缩放

### 测试策略

本项目采用多层次的测试策略，确保代码质量和应用稳定性。

#### 测试类型

1. **单元测试**
   - 测试独立的函数、组件和模块
   - 使用 Vitest 作为测试框架
   - 使用 @vue/test-utils 测试 Vue 组件
   ```typescript
   // tests/unit/utils/format.spec.ts
   import { describe, it, expect } from 'vitest';
   import { formatCurrency, formatDate } from '@/utils/format';
   
   describe('formatCurrency', () => {
     it('应正确格式化货币值', () => {
       expect(formatCurrency(1000)).toBe('¥1,000.00');
       expect(formatCurrency(1000.5)).toBe('¥1,000.50');
       expect(formatCurrency(0)).toBe('¥0.00');
     });
     
     it('应处理负数', () => {
       expect(formatCurrency(-1000)).toBe('-¥1,000.00');
     });
   });
   
   describe('formatDate', () => {
     it('应正确格式化日期', () => {
       const date = new Date('2023-01-01T12:00:00');
       expect(formatDate(date, 'YYYY-MM-DD')).toBe('2023-01-01');
       expect(formatDate(date, 'YYYY年MM月DD日')).toBe('2023年01月01日');
     });
     
     it('应处理无效日期', () => {
       expect(formatDate(null, 'YYYY-MM-DD')).toBe('-');
     });
   });
   ```

2. **组件测试**
   - 测试组件的渲染、交互和生命周期
   - 使用 @vue/test-utils 和 Vitest
   - 模拟依赖和事件
   ```typescript
   // tests/unit/components/UserCard.spec.ts
   import { describe, it, expect, vi } from 'vitest';
   import { mount } from '@vue/test-utils';
   import UserCard from '@/components/UserCard.vue';
   
   describe('UserCard.vue', () => {
     it('正确渲染用户信息', () => {
       const user = {
         id: 1,
         name: '张三',
         email: '<EMAIL>',
         avatar: '/images/avatar.png'
       };
       
       const wrapper = mount(UserCard, {
         props: { user }
       });
       
       expect(wrapper.text()).toContain('张三');
       expect(wrapper.text()).toContain('<EMAIL>');
       expect(wrapper.find('img').attributes('src')).toBe('/images/avatar.png');
     });
     
     it('点击编辑按钮时触发 edit 事件', async () => {
       const user = { id: 1, name: '张三' };
       const wrapper = mount(UserCard, {
         props: { user }
       });
       
       await wrapper.find('.edit-button').trigger('click');
       
       expect(wrapper.emitted('edit')).toBeTruthy();
       expect(wrapper.emitted('edit')[0]).toEqual([user.id]);
     });
   });
   ```

3. **集成测试**
   - 测试多个组件或模块的交互
   - 测试路由导航和状态管理
   - 使用 Vitest 和 @vue/test-utils
   ```typescript
   // tests/integration/views/UserList.spec.ts
   import { describe, it, expect, vi, beforeEach } from 'vitest';
   import { mount } from '@vue/test-utils';
   import { createRouter, createWebHistory } from 'vue-router';
   import { createTestingPinia } from '@pinia/testing';
   import UserList from '@/views/UserList.vue';
   import { useUserStore } from '@/store/modules/user';
   
   // 模拟 API 请求
   vi.mock('@/api/user', () => ({
     getUserList: vi.fn().mockResolvedValue([
       { id: 1, name: '张三' },
       { id: 2, name: '李四' }
     ])
   }));
   
   describe('UserList.vue', () => {
     let router;
     let wrapper;
     let userStore;
     
     beforeEach(() => {
       // 创建路由
       router = createRouter({
         history: createWebHistory(),
         routes: [
           { path: '/', component: { template: '<div></div>' } },
           { path: '/user/:id', name: 'UserDetail', component: { template: '<div></div>' } }
         ]
       });
       
       // 创建测试 Pinia
       const pinia = createTestingPinia({
         createSpy: vi.fn
       });
       
       // 挂载组件
       wrapper = mount(UserList, {
         global: {
           plugins: [router, pinia]
         }
       });
       
       userStore = useUserStore();
     });
     
     it('加载时获取用户列表', async () => {
       // 等待异步操作完成
       await vi.dynamicImportSettled();
       
       expect(userStore.fetchUsers).toHaveBeenCalled();
       expect(wrapper.findAll('.user-item')).toHaveLength(2);
     });
     
     it('点击用户项时导航到用户详情页', async () => {
       await vi.dynamicImportSettled();
       
       const spy = vi.spyOn(router, 'push');
       await wrapper.find('.user-item').trigger('click');
       
       expect(spy).toHaveBeenCalledWith({
         name: 'UserDetail',
         params: { id: 1 }
       });
     });
   });
   ```

4. **端到端测试**
   - 测试整个应用的用户流程
   - 使用 Cypress 或 Playwright
   - 模拟真实用户交互
   ```typescript
   // cypress/e2e/login.cy.js
   describe('登录流程', () => {
     beforeEach(() => {
       cy.visit('/login');
     });
     
     it('成功登录后重定向到仪表盘', () => {
       // 输入有效凭据
       cy.get('#username').type('admin');
       cy.get('#password').type('password123');
       cy.get('button[type="submit"]').click();
       
       // 验证重定向到仪表盘
       cy.url().should('include', '/dashboard');
       cy.get('.welcome-message').should('contain', 'admin');
     });
     
     it('登录失败时显示错误消息', () => {
       // 输入无效凭据
       cy.get('#username').type('admin');
       cy.get('#password').type('wrongpassword');
       cy.get('button[type="submit"]').click();
       
       // 验证错误消息
       cy.get('.error-message').should('be.visible');
       cy.get('.error-message').should('contain', '用户名或密码错误');
     });
   });
   ```

#### 测试配置

1. **Vitest 配置**
   ```typescript
   // vitest.config.ts
   import { defineConfig } from 'vitest/config';
   import vue from '@vitejs/plugin-vue';
   import { fileURLToPath } from 'url';
   import { mergeConfig } from 'vite';
   import viteConfig from './vite.config';
   
   export default mergeConfig(
     viteConfig,
     defineConfig({
       plugins: [vue()],
       test: {
         globals: true,
         environment: 'jsdom',
         coverage: {
           provider: 'c8',
           reporter: ['text', 'json', 'html'],
           exclude: ['node_modules/', 'tests/', '**/*.d.ts']
         },
         include: ['tests/**/*.spec.ts'],
         alias: {
           '@': fileURLToPath(new URL('./src', import.meta.url))
         }
       }
     })
   );
   ```

2. **Cypress 配置**
   ```javascript
   // cypress.config.js
   const { defineConfig } = require('cypress');
   
   module.exports = defineConfig({
     e2e: {
       baseUrl: 'http://localhost:3000',
       viewportWidth: 1280,
       viewportHeight: 800,
       video: false,
       screenshotOnRunFailure: true,
       setupNodeEvents(on, config) {
         // 实现节点事件监听器
       }
     }
   });
   ```

#### 测试最佳实践

1. **单元测试原则**
   - 测试应该是独立的，不依赖于其他测试
   - 每个测试只测试一个概念
   - 使用 AAA 模式：Arrange（准备）、Act（执行）、Assert（断言）
   - 避免测试实现细节，关注组件行为
   ```typescript
   // 良好的测试示例
   it('用户名少于3个字符时显示错误信息', async () => {
     // Arrange
     const wrapper = mount(UserForm);
     
     // Act
     await wrapper.find('#username').setValue('ab');
     await wrapper.find('form').trigger('submit');
     
     // Assert
     expect(wrapper.find('.error-message').text()).toContain('用户名至少需要3个字符');
   });
   ```

2. **模拟与存根**
   - 模拟外部依赖（API 调用、第三方服务）
   - 使用 vi.mock() 模拟模块
   - 使用 vi.spyOn() 监视方法调用
   ```typescript
   // 模拟 API 调用
   vi.mock('@/api/user', () => ({
     createUser: vi.fn().mockResolvedValue({ id: 1, name: '新用户' })
   }));
   
   // 监视方法调用
   const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
   expect(consoleSpy).toHaveBeenCalledTimes(1);
   ```

3. **组件测试策略**
   - 测试 props 是否正确渲染
   - 测试用户交互（点击、输入等）
   - 测试事件触发
   - 测试插槽内容渲染
   ```typescript
   // 测试插槽内容
   it('正确渲染默认插槽内容', () => {
     const wrapper = mount(Card, {
       slots: {
         default: '<div class="test-content">测试内容</div>'
       }
     });
     
     expect(wrapper.find('.test-content').exists()).toBe(true);
     expect(wrapper.find('.test-content').text()).toBe('测试内容');
   });
   ```

4. **测试覆盖率**
   - 使用 c8 或 istanbul 收集覆盖率数据
   - 设置覆盖率目标（如行覆盖率 > 80%）
   - 在 CI/CD 流程中集成覆盖率检查
   ```bash
   # 运行测试并收集覆盖率
   pnpm test:coverage
   ```

5. **持续集成**
   - 在每次提交时运行测试
   - 使用 GitHub Actions 或其他 CI 工具
   - 设置测试失败时阻止合并
   ```yaml
   # .github/workflows/test.yml
   name: Run Tests
   
   on:
     push:
       branches: [main, develop]
     pull_request:
       branches: [main, develop]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - uses: actions/setup-node@v3
           with:
             node-version: 16
             cache: 'pnpm'
         - run: pnpm install
         - run: pnpm lint
         - run: pnpm test:unit
         - run: pnpm test:e2e:ci
   ```

#### 测试检查清单

1. **单元测试**
   - [ ] 所有工具函数有单元测试
   - [ ] 所有 Pinia Store 有单元测试
   - [ ] 所有自定义 Hooks/Composables 有单元测试
   - [ ] 测试覆盖正常路径和边缘情况

2. **组件测试**
   - [ ] 所有共享/基础组件有测试
   - [ ] 测试组件渲染、交互和事件
   - [ ] 测试组件的不同状态（加载中、错误、空数据等）
   - [ ] 测试组件的可访问性

3. **集成测试**
   - [ ] 测试关键用户流程
   - [ ] 测试组件间交互
   - [ ] 测试路由导航
   - [ ] 测试状态管理集成

4. **端到端测试**
   - [ ] 测试关键业务流程（登录、注册、核心功能）
   - [ ] 测试表单提交和验证
   - [ ] 测试页面导航和路由
   - [ ] 测试响应式布局

5. **测试维护**
   - [ ] 定期更新测试依赖
   - [ ] 重构时更新相关测试
   - [ ] 修复失败的测试
   - [ ] 定期审查测试覆盖率报告

1. **单元测试**
   - 使用 Vitest 进行单元测试
   - 测试关键业务逻辑和工具函数
   - 使用 Vue Test Utils 测试组件

2. **端到端测试**
   - 使用 Cypress 进行端到端测试
   - 测试关键用户流程
   - 模拟 API 响应

3. **测试覆盖率**
   - 设置合理的测试覆盖率目标
   - 优先测试核心功能和高风险区域
   - 集成到 CI/CD 流程中

### 文档规范

良好的文档是项目成功的关键因素之一，本项目采用以下文档规范确保知识的有效传递和维护。

#### 文档类型

1. **项目文档**
   - README.md：项目概述、安装说明、快速开始
   - CHANGELOG.md：版本更新记录
   - CONTRIBUTING.md：贡献指南
   - project_rules.md：项目规范（本文档）

2. **技术文档**
   - 架构设计文档
   - API 接口文档
   - 数据库设计文档
   - 组件库文档

3. **用户文档**
   - 用户手册
   - 常见问题解答（FAQ）
   - 操作指南

4. **开发文档**
   - 开发环境搭建指南
   - 调试与测试指南
   - 部署指南

#### 文档格式规范

1. **Markdown 格式规范**
   - 使用 Markdown 语法编写所有文档
   - 遵循一致的标题层级（# 一级标题，## 二级标题，以此类推）
   - 使用列表、表格、代码块等元素增强可读性
   - 代码块需指定语言类型
   ```markdown
   # 文档标题

   ## 二级标题

   正文内容...

   ### 三级标题

   - 列表项 1
   - 列表项 2
     - 子列表项

   | 表头 1 | 表头 2 |
   | ------ | ------ |
   | 内容 1 | 内容 2 |

   ```javascript
   // 代码示例
   function example() {
     console.log('Hello World');
   }
   ```
   ```

2. **文档结构规范**
   - 每个文档必须有明确的标题
   - 包含目录（适用于较长文档）
   - 逻辑结构清晰，从概述到细节
   - 相关内容应分组在一起
   - 使用适当的分隔符分隔不同部分

3. **图表与图片**
   - 使用 PlantUML、Mermaid 等工具创建图表
   - 图片应有适当的大小和分辨率
   - 为所有图片提供替代文本（alt text）
   - 图表和图片应有清晰的标题或说明
   ```markdown
   ![组件关系图](./images/component-diagram.png "组件关系图")

   ```mermaid
   graph TD
     A[开始] --> B{条件判断}
     B -->|是| C[处理 1]
     B -->|否| D[处理 2]
     C --> E[结束]
     D --> E
   ```
   ```

#### 代码注释规范

1. **文件头注释**
   ```typescript
   /**
    * @file 用户管理模块
    * @description 处理用户的增删改查操作
    * <AUTHOR> <<EMAIL>>
    * @created 2023-01-01
    * @updated 2023-02-15
    */
   ```

2. **函数/方法注释**
   ```typescript
   /**
    * 获取用户信息
    * @param {number} userId - 用户ID
    * @returns {Promise<User>} 用户信息对象
    * @throws {ApiError} 当API请求失败时抛出
    * @example
    * ```ts
    * const user = await getUserInfo(1);
    * console.log(user.name);
    * ```
    */
   async function getUserInfo(userId: number): Promise<User> {
     // 实现...
   }
   ```

3. **组件注释**
   ```vue
   <script setup lang="ts">
   /**
    * 用户信息卡片组件
    * @component UserCard
    * @description 显示用户基本信息的卡片组件
    * @example
    * ```vue
    * <UserCard :user="userInfo" @edit="handleEdit" />
    * ```
    */
   
   // Props 定义
   const props = defineProps<{
     /**
      * 用户信息对象
      */
     user: User;
   }>();
   
   // Emits 定义
   const emit = defineEmits<{
     /**
      * 编辑用户时触发
      * @param {number} userId - 用户ID
      */
     (e: 'edit', userId: number): void;
   }>();
   </script>
   ```

4. **复杂逻辑注释**
   ```typescript
   // 使用二分查找算法查找目标值
   // 时间复杂度: O(log n)
   // 空间复杂度: O(1)
   function binarySearch(arr: number[], target: number): number {
     let left = 0;
     let right = arr.length - 1;
     
     while (left <= right) {
       const mid = Math.floor((left + right) / 2);
       
       if (arr[mid] === target) {
         return mid; // 找到目标值，返回索引
       } else if (arr[mid] < target) {
         left = mid + 1; // 目标在右半部分
       } else {
         right = mid - 1; // 目标在左半部分
       }
     }
     
     return -1; // 未找到目标值
   }
   ```

#### API 文档规范

1. **使用 OpenAPI/Swagger 规范**
   - 使用 OpenAPI 3.0 或更高版本
   - 包含完整的请求/响应示例
   - 详细描述所有参数和返回值

2. **API 端点文档结构**
   - 端点 URL 和方法（GET, POST, PUT, DELETE 等）
   - 功能描述
   - 请求参数（路径参数、查询参数、请求体）
   - 响应格式（成功和错误情况）
   - 认证要求
   - 示例请求和响应
   - 可能的错误码和说明

3. **使用 TypeDoc 生成 TypeScript 文档**
   - 为所有公共 API 添加 JSDoc 注释
   - 使用 TypeDoc 自动生成 API 文档
   - 集成到构建流程中

#### 文档维护流程

1. **文档更新时机**
   - 新功能开发时同步更新文档
   - 修复 bug 时更新相关文档
   - 重构代码时检查并更新文档
   - 定期审查文档的准确性和完整性

2. **文档审查流程**
   - 代码审查时同时审查相关文档
   - 使用文档检查清单确保质量
   - 鼓励团队成员提供文档反馈

3. **版本控制**
   - 文档与代码放在同一仓库，共享版本历史
   - 重大更新时在文档中标注版本号
   - 保持 CHANGELOG.md 的更新

#### 文档最佳实践

1. **保持简洁明了**
   - 使用简单、直接的语言
   - 避免技术行话，必要时提供解释
   - 一个段落只表达一个主要观点

2. **使用示例**
   - 提供实际的代码示例
   - 包含常见用例和边缘情况
   - 示例应该可以直接复制运行

3. **保持更新**
   - 定期检查文档的准确性
   - 删除过时的信息
   - 添加新功能的文档

4. **考虑受众**
   - 针对不同技能水平的用户提供适当的文档
   - 为新手提供入门指南
   - 为高级用户提供深入的技术细节

#### 文档检查清单

1. **内容检查**
   - [ ] 文档内容是否准确反映当前代码
   - [ ] 是否包含所有必要的信息
   - [ ] 是否有明确的目标受众
   - [ ] 是否提供了足够的示例

2. **格式检查**
   - [ ] 是否遵循 Markdown 格式规范
   - [ ] 标题层级是否合理
   - [ ] 是否使用了适当的列表、表格等元素
   - [ ] 代码块是否指定了语言

3. **可读性检查**
   - [ ] 是否使用了简洁明了的语言
   - [ ] 是否避免了不必要的技术行话
   - [ ] 是否有逻辑清晰的结构
   - [ ] 是否有适当的目录和导航

4. **完整性检查**
   - [ ] 是否包含所有必要的文档类型
   - [ ] API 文档是否完整
   - [ ] 是否包含故障排除信息
   - [ ] 是否提供了联系方式或支持渠道

1. **代码注释**
   - 为复杂逻辑添加注释
   - 使用 JSDoc 注释函数和类
   - 记录组件的 props、events 和 slots

2. **README 文档**
   - 项目介绍和功能说明
   - 安装和运行指南
   - 开发指南和注意事项
   - 常见问题解答

3. **变更日志**
   - 记录版本更新内容
   - 遵循语义化版本规范
   - 标注破坏性变更