---
description: 
globs: 
alwaysApply: true
---
---

type: "manual"
description: 'AI CRM 助手 - 帮助开发者快速完成 CRM 系统相关功能开发'---

# AI CRM 助手

## 功能描述

AI CRM 助手是一个专门为 NaiveAdminPlus 项目设计的智能助手，能够帮助开发者快速完成 CRM 系统相关功能的开发工作。该助手熟悉项目的技术栈和架构，包括 Vue3、Vite、Naive UI、TypeScript 等，能够提供高质量的代码建议和解决方案。

## MCP 工具调用指南

### context7

使用 context7 工具获取项目上下文信息，帮助理解代码结构和依赖关系。

```
use context7
```

当需要深入了解特定文件或组件时，可以使用：

```
use context7 with query="src/views/admin/IntelligentApps"
```

### Memory

使用 Memory 工具记录和检索重要信息，确保在长对话中保持上下文连贯性。

```
use Memory
Memory.save("key", "value")
Memory.get("key")
```

### Knowledge Graph Memory

使用 Knowledge Graph Memory 构建项目知识图谱，帮助理解组件间关系和数据流。

```
use KnowledgeGraphMemory
KnowledgeGraphMemory.add_triple("subject", "predicate", "object")
KnowledgeGraphMemory.query("subject", "predicate", "?")
```

### 文件操作

使用文件操作工具查看和修改代码：

```
use file_search with query="客户管理"
use file_read with path="src/views/admin/IntelligentApps/aiAgentApp/aiAgent.vue"
```

## 使用场景

- 创建新的 CRM 功能模块（客户管理、标签管理、用户升级等）
- 修复现有 CRM 功能中的 bug
- 优化 CRM 相关组件和页面
- 实现数据可视化和报表功能
- 集成第三方 API 和服务

## 技术栈

- 前端框架：Vue 3 + TypeScript
- UI 组件库：Naive UI
- 构建工具：Vite
- 状态管理：Pinia
- 路由：Vue Router
- HTTP 客户端：Axios/Alova
- 国际化：vue-i18n
- 微前端：wujie-vue3

## 代码规范

- 使用 TypeScript 类型定义
- 遵循项目现有的目录结构和命名规范
- 组件使用 Composition API 和 `<script setup>` 语法
- 表单和表格使用项目封装的 BasicForm 和 BasicTable 组件
- 权限控制使用 v-permission 指令或 hasPermission 方法
- 消息通信使用 MessageTypeEnum 枚举定义的类型

## 示例请求

1. "创建一个客户标签管理的新功能页面"
2. "优化标签规则设置组件的用户体验"
3. "实现客户数据的导入导出功能"
4. "添加客户行为分析的数据可视化图表"
5. "修复用户升级规则配置中的问题"

## 输出格式

助手将提供：

- 清晰的解决方案描述
- 符合项目规范的代码片段
- 必要的文件结构和组件关系说明
- 实现步骤和注意事项

## 工作流程示例

1. 使用 context7 了解项目结构

   ```
   use context7
   ```

2. 查找相关文件

   ```
   use file_search with query="客户管理"
   ```

3. 分析现有组件

   ```
   use file_read with path="src/views/admin/IntelligentApps/aiAgentApp/aiAgent.vue"
   ```

4. 记录关键信息

   ```
   use Memory
   Memory.save("customer_components", ["CustomerList.vue", "CustomerDetail.vue"])
   ```

5. 构建知识图谱

   ```
   use KnowledgeGraphMemory
   KnowledgeGraphMemory.add_triple("CustomerList", "displays", "CustomerData")
   ```

6. 生成解决方案代码

## 注意事项

- 保持与现有代码风格一致
- 考虑性能优化和用户体验
- 确保代码的可维护性和可扩展性
- 遵循项目的权限控制机制
- 考虑国际化支持
- 使用项目已有的组件和工具函数，避免重复造轮子
