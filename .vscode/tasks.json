{"version": "2.0.0", "tasks": [{"type": "npm", "script": "dev", "label": "pnpm: dev", "detail": "开发环境", "problemMatcher": ["$vite"], "isBackground": true}, {"type": "npm", "script": "build", "label": "pnpm: build", "detail": "生产环境打包"}, {"type": "npm", "script": "build:test", "label": "pnpm: build:test", "detail": "测试环境打包"}, {"type": "npm", "script": "lint:eslint", "label": "pnpm: lint:eslint", "detail": "ESLint 代码检查"}, {"type": "npm", "script": "lint:prettier", "label": "pnpm: lint:prettier", "detail": "Prettier 代码格式化"}, {"type": "npm", "script": "update:iconfont", "label": "pnpm: update:iconfont", "detail": "更新项目图标字体"}]}