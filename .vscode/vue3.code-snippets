{
  // Place your qimall_plus_admin 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }

  "Vue3 Template": {
    "scope": "vue",
    "prefix": "v3",
    "body": [
      "<template>",
      "  <div>",
      "    $1",
      "  </div>",
      "</template>",
      "",
      "<script setup>",
      "$2",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "$2",
      "</style>",
    ],
    "description": "Vue 3 基础模板",
  },

  "Vue3 Setup Template": {
    "scope": "vue",
    "prefix": "v3s",
    "body": [
      "<template>",
      "  <div>",
      "    $1",
      "  </div>",
      "</template>",
      "",
      "<script setup>",
      "import { ref, onMounted } from 'vue'",
      "",
      "$2",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "$3",
      "</style>",
    ],
    "description": "Vue 3 Setup 语法模板",
  },

  "Vue3 Props": {
    "scope": "javascript,typescript",
    "prefix": "vprops",
    "body": [
      "const props = defineProps({",
      "  ${1:propName}: {",
      "    type: ${2:String},",
      "    required: ${3:true},",
      "    default: ${4:null}",
      "  }",
      "})",
    ],
    "description": "Vue 3 Props 定义",
  },

  "Vue3 Emits": {
    "scope": "javascript,typescript",
    "prefix": "vemit",
    "body": ["const emit = defineEmits(['${1:update}'])"],
    "description": "Vue 3 Emits 定义",
  },

  "Vue3 Ref": {
    "scope": "javascript,typescript",
    "prefix": "vref",
    "body": ["const ${1:name} = ref(${2:initialValue})"],
    "description": "Vue 3 ref 响应式变量",
  },
}
