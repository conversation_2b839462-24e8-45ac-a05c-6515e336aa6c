{
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.suggestSelection": "first",
  "editor.cursorBlinking": "expand",
  "cSpell.enabled": false,
  "files.eol": "\n",
  // 默认格式化工具选择prettier
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // 保存的时候自动格式化
  "editor.formatOnSave": true,
  //开启自动修复
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  // 配置stylelint检查的文件类型范围
  "stylelint.validate": ["css", "scss", "vue", "html"], // 与package.json的scripts对应
  "stylelint.enable": true,
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dotenv]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 配置语言的文件关联
  "files.associations": {
    "pages.json": "jsonc", // pages.json 可以写注释
    "manifest.json": "jsonc" // manifest.json 可以写注释
  },
  "cSpell.ignorePaths": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    ".vscode/**",
    "**/*.json",
    "*.config.ts",
    ".env*",
    "**/enums/**",
    "**/platform/permission/**"
  ],
  "cSpell.words": [
    "climblee",
    "commitlint",
    "dcloudio",
    "iconfont",
    "qrcode",
    "refresherrefresh",
    "scrolltolower",
    "tabbar",
    "mall",
    "unocss",
    "WechatMiniprogram",
    "wechat",
    "uniapp",
    "WEIXIN",
    "pinia",
    "vueuse",
    "vuex",
    "composables",
    "miniprogram",
    "Symbian",
    "fullpath",
    "stylelintrc",
    "Zhanzr",
    "dompurify",
    "alova",
    "unref",
    "hoverable",
    "zhanwei",
    "sider",
    "datetimerange",
    "maxlength",
    "qiniu",
    "jsapi",
    "noopener",
    "noreferrer",
    "vuedraggable",
    "vicons",
    "cascader",
    "daterange"
  ],
  "cSpell.ignoreRegExpList": [
    "/icon\\-[a-zA-Z0-9\\-]+/", // 忽略 icon- 开头的图标类名
    "/name=\"[^\"]*\"/", // 忽略 name 属性中的内容
    "/https?:\\/\\/[^\\s]+/", // 忽略完整的 URL
    "/\\/[a-zA-Z0-9%?=&_/.-]+/", // 忽略类似 /pages/login?redirect=... 的路径
    "/import { [^}]+ } from '[^']+'/", // 忽略 import { ... } from '...' 的语句
    "/<\/?n-[a-zA-Z0-9\\-]+/" // 忽略 n- 开头的类名
  ],
  "typescript.tsdk": "node_modules\\typescript\\lib",
  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "$(capture).test.ts, $(capture).test.tsx",
    "*.tsx": "$(capture).test.ts, $(capture).test.tsx",
    "*.env": "$(capture).env.*",
    "CHANGELOG.md": "CHANGELOG*",
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,yarn.lock,LICENSE,CHANGELOG*,CNAME,.npmrc,.gitpod.yml,.gitattributes,.gitignore,.eslintrc-auto-import.json,.eslintignore,.prettierignore,.stylelintignore,.eslintrc.*,.commitlintrc.*,.prettierrc.*,prettier.config.*,.stylelintrc.*,stylelint.config.*,.editorconfig,postcss.config.*,tailwind.config.*",
    "vite.config.ts": "*.config.ts",
    "*.vue": "$(capture).*.ts,$(capture).*.js,$(capture).scss"
  },
  "[properties]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "todohighlight.include": [
    "**/*.js",
    "**/*.jsx",
    "**/*.ts",
    "**/*.tsx",
    "**/*.html",
    "**/*.css",
    "**/*.scss",
    "**/*.vue"
  ],
  "todohighlight.exclude": [
    "**/node_modules/**",
    "**/bower_components/**",
    "**/dist/**",
    "**/build/**",
    "**/.vscode/**",
    "**/.github/**",
    "**/_output/**",
    "**/*.min.*",
    "**/*.map",
    "**/.next/**",
    "src/views/kong/**",
    "src/locales/lang/en/kong.ts",
    "src/locales/lang/zh-CN/kong.ts"
  ],
  "[ignore]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  // i18n Ally 配置 - 使用扁平化 JSON 文件
  "i18n-ally.localesPaths": ["src/locales/flat"],
  "i18n-ally.keystyle": "flat",
  "i18n-ally.sortKeys": true,
  "i18n-ally.namespace": false,
  "i18n-ally.enabledParsers": ["json"],
  "i18n-ally.sourceLanguage": "zh-CN",
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.enabledFrameworks": ["vue", "vue-i18n"],
  "i18n-ally.pathMatcher": "{locale}.json",
  "i18n-ally.extract.keygenStyle": "camelCase",
  "i18n-ally.annotations": true,
  "i18n-ally.autoDetection": false,
  "i18n-ally.dirStructure": "file",
  "i18n-ally.fullReloadOnChanged": true,
  "i18n-ally.usage.scanningIgnore": [
    "node_modules/**",
    "dist/**",
    "build/**",
    "src/locales/lang/**",
    "scripts/**",
    "tools/**"
  ],
  "i18n-ally.regex.key": "(?:['\"`])([^'\"`]+)(?:['\"`])",
  "i18n-ally.regex.usageMatchAppend": [
    "\\bt\\s*\\(\\s*['\"`]({key})['\"`]",
    "\\$t\\s*\\(\\s*['\"`]({key})['\"`]"
  ],
  "i18n-ally.review.enabled": true,
  "i18n-ally.editor.preferEditor": true,
  "vue-i18n.i18nPaths": "src/locales,src/locales/flat,src/views/template/i18n",
  "Codegeex.RepoIndex": true
}
