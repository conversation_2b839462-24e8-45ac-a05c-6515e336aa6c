#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 动态生成 SCSS 样式文件的脚本
 * 从 designSetting.ts 中读取 colorMap 并生成对应的 SCSS 变量和工具类
 */

// 配置
const CONFIG = {
  // 输入文件路径
  inputFiles: {
    designSetting: path.resolve(__dirname, '../src/settings/designSetting.ts'),
    projectSetting: path.resolve(__dirname, '../src/settings/projectSetting.ts'),
  },
  // 输出目录
  outputDir: path.resolve(__dirname, '../src/styles/generated'),
  // 输出文件
  outputFiles: {
    variables: 'variables.scss',
    utilities: 'utilities.scss',
    index: 'index.scss',
  },
}

/**
 * 从 TypeScript 文件中提取项目设置
 */
function extractProjectSettings(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8')

    // 找到 baseSettings 的开始位置
    const startMatch = content.match(/const baseSettings[^=]*= \{/)
    if (!startMatch) {
      console.warn('未找到 baseSettings 定义，跳过项目设置提取')
      return {}
    }

    const startIndex = startMatch.index + startMatch[0].length
    let braceCount = 1
    let endIndex = startIndex

    // 手动匹配大括号，找到正确的结束位置
    for (let i = startIndex; i < content.length && braceCount > 0; i++) {
      if (content[i] === '{') {
        braceCount++
      } else if (content[i] === '}') {
        braceCount--
      }
      endIndex = i
    }

    if (braceCount !== 0) {
      console.warn('baseSettings 对象格式不正确，跳过项目设置提取')
      return {}
    }

    const settingContent = content.slice(startIndex, endIndex)
    const settings = {}

    // 解析每一行设置定义
    const lines = settingContent.split('\n')

    for (const line of lines) {
      // 匹配设置项：borderRadius: 4, fontSize: 14, 等
      const match = line.match(/([a-zA-Z][a-zA-Z0-9]*)\s*:\s*([^,]+),?/)
      if (match) {
        const [, key, value] = match
        let cleanValue = value.trim()

        // 移除尾部逗号和注释
        cleanValue = cleanValue
          .replace(/,.*$/, '')
          .replace(/\/\/.*$/, '')
          .trim()

        // 只保留数值类型的设置（用于生成 CSS）
        if (/^\d+(\.\d+)?$/.test(cleanValue)) {
          settings[key] = cleanValue
        }
      }
    }

    return settings
  } catch (error) {
    console.warn('读取项目设置失败:', error.message)
    return {}
  }
}

/**
 * 从 TypeScript 文件中提取 colorMap
 */
function extractColorMap(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8')

    // 找到 colorMap 的开始位置
    const startMatch = content.match(/export const colorMap = \{/)
    if (!startMatch) {
      throw new Error('未找到 colorMap 导出')
    }

    const startIndex = startMatch.index + startMatch[0].length
    let braceCount = 1
    let endIndex = startIndex

    // 手动匹配大括号，找到正确的结束位置
    for (let i = startIndex; i < content.length && braceCount > 0; i++) {
      if (content[i] === '{') {
        braceCount++
      } else if (content[i] === '}') {
        braceCount--
      }
      endIndex = i
    }

    if (braceCount !== 0) {
      throw new Error('colorMap 对象格式不正确，大括号不匹配')
    }

    const colorMapContent = content.slice(startIndex, endIndex)
    const colorMap = {}

    // 解析每一行颜色定义
    const lines = colorMapContent.split('\n')

    for (const line of lines) {
      // 匹配多种格式：
      // 'key': 'value',
      // 'key': '#value',
      // key: 'value',
      // 'key': `${variable}px`,
      // 'key': 'rgba(0, 0, 0, 0.85)',
      const match = line.match(/['"`]?([^'"`:\s]+)['"`]?\s*:\s*(.*?)(?:,\s*$|$)/)
      if (match) {
        const [, key, value] = match
        // 清理值，移除引号和尾部逗号
        let cleanValue = value.trim()

        // 移除开头和结尾的引号
        if (
          (cleanValue.startsWith("'") && cleanValue.endsWith("'")) ||
          (cleanValue.startsWith('"') && cleanValue.endsWith('"')) ||
          (cleanValue.startsWith('`') && cleanValue.endsWith('`'))
        ) {
          cleanValue = cleanValue.slice(1, -1)
        }

        // 移除尾部的逗号
        cleanValue = cleanValue.replace(/,\s*$/, '')

        // 只有当值不为空且不是注释时才添加
        if (cleanValue && !cleanValue.startsWith('//') && cleanValue !== '') {
          colorMap[key] = cleanValue
        }
      }
    }

    return colorMap
  } catch (error) {
    console.error('读取 colorMap 失败:', error.message)
    process.exit(1)
  }
}

/**
 * 生成 SCSS 变量文件
 */
function generateVariables(colorMap, projectSettings = {}) {
  let content = `// 自动生成的 SCSS 变量文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

// ===== 项目设置变量 =====
`

  // 生成项目设置变量
  for (const [key, value] of Object.entries(projectSettings)) {
    const variableName = key.replace(/([A-Z])/g, '-$1').toLowerCase()
    content += `$${variableName}: ${value}px;\n`
  }

  content += `
// ===== 颜色变量 =====
`

  // 生成 SCSS 变量
  for (const [key, value] of Object.entries(colorMap)) {
    const variableName = key.replace(/['"]/g, '')
    content += `$color-${variableName}: ${value};\n`
  }

  content += `
// ===== 项目设置映射 =====
$project-settings: (
`

  // 生成项目设置映射
  for (const [key] of Object.entries(projectSettings)) {
    const variableName = key.replace(/([A-Z])/g, '-$1').toLowerCase()
    content += `  '${key}': $${variableName},\n`
  }

  content += `);

// ===== 颜色映射 =====
$color-map: (
`

  // 生成颜色映射
  for (const [key] of Object.entries(colorMap)) {
    const variableName = key.replace(/['"]/g, '')
    content += `  '${variableName}': $color-${variableName},\n`
  }

  content += `);\n`

  return content
}

/**
 * 生成工具类 SCSS 文件
 */
function generateUtilities(colorMap) {
  let content = `// 自动生成的工具类 SCSS 文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

// ===== 文本颜色工具类 =====
`

  // 生成文本颜色工具类
  for (const [key, value] of Object.entries(colorMap)) {
    const className = key.replace(/['"]/g, '')
    content += `.text-${className} {\n  color: ${value} !important;\n}\n`
  }

  content += `\n// ===== 背景颜色工具类 =====
`

  // 生成背景颜色工具类
  for (const [key, value] of Object.entries(colorMap)) {
    const className = key.replace(/['"]/g, '')
    content += `.bg-${className} {\n  background-color: ${value} !important;\n}\n`
  }

  content += `\n// ===== 边框颜色工具类 =====
`

  // 生成边框颜色工具类
  for (const [key, value] of Object.entries(colorMap)) {
    const className = key.replace(/['"]/g, '')
    content += `.border-${className} {\n  border-color: ${value} !important;\n}\n`
  }

  content += `\n// ===== 阴影颜色工具类 =====
`

  // 生成阴影颜色工具类
  for (const [key, value] of Object.entries(colorMap)) {
    const className = key.replace(/['"]/g, '')
    content += `.shadow-${className} {\n  box-shadow: 0 2px 8px ${value}33 !important;\n}\n`
  }

  return content
}

/**
 * 生成索引文件
 */
function generateIndex(projectSettings = {}) {
  console.log(projectSettings)

  return `// 自动生成的样式索引文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

@use './variables.scss' as *;
@use './utilities.scss' as *;
@use 'sass:map';

// 导出颜色映射函数
@function get-color($key) {
  @if map.has-key($color-map, $key) {
    @return map.get($color-map, $key);
  } @else {
    @warn "颜色 '#{$key}' 不存在于 $color-map 中";
    @return null;
  }
}

// 导出项目设置函数
@function get-setting($key) {
  @if map.has-key($project-settings, $key) {
    @return map.get($project-settings, $key);
  } @else {
    @warn "设置 '#{$key}' 不存在于 $project-settings 中";
    @return null;
  }
}

// Mixin: 应用主题颜色
@mixin theme-color($property, $color-key) {
  #{$property}: get-color($color-key);
}

// Mixin: 应用项目设置
@mixin apply-setting($property, $setting-key) {
  #{$property}: get-setting($setting-key);
}

// 常用的项目设置 Mixin
@mixin border-radius($radius: null) {
  @if $radius {
    border-radius: $radius;
  } @else {
    border-radius: get-setting('borderRadius');
  }
}
`
}

/**
 * 确保目录存在
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`✅ 创建目录: ${dirPath}`)
  }
}

/**
 * 写入文件
 */
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf-8')
    console.log(`✅ 生成文件: ${filePath}`)

    // 验证文件是否真的写入成功
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath)
      console.log(`   📊 文件大小: ${stats.size} bytes`)
    } else {
      console.error(`❌ 文件写入失败: ${filePath}`)
    }
  } catch (error) {
    console.error(`❌ 写入文件时出错: ${filePath}`, error.message)
  }
}

/**
 * 生成所有样式文件
 */
function generateAllFiles() {
  // 1. 提取 colorMap
  console.log('📖 读取 colorMap...')
  const colorMap = extractColorMap(CONFIG.inputFiles.designSetting)
  console.log(`✅ 成功读取 ${Object.keys(colorMap).length} 个颜色定义`)

  // 2. 提取项目设置
  console.log('📖 读取项目设置...')
  const projectSettings = extractProjectSettings(CONFIG.inputFiles.projectSetting)
  console.log(`✅ 成功读取 ${Object.keys(projectSettings).length} 个项目设置`)

  // 3. 确保输出目录存在
  ensureDir(CONFIG.outputDir)

  // 4. 生成文件
  console.log('📝 生成 SCSS 文件...')

  // 生成变量文件
  const variablesContent = generateVariables(colorMap, projectSettings)
  writeFile(path.join(CONFIG.outputDir, CONFIG.outputFiles.variables), variablesContent)

  // 生成工具类文件
  const utilitiesContent = generateUtilities(colorMap)
  writeFile(path.join(CONFIG.outputDir, CONFIG.outputFiles.utilities), utilitiesContent)

  // 生成索引文件
  const indexContent = generateIndex(projectSettings)
  writeFile(path.join(CONFIG.outputDir, CONFIG.outputFiles.index), indexContent)

  return { colorMap, projectSettings }
}

/**
 * 监听模式
 */
function watchMode() {
  console.log('👀 启动监听模式...')
  console.log(`📁 监听文件: ${CONFIG.inputFiles.designSetting}`)
  console.log(`📁 监听文件: ${CONFIG.inputFiles.projectSetting}`)

  // 初始生成
  generateAllFiles()

  // 监听设计设置文件变化
  fs.watchFile(CONFIG.inputFiles.designSetting, { interval: 1000 }, (curr, prev) => {
    if (curr.mtime !== prev.mtime) {
      console.log('\n🔄 检测到设计设置文件变化，重新生成...')
      try {
        generateAllFiles()
        console.log('✅ 重新生成完成\n')
      } catch (error) {
        console.error('❌ 重新生成失败:', error.message)
      }
    }
  })

  // 监听项目设置文件变化
  fs.watchFile(CONFIG.inputFiles.projectSetting, { interval: 1000 }, (curr, prev) => {
    if (curr.mtime !== prev.mtime) {
      console.log('\n🔄 检测到项目设置文件变化，重新生成...')
      try {
        generateAllFiles()
        console.log('✅ 重新生成完成\n')
      } catch (error) {
        console.error('❌ 重新生成失败:', error.message)
      }
    }
  })

  console.log('\n💡 监听模式已启动，按 Ctrl+C 退出')
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2)
  const isWatchMode = args.includes('--watch') || args.includes('-w')

  console.log('🚀 开始生成 SCSS 样式文件...\n')

  if (isWatchMode) {
    watchMode()
  } else {
    generateAllFiles()

    console.log('\n🎉 SCSS 样式文件生成完成!')
    console.log(`📁 输出目录: ${CONFIG.outputDir}`)
    console.log('📋 生成的文件:')
    console.log(`   - ${CONFIG.outputFiles.variables} (SCSS 变量)`)
    console.log(`   - ${CONFIG.outputFiles.utilities} (工具类)`)
    console.log(`   - ${CONFIG.outputFiles.index} (索引文件)`)

    console.log('\n💡 使用方法:')
    console.log('   在你的 SCSS 文件中导入: @import "~/styles/generated";')
    console.log('   使用变量: color: $color-blue-deep;')
    console.log('   使用函数: color: get-color("blue-deep");')
    console.log('   使用工具类: <div class="text-blue-deep bg-white">')
    console.log('\n🔧 高级用法:')
    console.log('   监听模式: npm run generate:styles -- --watch')
  }
}

// 运行脚本
const isMainModule = import.meta.url === new URL(process.argv[1], 'file:').href
if (isMainModule) {
  main()
}

export { extractColorMap, generateVariables, generateUtilities, generateIndex }
