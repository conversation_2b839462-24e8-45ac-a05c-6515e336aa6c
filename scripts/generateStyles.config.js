/**
 * 样式生成脚本配置文件
 */

const path = require('path')

module.exports = {
  // 输入文件配置
  input: {
    // designSetting.ts 文件路径
    designSettingFile: path.resolve(__dirname, '../src/settings/designSetting.ts'),
    // 要提取的对象名称
    extractObjects: ['colorMap'],
  },

  // 输出配置
  output: {
    // 输出目录
    dir: path.resolve(__dirname, '../src/styles/generated'),
    // 输出文件名
    files: {
      variables: 'variables.scss',
      utilities: 'utilities.scss',
      index: 'index.scss',
    },
  },

  // 生成选项
  generation: {
    // 是否生成 SCSS 变量
    generateVariables: true,
    // 是否生成工具类
    generateUtilities: true,
    // 是否生成索引文件
    generateIndex: true,
    // 工具类前缀
    utilityPrefix: '',
    // 是否使用 !important
    useImportant: true,
    // 变量前缀
    variablePrefix: 'color-',
  },

  // 工具类配置
  utilities: {
    // 文本颜色工具类
    textColor: {
      enabled: true,
      prefix: 'text-',
      property: 'color',
    },
    // 背景颜色工具类
    backgroundColor: {
      enabled: true,
      prefix: 'bg-',
      property: 'background-color',
    },
    // 边框颜色工具类
    borderColor: {
      enabled: true,
      prefix: 'border-',
      property: 'border-color',
    },
    // 阴影颜色工具类
    shadowColor: {
      enabled: true,
      prefix: 'shadow-',
      property: 'box-shadow',
      template: '0 2px 8px {color}33', // {color} 会被替换为实际颜色值
    },
    // 自定义工具类
    custom: [
      {
        enabled: true,
        prefix: 'hover-text-',
        property: 'color',
        selector: '&:hover',
        description: '悬停文本颜色',
      },
      {
        enabled: true,
        prefix: 'focus-border-',
        property: 'border-color',
        selector: '&:focus',
        description: '聚焦边框颜色',
      },
    ],
  },

  // 文件头部注释模板
  templates: {
    header: `// 自动生成的样式文件
// 请勿手动修改，运行 npm run generate:styles 重新生成
// 生成时间: {timestamp}
// 源文件: {sourceFile}

`,
    variableComment: '// ===== {section} =====\n',
    utilityComment: '// ===== {section} =====\n',
  },

  // 监听模式配置
  watch: {
    // 监听间隔（毫秒）
    interval: 1000,
    // 是否在启动时立即生成
    generateOnStart: true,
    // 是否显示详细日志
    verbose: true,
  },
}
