import https from 'node:https'
import fs from 'node:fs'
import path from 'node:path'
import readline from 'node:readline'
import { fileURLToPath } from 'node:url'
import prettier from 'prettier'

// 获取 __dirname
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 创建读取输入的接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// 提示用户输入CSS链接
rl.question('请输入iconfont的CSS链接: ', (cssUrl) => {
  // 下载CSS内容
  https
    .get(cssUrl, (response) => {
      let cssData = ''

      response.on('data', (chunk) => {
        cssData += chunk
      })

      response.on('end', async () => {
        try {
          // 替换所有的 //at.alicdn.com 为 https://at.alicdn.com
          let updatedCssData = cssData.replace(/\/\/at.alicdn\.com/g, 'https://at.alicdn.com')

          // 使用 Prettier 格式化代码
          updatedCssData = await prettier.format(updatedCssData, {
            parser: 'css',
            singleQuote: true,
            printWidth: 100,
            tabWidth: 2,
          })

          const filePath = path.join(__dirname, '../src/styles/iconfont.scss')

          // 确保目录存在
          const dir = path.dirname(filePath)
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true })
          }

          // 写入到文件
          fs.writeFile(filePath, updatedCssData, 'utf8', (err) => {
            if (err) {
              console.error('写入文件时出错:', err)
            } else {
              console.log(`CSS内容已成功更新到 ${filePath}`)
            }
          })
        } catch (error) {
          console.error('处理文件时出错:', error)
        }
      })
    })
    .on('error', (err) => {
      console.error('获取CSS文件时出错:', err)
    })

  // 关闭输入接口
  rl.close()
})
