// @ts-check
import chalk from 'chalk'
import fs from 'node:fs'
import path from 'node:path'
import { execSync } from 'node:child_process'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

function printError(message, commitMsg) {
  console.error(chalk.red(`🚨 错误提示：${message}`))
  console.error(chalk.dim(`\n📝 本次提交内容：${commitMsg}\n`))
}

function printGuide() {
  console.error(
    `${chalk.red(`❌ 提交信息格式错误, 请按照规范格式提交.`)}\n\n` +
      `${chalk.yellow('💯 提交信息的标准格式为：')}\n` +
      `${chalk.bold('<type>(<scope>): <subject> [可选: (<task type> #<task id>)]')}\n\n` +
      `${chalk.blue('- <type> 是必填项，表示提交的类型，必须是以下类型之一:')}\n` +
      `${chalk.cyan(
        '  feat, fix, docs, dx, style, refactor, perf, test, workflow, build, ci, chore, types, wip, release',
      )}\n\n` +
      `${chalk.green('- <scope> 是可选项，表示影响的模块或功能.')}\n` +
      `${chalk.blue('- <subject> 是必填项，简要描述提交内容，长度应在 1 到 50 个字符之间.')}\n` +
      `${chalk.green(
        '- <task type> 和 <task id> 是可选项，用于关联禅道，格式为 "(bug #123)".',
      )}\n\n` +
      `${chalk.yellow('例如：')}\n` +
      `${chalk.cyan(`"feat: 新增 mall 插件"`)}\n` +
      `${chalk.cyan(`"fix(v-model): handle events on blur (bug #28)"`)}\n\n` +
      `${chalk.blue('🔗 详细规范请参考：')}${chalk.underline(
        chalk.cyan('https://www.yuque.com/qijianshijituan/swm3y1/xn8077xi9u9r4ngn'),
      )}\n`,
  )
}

// 检查是否有暂存的文件
try {
  const stagedFiles = execSync('git diff --staged --name-only', {
    encoding: 'utf-8',
    stdio: ['pipe', 'pipe', 'ignore'], // 忽略标准错误输出
  })
  if (!stagedFiles.trim()) {
    process.exit(0) // 如果没有暂存文件，直接退出，不显示任何信息
  }
} catch (err) {
  // 忽略错误，继续执行
}

// 获取 commit 消息
const msgPath = path.resolve(__dirname, '../.git/COMMIT_EDITMSG')
try {
  const commitMsg = fs.readFileSync(msgPath, 'utf-8').trim().split('\n\n')[0]

  // 合法的提交消息格式
  const commitRE =
    /^(revert: )?(feat|fix|docs|dx|style|refactor|perf|test|workflow|build|ci|chore|types|wip|release)(\(.+\))?: .{1,50}$/
  const potentialTaskInfoRE =
    /((?:bug|story|task|todo|testcase|doc|execution|product).#?\d*)|(#\d*)/
  const commitWithTaskRE = / \((bug|story|task|todo|testcase|doc|execution|product) #[0-9]+\)$/
  const containsTaskInfoRE = /(bug|story|task|todo|testcase|doc|execution|product) #[0-9]+/

  if (commitMsg.startsWith('Merge')) {
    console.log(chalk.green(`🎉 合并提交信息，跳过检查！`))
    process.exit(0)
  }

  if (!commitRE.test(commitMsg)) {
    const typeMatch = commitMsg.match(
      /^(revert: )?(feat|fix|docs|dx|style|refactor|perf|test|workflow|build|ci|chore|types|wip|release)/,
    )
    const scopeMatch = commitMsg.match(/\(.+\)/)
    const colonSpaceMatch = commitMsg.match(/:\S/)
    const subjectMatch = commitMsg.match(/: .{1,50}/)

    if (!typeMatch) {
      printError('类型(type)不符合要求', commitMsg)
    } else if (scopeMatch && (!scopeMatch[0].startsWith('(') || !scopeMatch[0].endsWith(')'))) {
      printError('作用范围(scope)格式错误，应使用括号包裹。例如："(模块名称)".', commitMsg)
    } else if (colonSpaceMatch) {
      printError('冒号后面必须跟一个空格。例如："fix(scope): 描述".', commitMsg)
    } else if (!subjectMatch) {
      printError(
        '描述(subject)长度应在 1 到 50 个字符之间，且以 ": " 开头。请确保描述简洁明了.',
        commitMsg,
      )
    }

    printGuide()
    process.exit(1)
  }

  if (potentialTaskInfoRE.test(commitMsg)) {
    if (containsTaskInfoRE.test(commitMsg) && !commitWithTaskRE.test(commitMsg)) {
      const missingType = commitMsg.match(
        /(bug|story|task|todo|testcase|doc|execution|product).*#?\d*/,
      )
      const missingId = commitMsg.match(
        /(bug|story|task|todo|testcase|doc|execution|product).*#[0-9]+/,
      )
      const missingBrackets = commitMsg.match(
        /\((bug|story|task|todo|testcase|doc|execution|product).*#[0-9]+\)/,
      )
      const missingSpace = commitMsg.match(
        /\((bug|story|task|todo|testcase|doc|execution|product).#[0-9]+\)/,
      )
      const missingSpaceBefore = commitMsg.match(
        / \((bug|story|task|todo|testcase|doc|execution|product).#[0-9]+\)/,
      )

      if (!missingType) {
        printError('禅道信息缺少类型。例如：(bug #123).', commitMsg)
      } else if (!missingId) {
        printError(
          '禅道信息缺少编号或不符合规范\n正确的编号格式应为 "#123"，请确保编号前有 "#" 符号并且是有效的数字.',
          commitMsg,
        )
      } else if (!missingBrackets) {
        printError(
          '禅道信息缺少括号.禅道信息应被括号包围，例如：(<task type> #<task id>)',
          commitMsg,
        )
      } else if (!missingSpace) {
        printError(
          '禅道类型和禅道ID之间必须有且只有一个空格.正确的格式应为 "(bug #123)"',
          commitMsg,
        )
      } else if (!missingSpaceBefore) {
        printError(
          `禅道信息前必须有一个空格.禅道信息应与提交描述之间保持一个空格，例如："fix: 修复问题 (bug #123)"`,
          commitMsg,
        )
      }

      printGuide()
      process.exit(1)
    }
  }

  console.log(chalk.green(`🎉 提交信息格式正确！`))
} catch (err) {
  console.error(chalk.red('读取提交信息失败，请重试'))
  process.exit(1)
}
