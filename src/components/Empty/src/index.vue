<template>
  <div class="empty-block">
    <img v-if="iconComponent" :src="iconComponent" class="empty-icon" />
    <span class="empty-text" v-if="emptyText">{{ emptyText }}</span>
    <span class="empty-text" v-if="emptyDescription">{{ emptyDescription }}</span>
    <n-button v-if="buttonText" style="margin-top: 16px" type="primary" @click="onButtonClick">
      <template #icon>
        <PlusOutlined />
      </template>
      {{ buttonText }}
    </n-button>
    <slot name="customText"></slot>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, computed, PropType } from 'vue'
  import { PlusOutlined } from '@vicons/antd'
  import msgEmpty from '@/assets/images/empty/msgEmpty.png'
  import searchEmpty from '@/assets/images/empty/searchEmpty.png'
  import taskEmpty from '@/assets/images/empty/taskEmpty.png'
  import structureEmpty from '@/assets/images/empty/structureEmpty.png'

  const iconMap = {
    msg: msgEmpty,
    search: searchEmpty,
    task: taskEmpty,
    structure: structureEmpty,
  }

  const props = defineProps({
    icon: {
      type: String,
      default: 'msg',
    },
    emptyText: {
      type: String,
      default: '暂无数据',
    },
    emptyDescription: {
      type: String,
      default: '',
    },
    buttonText: {
      type: String,
      default: '',
    },
    buttonIcon: {
      type: Boolean,
      default: false,
    },
    onButtonClick: {
      type: Function as PropType<((e?: MouseEvent) => void) | null>,
      default: null,
    },
  })

  const iconComponent = computed(() => iconMap[props.icon])
</script>

<style scoped lang="scss">
  .empty-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    .empty-icon {
      width: 140px;
      margin-bottom: 12px;
      font-size: 48px;
      color: #e6e6e6;
      object-fit: contain;
    }
    .empty-text {
      margin-bottom: 8px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
</style>
