<template>
  <div
    :class="containerClass"
    :style="containerStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <svg
      :class="svgClass"
      :style="svgStyle"
      :color="currentColor"
      :width="width"
      :height="height"
      :fill="fill"
      :aria-hidden="ariaHidden"
      v-bind="$attrs"
    >
      <use :xlink:href="xlinkHref" />
    </svg>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'

  interface Props {
    /** SVG图标的xlink引用 */
    xlinkHref: string
    /** SVG的class */
    class?: string | string[]
    /** SVG的填充颜色 */
    color?: string
    /** 鼠标悬浮时的颜色 */
    hoverColor?: string
    /** 鼠标悬浮时的背景颜色 */
    hoverBackgroundColor?: string
    /** SVG的样式对象 */
    style?: Record<string, any>
    /** SVG宽度 */
    width?: string | number
    /** SVG高度 */
    height?: string | number
    /** SVG填充颜色 */
    fill?: string
    /** 是否隐藏aria属性 */
    ariaHidden?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    ariaHidden: true,
    fill: 'currentColor',
  })

  const isHovered = ref(false)

  const currentColor = computed(() => {
    if (isHovered.value && props.hoverColor) {
      return props.hoverColor
    }
    return props.color
  })

  const containerStyle = computed(() => {
    const baseStyle = props.style || {}
    const hoverStyle: Record<string, any> = { ...baseStyle }

    if (isHovered.value && props.hoverBackgroundColor) {
      hoverStyle.backgroundColor = props.hoverBackgroundColor
    }

    return hoverStyle
  })

  const svgStyle = computed(() => {
    return {}
  })

  const containerClass = computed(() => {
    if (typeof props.class === 'string') {
      return props.class
    }
    if (Array.isArray(props.class)) {
      return props.class.join(' ')
    }
    return ''
  })

  const svgClass = computed(() => {
    return ''
  })

  const handleMouseEnter = () => {
    isHovered.value = true
  }

  const handleMouseLeave = () => {
    isHovered.value = false
  }
</script>

<style scoped>
  div {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px;
    border-radius: 2px;
    transition: background-color 0.2s ease;
  }

  svg {
    display: inline-block;
    vertical-align: middle;
    transition: color 0.2s ease;
  }
</style>
