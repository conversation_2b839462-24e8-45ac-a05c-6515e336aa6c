import { h, defineComponent, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getImageUrl } from '@/utils/assetUtils'

/**
 * 图片图标组件
 * 支持普通状态和激活状态的图标切换
 * 自动检测Naive UI菜单的选中状态
 */
export const ImageIcon = defineComponent({
  name: 'ImageIcon',
  props: {
    src: {
      type: String,
      required: true,
    },
    activeSrc: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '16px',
    },
    height: {
      type: String,
      default: '16px',
    },
    active: {
      type: Boolean,
      default: false,
    },
    menuKey: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    // 获取当前路由信息
    const route = useRoute()

    // 计算当前是否应该显示激活状态的图标
    const isActive = computed(() => {
      // 如果明确传入了active属性，优先使用
      if (props.active) return true

      // 如果提供了menuKey，通过路由名称判断是否选中
      if (props.menuKey && route.name) {
        // 精确匹配
        if (route.name === props.menuKey) {
          return true
        }

        // 支持层级匹配：如果当前路由名称包含menuKey作为前缀
        // 例如：menuKey为'leads'，当前路由为'leads_list'或'leads_detail'时也应该激活
        if (typeof route.name === 'string' && route.name.startsWith(props.menuKey + '_')) {
          return true
        }

        // 支持路径匹配：如果当前路径包含menuKey
        if (route.path.includes(`/${props.menuKey}`)) {
          return true
        }
      }

      // 如果在菜单上下文中，尝试获取选中状态
      // 注意：这里需要根据实际的菜单上下文结构来判断
      // 暂时返回false，后续可以根据实际情况调整
      return false
    })

    return () => {
      // 根据激活状态选择图标
      const currentSrc = isActive.value && props.activeSrc ? props.activeSrc : props.src

      return h('img', {
        src: getImageUrl(currentSrc),
        style: {
          width: props.width,
          height: props.height,
          display: 'inline-block',
          verticalAlign: 'middle',
        },
      })
    }
  },
})

/**
 * 渲染图片图标的函数，自动适配Naive UI n-menu的icon渲染参数
 * @param src 普通状态下的图标路径
 * @param activeSrc 激活状态下的图标路径
 * @param menuKey 菜单项的key，用于判断是否选中
 * @param props 其他属性
 * @returns 渲染函数，支持Naive UI n-menu的icon参数
 */
export function renderImageIcon(src: string, activeSrc?: string, menuKey?: string, props = {}) {
  // 返回一个函数，该函数会被Naive UI调用
  // Naive UI会传递一些参数，包括图标大小等
  return (naiveUIProps?: any) => {
    // 从Naive UI传递的参数中提取图标大小
    const iconSize = naiveUIProps?.style?.fontSize || '22px'

    // 创建一个响应式的图标组件，它会根据菜单的选中状态自动切换图标
    return h(ImageIcon, {
      src,
      activeSrc: activeSrc || src, // 如果没有提供activeSrc，使用src作为默认值
      menuKey, // 传递菜单key用于状态检测
      active: false, // 初始状态为false，会在组件内部处理选中状态
      width: iconSize, // 使用Naive UI传递的图标大小
      height: iconSize, // 使用Naive UI传递的图标大小
      ...props,
    })
  }
}

export default ImageIcon
