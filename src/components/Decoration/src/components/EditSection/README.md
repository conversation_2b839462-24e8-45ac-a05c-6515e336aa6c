# EditSection 编辑分区组件

这个组件用于创建带有标题的编辑分区，常用于将样式设置界面划分为不同的区块。

## 使用方法

```vue
<template>
  <edit-section title="基本样式">
    <!-- 样式设置内容 -->
    <div class="style-item">
      <div class="style-item-label">文字颜色</div>
      <div class="style-item-content">
        <single-color-picker v-model="textColor" />
      </div>
    </div>
  </edit-section>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { EditSection } from '@/components/Decoration'
import { SingleColorPicker } from '@/components'

const textColor = ref('#333333')
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| title | String | '' | 分区标题，如果为空则不显示标题 |
| noMargin | Boolean | false | 是否移除底部外边距 |
| customStyle | Object | {} | 自定义样式对象，应用于整个分区 |
| titleStyle | Object | {} | 自定义标题样式对象 |
| contentStyle | Object | {} | 自定义内容样式对象 |
| bordered | Boolean | true | 是否显示边框 |

## 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 分区内容 | 