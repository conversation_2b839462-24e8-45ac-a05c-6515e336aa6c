<template>
  <div class="edit-section" :class="{ 'no-margin': noMargin }" :style="sectionStyle">
    <div v-if="title" class="edit-section-title" :style="titleStyle">{{ title }}</div>
    <div class="edit-section-content" :style="contentStyle">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, CSSProperties } from 'vue'
  import { basicProps } from './props'

  const props = defineProps(basicProps)

  // 计算样式
  const sectionStyle = computed(() => {
    const style: CSSProperties = { ...props.customStyle }
    if (!props.bordered) {
      style.border = 'none'
      style.boxShadow = 'none'
    }
    return style
  })
</script>

<style lang="scss" scoped>
  .edit-section {
    background-color: #fff;

    :deep(.n-form-item-label__text) {
      color: #666666;
    }

    &.no-margin {
      margin-bottom: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .edit-section-title {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #333333;
  }

  .edit-section-content {
    padding: 0;
  }
</style>
