import type { CSSProperties } from 'vue'

export const basicProps = {
  // 标题
  title: {
    type: String,
    default: '',
  },
  // 是否移除底部外边距
  noMargin: {
    type: Boolean,
    default: false,
  },
  // 自定义样式
  customStyle: {
    type: Object as () => CSSProperties,
    default: () => ({}),
  },
  // 标题样式
  titleStyle: {
    type: Object as () => CSSProperties,
    default: () => ({}),
  },
  // 内容样式
  contentStyle: {
    type: Object as () => CSSProperties,
    default: () => ({}),
  },
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: true,
  },
}
