<!-- 组件操作栏 -->
<template>
  <ul
    v-if="isVisible"
    class="quick-action text-center w-[40px] rounded-lg shadow-lg bg-white overflow-hidden"
  >
    <li v-for="(action, index) in actions" :key="index">
      <n-tooltip placement="right" trigger="hover">
        {{ action.title }}
        <template #trigger>
          <div :class="['quick-action-item', index < actions.length - 1 ? 'border-gray-100' : '']">
            <n-icon
              class="transition-all duration-300"
              size="20"
              @click="handleAction(action.event)"
            >
              <component :is="action.icon" />
            </n-icon>
          </div>
        </template>
      </n-tooltip>
    </li>
  </ul>
</template>

<script setup lang="ts">
  import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    CopyOutlined,
    DeleteOutlined,
    ReloadOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
  } from '@vicons/antd'
  import { NIcon, NTooltip } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { computed } from 'vue'

  const { t } = useI18n()

  const props = defineProps<{
    isVisible: boolean
    isHidden: boolean
  }>()

  const emit = defineEmits<{
    (e: 'move-up'): void
    (e: 'move-down'): void
    (e: 'copy'): void
    (e: 'delete'): void
    (e: 'reset'): void
    (e: 'toggle-visibility'): void
  }>()

  // 使用computed属性预先计算所有操作项
  const actions = computed(() => [
    {
      title: t('decoration.common.moveUpComponent'),
      icon: ArrowUpOutlined,
      event: 'move-up',
    },
    {
      title: t('decoration.common.moveDownComponent'),
      icon: ArrowDownOutlined,
      event: 'move-down',
    },
    {
      title: t('decoration.common.copyComponent'),
      icon: CopyOutlined,
      event: 'copy',
    },
    {
      title: t('decoration.common.deleteComponent'),
      icon: DeleteOutlined,
      event: 'delete',
    },
    {
      title: t('decoration.common.resetComponent'),
      icon: ReloadOutlined,
      event: 'reset',
    },
    {
      title: props.isHidden
        ? t('decoration.common.showComponent')
        : t('decoration.common.hideComponent'),
      icon: props.isHidden ? EyeOutlined : EyeInvisibleOutlined,
      event: 'toggle-visibility',
    },
  ])

  // 统一处理点击事件
  const handleAction = (event: string) => {
    emit(event as any)
  }
</script>

<style lang="scss" scoped>
  .quick-action {
    z-index: 100;
  }

  .quick-action-item {
    @apply cursor-pointer py-2 hover:bg-gray-100;
    transition: all 0.3s ease;

    &:hover {
      .n-icon {
        color: var(--primary-color);
        transform: scale(1.2);
      }
    }
  }
</style>
