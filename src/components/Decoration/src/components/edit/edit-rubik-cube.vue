<template>
  <div class="edit-rubik-cube">
    <!-- 内容 -->
    <div class="content-wrap rubik-cube" v-show="decorationStore.editTab == 'content'">
      <n-form label-width="80px" class="px-[10px]" label-placement="left" label-align="left">
        <edit-section :title="t('decoration.common.displaySettings')">
          <n-form-item :label="t('decoration.common.selectStyle')">
            <n-space align="center">
              <n-button type="primary" @click="handleShowTemplate">
                {{ t('decoration.common.changeTemplate') }}
              </n-button>
              <n-text>{{ selectTemplate.styleName }}</n-text>
            </n-space>
          </n-form-item>
        </edit-section>

        <edit-section :title="t('decoration.common.rubikCubeLayout')">
          <n-space>
            <RubikCube
              @change="decorationStore.updateEditComponent('cubeListInfo', $event)"
              :cube-list="cubeListInfo"
              :style-arr="selectTemplate.dimensionScale"
              :density="selectTemplate.density"
            />
          </n-space>
        </edit-section>
      </n-form>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab == 'style'">
      <edit-section :title="t('decoration.common.imageSet')">
        <n-form label-width="80px" class="px-[10px]">
          <n-form-item :label="t('decoration.common.imageGap')">
            <slider-input
              :value="currentEditComponent.imageGap"
              :min="0"
              :max="30"
              :default-value="0"
              @update:value="decorationStore.updateEditComponent('imageGap', $event)"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.topRadius')">
            <slider-input
              :value="displayTopRadius"
              :min="0"
              :max="50"
              :default-value="0"
              @update:value="updateTopRadius"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.bottomRadius')">
            <slider-input
              :value="displayBottomRadius"
              :min="0"
              :max="50"
              :default-value="0"
              @update:value="updateBottomRadius"
            />
          </n-form-item>
        </n-form>
      </edit-section>

      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
    <!-- 选择风格弹窗 -->
    <basicModal @register="modalRegister" ref="modalRef" @on-ok="okModal" class="w-[1240px]">
      <template #default>
        <div class="grid grid-cols-5 gap-10">
          <div
            class="flex items-center"
            v-for="(item, i) in templateList"
            :key="i"
            @click="switchStyle(item)"
          >
            <n-space vertical justify="space-around">
              <img
                :src="getImageModule(item.img)"
                class="h-[200px] w-[200px] border border-solid rounded-md"
                :class="{
                  'border-[var(--n-color-target)]': item.style === currentStyle,
                }"
              />

              <div class="text-center">{{ item.styleName }}</div>
            </n-space>
          </div>
        </div>
      </template>
    </basicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { basicModal, useModal } from '@/components/Modal'
  import RubikCube from '../RubikCube'
  import { getImageModule } from '@/utils'
  import { RubikCubeComponent } from '../../types'

  defineEmits(['register'])

  const decorationStore = useDecorationStore()
  const { t } = useI18n()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<RubikCubeComponent>(),
  )
  /** 初始化时设置ignore属性 */
  decorationStore.updateEditComponent('ignore', [
    'background',
    'itemStyle',
    'containerMargin',
    'containerPadding',
    'textColor',
  ])

  // TODO:组件验证
  // currentEditComponent.value.verify = (index: number) => {
  //   const res = { code: true, message: '' }
  //   decorationStore.componentsData[index].list.forEach((item: any) => {
  //     if (item.imageUrl === '') {
  //       res.code = false
  //       res.message = t('decoration.common.imageUrlTip')
  //       return res
  //     }
  //   })
  //   return res
  // }

  /** 风格列表 */
  const templateList = ref([
    {
      img: 'cube1.png',
      style: 'style-1',
      styleName: '风格1',
      density: 4,
      dimensionScale: [],
    },
    {
      img: 'cube2.png',
      style: 'style-2',
      styleName: '风格2',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 1,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube3.png',
      style: 'style-3',
      styleName: '风格3',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube4.png',
      style: 'style-4',
      styleName: '风格4',
      density: 3, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 1,
            y: 3,
          },
        },
        {
          start: {
            x: 2,
            y: 1,
          },
          end: {
            x: 2,
            y: 3,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 3,
            y: 3,
          },
        },
      ],
    },
    {
      img: 'cube5.png',
      style: 'style-5',
      styleName: '风格5',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 2,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 1,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube6.png',
      style: 'style-6',
      styleName: '风格6',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 1,
            y: 3,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube7.png',
      style: 'style-7',
      styleName: '风格7',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 2,
          },
        },
        {
          start: {
            x: 1,
            y: 3,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube8.png',
      style: 'style-8',
      styleName: '风格8',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 3,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube9.png',
      style: 'style-9',
      styleName: '风格9',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 2,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 1,
            y: 3,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
    {
      img: 'cube10.png',
      style: 'style-10',
      styleName: '风格10',
      density: 4, //密度
      dimensionScale: [
        {
          start: {
            x: 1,
            y: 1,
          },
          end: {
            x: 2,
            y: 4,
          },
        },
        {
          start: {
            x: 3,
            y: 1,
          },
          end: {
            x: 4,
            y: 2,
          },
        },
        {
          start: {
            x: 3,
            y: 3,
          },
          end: {
            x: 3,
            y: 4,
          },
        },
        {
          start: {
            x: 4,
            y: 3,
          },
          end: {
            x: 4,
            y: 4,
          },
        },
      ],
    },
  ])

  /** 当前选中的风格信息 */
  const selectTemplate = computed(() => {
    let data
    templateList.value.forEach((item) => {
      if (item.style == currentEditComponent.value.style) {
        data = item
      }
    })
    return data
  })

  /**
   * 用于显示的上圆角
   */
  const displayTopRadius = computed({
    get: () => currentEditComponent.value.borderRadius?.topLeft ?? 0,
    set: (value) => updateTopRadius(value),
  })

  /**
   * 用于显示的下圆角
   */
  const displayBottomRadius = computed({
    get: () => currentEditComponent.value.borderRadius?.bottomLeft ?? 0,
    set: (value) => updateBottomRadius(value),
  })

  /**
   * 更新上圆角
   * @param value
   */
  const updateTopRadius = (value: number) => {
    if (currentEditComponent.value.borderRadius) {
      currentEditComponent.value.borderRadius.topLeft = value
      currentEditComponent.value.borderRadius.topRight = value
      decorationStore.updateEditComponent('borderRadius', currentEditComponent.value.borderRadius)
    }
  }

  /**
   * 更新下圆角
   * @param value
   */
  const updateBottomRadius = (value: number) => {
    if (currentEditComponent.value.borderRadius) {
      currentEditComponent.value.borderRadius.bottomLeft = value
      currentEditComponent.value.borderRadius.bottomRight = value
      decorationStore.updateEditComponent('borderRadius', currentEditComponent.value.borderRadius)
    }
  }

  /**魔方数据 */
  const cubeListInfo = ref([])
  /** 风格弹窗中选中的风格 */
  const currentStyle = ref('style-1')
  /**切换风格 */
  const switchStyle = (item) => {
    currentStyle.value = item.style
  }

  const [modalRegister, { openModal, closeModal }] = useModal({
    title: '选择风格',
    width: 600,
  })

  /**
   *  显示风格弹窗
   */
  const handleShowTemplate = () => {
    currentStyle.value = currentEditComponent.value.style
    openModal()
  }

  /**
   * 风格弹窗确认
   */
  const okModal = () => {
    closeModal()
    decorationStore.updateEditComponent('style', currentStyle.value)
    cubeListInfo.value = selectTemplate.value.dimensionScale
  }

  defineExpose({})
</script>

<style lang="scss" scoped></style>
