<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.displaySettings')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="radiogroup"
            @update:value="(val) => handleStyleChange(val)"
          >
            <n-space>
              <n-radio
                v-for="option in styleOptions"
                :key="option.value"
                :value="option.value"
                :label="t(option.i18nKey)"
              />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.titleContent')">
        <n-form-item :label="t('decoration.common.titleText')">
          <n-input
            :value="currentEditComponent.titleText"
            @update:value="(val) => decorationStore.updateEditComponent('titleText', val)"
            :placeholder="t('decoration.common.titlePlaceholder')"
            clearable
            maxlength="15"
            show-count
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.link')">
          <SelectLink v-model="currentEditComponent.link" />
        </n-form-item>

        <template v-if="currentEditComponent.subTitle.control">
          <n-form-item :label="t('decoration.common.subTitle')">
            <n-input
              :value="currentEditComponent.subTitle.text"
              @update:value="(val) => decorationStore.updateEditComponent('subTitle.text', val)"
              :placeholder="t('decoration.common.subTitlePlaceholder')"
              clearable
              maxlength="30"
              show-count
            />
          </n-form-item>

          <n-form-item :label="t('decoration.common.moreControl')">
            <n-radio-group
              :value="currentEditComponent.more.control"
              @update:value="(val) => decorationStore.updateEditComponent('more.control', val)"
              name="radiogroup"
            >
              <n-space>
                <n-radio :value="ShowStatusEnum.SHOW" :label="t('decoration.common.showText')" />
                <n-radio :value="ShowStatusEnum.HIDE" :label="t('decoration.common.hideText')" />
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="t('decoration.common.more')">
            <n-input
              :value="currentEditComponent.more.text"
              @update:value="(val) => decorationStore.updateEditComponent('more.text', val)"
              :placeholder="t('decoration.common.morePlaceholder')"
              clearable
              maxlength="8"
              show-count
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.link')">
            <SelectLink v-model="currentEditComponent.more.link" />
          </n-form-item>
        </template>

        <n-form-item
          :label="t('decoration.common.textAlign')"
          v-show="currentEditComponent.style === StyleEnum.STYLE_1"
        >
          <n-radio-group
            :value="currentEditComponent.textAlign"
            @update:value="(val) => decorationStore.updateEditComponent('textAlign', val)"
          >
            <n-space>
              <n-radio :label="TextAlignEnum.CENTER" :value="TextAlignEnum.CENTER">
                {{ t('decoration.common.textAlignCenter') }}
              </n-radio>
              <n-radio :label="TextAlignEnum.LEFT" :value="TextAlignEnum.LEFT">
                {{ t('decoration.common.textAlignLeft') }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          :label="t('decoration.common.leftBlock')"
          v-show="currentEditComponent.style === StyleEnum.STYLE_1"
        >
          <n-radio-group
            :value="currentEditComponent.leftBlock"
            @update:value="(val) => decorationStore.updateEditComponent('leftBlock', val)"
          >
            <n-space>
              <n-radio :value="ShowStatusEnum.SHOW" :label="t('decoration.common.showText')" />
              <n-radio :value="ShowStatusEnum.HIDE" :label="t('decoration.common.hideText')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <edit-section :title="t('decoration.common.textStyle')">
        <n-form-item :label="t('decoration.common.titleColor')">
          <single-color-picker
            :value="currentEditComponent.textColor"
            :default-value="currentEditComponent.textColor"
            @update:value="(val) => decorationStore.updateEditComponent('textColor', val)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.titleFontSize')">
          <slider-input
            v-model:value="currentEditComponent.fontSize"
            :min="12"
            :max="30"
            :default-value="16"
            @update:value="decorationStore.updateEditComponent('fontSize', $event)"
          />
        </n-form-item>
        <n-form-item
          v-if="currentEditComponent.style === StyleEnum.STYLE_2"
          :label="t('decoration.common.subTitleColor')"
        >
          <single-color-picker
            :value="currentEditComponent.subTitle.color"
            defaultValue="#909399"
            placeholder="#FFFFFF"
          />
        </n-form-item>
        <n-form-item
          v-if="currentEditComponent.style === StyleEnum.STYLE_2"
          :label="t('decoration.common.subTitleFontSize')"
        >
          <slider-input
            :value="currentEditComponent.subTitle.fontSize"
            :min="12"
            :max="30"
            :default-value="12"
            @update:value="decorationStore.updateEditComponent('subTitle.fontSize', $event)"
          />
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import {
    TextAlignEnum,
    ShowStatusEnum,
    StyleEnum,
    getStyleI18nKey,
    getComponentStyleOptions,
  } from '@/components/Decoration'
  import { SingleColorPicker } from '@/components/ColorPicker'
  import { SelectLink } from '@/components/SelectLink'
  import { TitleComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<TitleComponent>())

  /** 标题组件的风格选项 */
  const styleOptions = getComponentStyleOptions('Title')

  /** 初始化时设置ignore属性 */
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 处理样式变化
   * @param style 样式
   */
  const handleStyleChange = (style: string) => {
    decorationStore.updateEditComponent('style', style, (component) => {
      // 更新样式名称，使用国际化键
      component.styleName = t(getStyleI18nKey(style))

      // 更新子标题和更多按钮的显示状态
      if (
        style === StyleEnum.STYLE_1 ||
        style === StyleEnum.STYLE_3 ||
        style === StyleEnum.STYLE_4
      ) {
        component.subTitle.control = ShowStatusEnum.HIDE
        component.more.control = ShowStatusEnum.HIDE
      } else if (style === StyleEnum.STYLE_2) {
        component.subTitle.control = ShowStatusEnum.SHOW
        component.more.control = ShowStatusEnum.SHOW
      }
    })
  }

  defineExpose({})
</script>

<style lang="scss" scoped></style>
