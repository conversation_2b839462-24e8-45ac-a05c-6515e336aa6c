<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.displaySettings')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent?.style || ''"
            name="radiogroup"
            @update:value="(val) => handleStyleChange(val)"
          >
            <n-space>
              <n-radio
                v-for="option in styleOptions"
                :key="option.value"
                :value="option.value"
                :label="t(option.i18nKey)"
              />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.memberContent')">
        <n-form-item :label="t('decoration.common.memberInfo')">
          <n-radio-group
            :value="currentEditComponent?.memberIdentifierType || ''"
            name="radiogroup"
            @update:value="
              (val) => decorationStore.updateEditComponent('memberIdentifierType', val)
            "
          >
            <n-space>
              <n-radio
                v-for="option in memberIdentifierOptions"
                :key="option.name"
                :value="option.name"
                :label="option.title"
              />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item :label="t('decoration.common.memberMarketing')">
          <n-checkbox-group
            :value="currentEditComponent?.memberMarketing || []"
            @update:value="(val) => decorationStore.updateEditComponent('memberMarketing', val)"
          >
            <n-space item-style="display: flex;">
              <n-checkbox
                v-for="(item, index) in memberMarketingOptions"
                :key="index"
                :value="item.name"
                :label="item.title"
              />
            </n-space>
          </n-checkbox-group>
        </n-form-item>
        <n-form-item :label="t('decoration.common.memberAsset')">
          <n-checkbox-group
            :value="currentEditComponent?.memberAsset || []"
            @update:value="(val) => decorationStore.updateEditComponent('memberAsset', val)"
          >
            <n-space item-style="display: flex;">
              <n-checkbox
                v-for="(item, index) in memberAssetOptions"
                :key="index"
                :value="item.name"
                :label="item.title"
              />
            </n-space>
          </n-checkbox-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <!-- <slot name="style"></slot> -->
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { getStyleI18nKey, getComponentStyleOptions } from '@/components/Decoration'

  import { MemberInfoComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<MemberInfoComponent>(),
  )

  // 初始化结构
  onMounted(() => {
    /** 初始化时设置ignore属性 */
    decorationStore.updateEditComponent('ignore', [
      'itemStyle',
      'containerMargin',
      'containerPadding',
      'containerGradient',
      'background',
      'textColor',
      'borderRadius',
    ])
  })

  /** 标题组件的风格选项 */
  const styleOptions = getComponentStyleOptions('MemberInfo')

  /** 会员标识符类型选项 */
  const memberIdentifierOptions = [
    {
      name: 'user_id',
      title: '用户ID',
      control: 1,
    },
    {
      name: 'mobile',
      title: '手机号',
      control: 1,
    },
  ]
  /** 营销推广选项 */
  const memberMarketingOptions = [
    {
      name: 'promotion',
      title: '邀请码',
      control: 1,
      textColor: '#333',
      bgColor: '#FFF',
    },
  ]

  // TODO: 需要根据后端返回的资产类型进行修改
  const memberAssetOptions = [
    {
      name: 'balance',
      title: '余额',
      control: 1,
    },
    {
      name: 'point',
      title: '积分',
      control: 1,
    },
    {
      name: 'coupon_count',
      title: '优惠券',
      control: 1,
    },
    {
      name: 'red_packet',
      title: '红包',
      control: 1,
    },
    {
      name: 'income',
      title: '收益',
      control: 1,
    },
  ]

  /**
   * 处理样式变化
   * @param style 样式
   */
  const handleStyleChange = (style: string) => {
    // 添加安全检查
    if (!currentEditComponent.value) {
      console.warn('样式变更失败：当前编辑组件为空')
      return
    }

    decorationStore.updateEditComponent('style', style, (component) => {
      if (!component) return

      // 更新样式名称，使用国际化键
      component.styleName = t(getStyleI18nKey(style))
    })
  }

  defineExpose({})
</script>

<style lang="scss" scoped></style>
