<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 列表设置 -->
      <edit-section :title="t('decoration.common.goodsListSet')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="stylegroup"
            @update:value="(val) => decorationStore.updateEditComponent('style', val)"
          >
            <n-grid y-gap="12" :cols="2">
              <n-gi v-for="(item, index) in styleList" :key="index">
                <n-radio :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 商品设置 -->
      <edit-section :title="t('decoration.common.goodsSet')">
        <!-- 选择方式 -->
        <n-form-item :label="t('decoration.common.selectType')">
          <n-radio-group
            :value="currentEditComponent.checkType"
            name="typegroup"
            @update:value="(val) => handleTypeChange(val)"
          >
            <n-space>
              <n-radio value="isGoods" :label="t('decoration.common.isGoods')" />
              <n-radio class="ml-7" value="isType" :label="t('decoration.common.isType')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 选择商品 -->
        <n-form-item
          :label="t('decoration.common.checkGoods')"
          v-if="currentEditComponent.checkType === 'isGoods'"
        >
          <n-space class="flex items-center">
            <SelectLink
              v-model:ids="currentEditComponent.goodIds"
              :only="['goods']"
              :only-second="[GOODS_MENU.SHOP_GOODS_SELECT]"
              goods-multiple
              @confirm="checkGoodsList"
            >
              <n-button type="primary">+{{ t('decoration.common.isGoods') }}</n-button>
            </SelectLink>
            <span v-if="currentEditComponent.goodIds && currentEditComponent.goodIds.length">
              <span>{{ `${t('decoration.common.checkTabGoods')}` }}</span>
              <span>{{ currentEditComponent.goodIds.length }}</span>
              <span>{{ `${t('decoration.common.piece')}` }}</span>
            </span>
          </n-space>
        </n-form-item>
        <!-- 选择分类 -->
        <n-form-item
          :label="t('decoration.common.checkType')"
          v-if="currentEditComponent.checkType === 'isType'"
        >
          <n-cascader
            v-model:value="currentEditComponent.cateIds"
            multiple
            clearable
            :options="typeOptions"
            :placeholder="t('decoration.common.placeholderCarGoodsType')"
            :cascade="false"
            :check-strategy="'all'"
            :show-path="true"
            value-field="id"
            label-field="name"
            @update:value="(val) => decorationStore.updateEditComponent('cateIds', val)"
          />
        </n-form-item>
        <!-- 商品数量 -->
        <n-form-item
          :label="t('decoration.common.goodsNum')"
          v-if="currentEditComponent.checkType === 'isType'"
        >
          <slider-input
            v-model:value="currentEditComponent.goodsNum"
            :min="0"
            :max="50"
            :default-value="1"
            @update:value="(val) => decorationStore.updateEditComponent('goodsNum', val)"
          />
        </n-form-item>
        <!-- 商品排序 -->
        <n-form-item
          :label="t('decoration.common.goodsSort')"
          v-if="currentEditComponent.checkType === 'isType'"
        >
          <n-radio-group
            :value="currentEditComponent.goodsSort"
            name="sortgroup"
            @update:value="(val) => decorationStore.updateEditComponent('goodsSort', val)"
          >
            <n-space>
              <n-radio value="sortAll" :label="t('decoration.common.sortAll')" />
              <n-radio value="sales" :label="t('decoration.common.sortVolume')" />
              <n-radio value="price" :label="t('decoration.common.sortPrice')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 显示内容 -->
      <edit-section :title="t('decoration.common.showContent')">
        <!-- 展示信息 -->
        <n-form-item :label="t('decoration.common.showInfo')">
          <n-checkbox-group
            v-model:value="currentEditComponent.showInfo"
            @update:value="(val) => decorationStore.updateEditComponent('showInfo', val)"
          >
            <n-grid x-gap="24" y-gap="12" :cols="2">
              <n-gi v-for="(item, index) in infoList" :key="index">
                <n-checkbox :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form-item>
      </edit-section>
      <!-- 购物车按钮 -->
      <edit-section :title="t('decoration.common.shoppingCarBtn')">
        <!-- 是否显示 -->
        <n-form-item :label="t('decoration.common.isShowCar')">
          <n-radio-group
            :value="currentEditComponent.isShowCar"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('isShowCar', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.showComponent')" />
              <n-radio class="ml-7" :value="0" :label="t('decoration.common.hideComponent')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 按钮样式 -->
        <n-form-item
          :label="t('decoration.common.carBtnStyle')"
          v-if="currentEditComponent.isShowCar === 1"
        >
          <n-space>
            <n-tag
              round
              class="cursor-pointer"
              :type="currentEditComponent.carBtnStyle === 1 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(1)"
            >
              <n-button size="tiny" color="#EB3534" round>
                {{ currentEditComponent.carBtnText }}
              </n-button>
            </n-tag>
            <n-tag
              round
              class="cursor-pointer ml-3"
              :type="currentEditComponent.carBtnStyle === 2 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(2)"
            >
              <n-avatar :src="catBtnStyle1" color="#EB3534" />
            </n-tag>
            <n-tag
              round
              class="cursor-pointer ml-3"
              :type="currentEditComponent.carBtnStyle === 3 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(3)"
            >
              <n-avatar :src="catBtnStyle2" color="#EB3534" />
            </n-tag>
          </n-space>
        </n-form-item>
        <!-- 按钮文字 -->
        <n-form-item
          :label="t('decoration.common.buttonText')"
          v-if="currentEditComponent.isShowCar === 1 && currentEditComponent.carBtnStyle === 1"
        >
          <n-input
            :value="currentEditComponent.carBtnText"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnText', val)"
            :placeholder="t('decoration.common.placeholderCarContent')"
            clearable
            maxlength="2"
            show-count
          />
        </n-form-item>
        <!-- 按钮效果 -->
        <n-form-item
          :label="t('decoration.common.carBtnEffect')"
          v-if="currentEditComponent.isShowCar === 1"
        >
          <n-radio-group
            :value="currentEditComponent.carBtnEffect"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnEffect', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.carBtnEffect1')" />
              <n-radio class="ml-7" :value="0" :label="t('decoration.common.carBtnEffect2')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 角标设置 -->
      <edit-section :title="t('decoration.common.subscriptSet')">
        <!-- 是否显示 -->
        <n-form-item :label="t('decoration.common.subscriptStyle')">
          <n-radio-group
            :value="currentEditComponent.subscriptStyle"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('subscriptStyle', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.subscriptStyle1')" />
              <n-radio :value="2" :label="t('decoration.common.subscriptStyle2')" />
              <n-radio :value="3" :label="t('decoration.common.subscriptStyle3')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 选择风格 -->
        <n-form-item
          :label="t('decoration.common.selectStyle')"
          v-if="currentEditComponent.subscriptStyle === 2"
        >
          <n-space>
            <n-tag
              class="cursor-pointer sub-btn__box"
              :type="currentEditComponent.subBtnStyle === index + 1 ? 'primary' : 'default'"
              @click="() => handleSubBtnStyle(item.type)"
              v-for="(item, index) in subBtnList"
              :key="index"
            >
              <img :src="item.imgUrl" :style="{ width: `${index === 1 ? '30px' : '42px'}` }" />
              <div
                class="sub-btn__text"
                :style="{
                  top: `${index === 2 ? '12px' : '7px'}`,
                  left: `${index === 1 ? '11px' : '14px'}`,
                }"
              >
                {{ currentEditComponent.subBtnText }}
              </div>
            </n-tag>
          </n-space>
        </n-form-item>
        <!-- 角标文字 -->
        <n-form-item
          :label="t('decoration.common.subBtnText')"
          v-if="currentEditComponent.subscriptStyle === 2"
        >
          <n-input
            :value="currentEditComponent.subBtnText"
            @update:value="(val) => decorationStore.updateEditComponent('subBtnText', val)"
            :placeholder="t('decoration.common.placeholderCarContent')"
            clearable
            maxlength="2"
            show-count
          />
        </n-form-item>
        <!-- 添加图片 -->
        <n-form-item
          :label="t('decoration.common.subBtnImg')"
          v-if="currentEditComponent.subscriptStyle === 3"
        >
          <n-space vertical>
            <AttachmentUpload v-model:value="currentEditComponent.subBtnImg" :maxNumber="1" />
            <div class="section-tips mt-4">{{ t('decoration.common.subBtnImgSize') }}</div>
          </n-space>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 商品样式 -->
      <edit-section :title="t('decoration.common.goodsStyle')">
        <!-- 商品名称 -->
        <n-form-item :label="t('decoration.common.goodsNameStyle')">
          <n-radio-group
            :value="currentEditComponent.goodsNameStyle"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('goodsNameStyle', val)"
          >
            <n-space>
              <n-radio value="normal" :label="t('decoration.common.goodsNameStyle1')" />
              <n-radio value="bold" :label="t('decoration.common.goodsNameStyle2')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 文字颜色 -->
        <n-form-item :label="t('decoration.common.textColor')">
          <single-color-picker
            :value="currentEditComponent.goodsNameColor"
            default-value="#333333"
            @update:value="(val) => decorationStore.updateEditComponent('goodsNameColor', val)"
          />
        </n-form-item>
        <!-- 上圆角 -->
        <n-form-item :label="t('decoration.common.imgBuildRadiusTop')">
          <slider-input
            v-model:value="currentEditComponent.imgBuildRadiusTop"
            :min="0"
            :max="50"
            :default-value="5"
            @update:value="(val) => decorationStore.updateEditComponent('imgBuildRadiusTop', val)"
          />
        </n-form-item>
        <!-- 下圆角 -->
        <n-form-item :label="t('decoration.common.imgBuildRadiusBottom')">
          <slider-input
            v-model:value="currentEditComponent.imgBuildRadiusBottom"
            :min="0"
            :max="50"
            :default-value="5"
            @update:value="
              (val) => decorationStore.updateEditComponent('imgBuildRadiusBottom', val)
            "
          />
        </n-form-item>
      </edit-section>
      <!-- 按钮样式 -->
      <edit-section :title="t('decoration.common.buyBtn')">
        <!-- 文字颜色 -->
        <n-form-item :label="t('decoration.common.textColor')">
          <single-color-picker
            :value="currentEditComponent.carBtnTextColor"
            default-value="#ffffff"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnTextColor', val)"
          />
        </n-form-item>
        <!-- 按钮颜色 -->
        <n-form-item :label="t('decoration.common.buyBtnColor')">
          <gradient-color-picker
            v-model:startColor="currentEditComponent.shoppingCarStyle.startColor"
            v-model:endColor="currentEditComponent.shoppingCarStyle.endColor"
            v-model:angle="currentEditComponent.shoppingCarStyle.angle"
            :defaultStartColor="'#EB3534'"
            :defaultEndColor="'#EB3534'"
            show-angle-control
            @change="(val) => decorationStore.updateEditComponent('shoppingCarStyle', val)"
          />
        </n-form-item>
        <!-- 圆角 -->
        <n-form-item :label="t('decoration.common.carBtnTBuildRadius')">
          <slider-input
            v-model:value="currentEditComponent.carBtnTBuildRadius"
            :min="0"
            :max="20"
            :default-value="20"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnTBuildRadius', val)"
          />
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, onMounted, computed } from 'vue'
  import { SingleColorPicker } from '@/components/ColorPicker'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import catBtnStyle1 from '@/assets/images/decoration/goodsList/catBtnStyle1.png'
  import catBtnStyle2 from '@/assets/images/decoration/goodsList/catBtnStyle2.png'
  import subBtnStyle1 from '@/assets/images/decoration/goodsList/subBtnStyle1.png'
  import subBtnStyle2 from '@/assets/images/decoration/goodsList/subBtnStyle2.png'
  import subBtnStyle3 from '@/assets/images/decoration/goodsList/subBtnStyle3.png'
  import { GOODS_MENU } from '@/components/SelectLink/src/config/constants'
  import { GoodsListComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  //展示风格
  const styleList = [
    {
      name: t('decoration.common.oneCols'),
      value: 0,
    },
    {
      name: t('decoration.common.towColsVertical'),
      value: 1,
    },
    {
      name: t('decoration.common.threeColsVertical'),
      value: 2,
    },
    {
      name: t('decoration.common.towColsHorizontal'),
      value: 3,
    },
    {
      name: t('decoration.common.bigImage'),
      value: 4,
    },
    {
      name: t('decoration.common.toSlide'),
      value: 5,
    },
  ]

  //展示信息
  const infoList = [
    {
      name: t('decoration.common.goodsName'),
      value: 'goodsName',
    },
    {
      name: t('decoration.common.goodsSubName'),
      value: 'goodsSubName',
    },
    {
      name: t('decoration.common.goodsPrice'),
      value: 'goodsPrice',
    },
    {
      name: t('decoration.common.originalPrice'),
      value: 'originalPrice',
    },
    {
      name: t('decoration.common.goodsSale'),
      value: 'goodsSale',
    },
    {
      name: t('decoration.common.userPrice'),
      value: 'userPrice',
    },
  ]

  //角标风格
  const subBtnList = [
    {
      type: 1,
      imgUrl: subBtnStyle1,
    },
    {
      type: 2,
      imgUrl: subBtnStyle2,
    },
    {
      type: 3,
      imgUrl: subBtnStyle3,
    },
  ]

  const typeOptions = ref<typeOptions[]>([])
  const goodsList = ref<any>([])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsListComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  const handleTypeChange = (type: string) => {
    decorationStore.updateEditComponent('checkType', type, () => {})
  }

  /* 选择购物车按钮样式 */
  const handleCarBtnStyle = (type: number) => {
    decorationStore.updateEditComponent('carBtnStyle', type, () => {})
  }

  /* 选择角标样式 */
  const handleSubBtnStyle = (type: number) => {
    decorationStore.updateEditComponent('subBtnStyle', type, () => {})
  }

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  const checkGoodsList = (linkValue, otherData) => {
    goodsList.value = otherData
    decorationStore.updateEditComponent('goodIds', linkValue?.goodsIds || [])
  }

  onMounted(() => {
    getGoodsTypeList()
  })

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    position: relative;
    top: -20px;
    font-size: 12px;
    color: #999999;
  }
  .check-goods {
    width: 100%;
    padding: 5px 10px;
    color: #999999;
    cursor: pointer;
    border: 1px solid #eeeeee;
    border-radius: 3px;
  }
  .sub-btn__box {
    position: relative;
    img {
      width: 42px;
      height: 24px;
    }
  }
  .sub-btn__text {
    position: absolute;
    top: 7px;
    left: 14px;
    font-size: 10px;
    color: #ffffff;
  }
</style>
