<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 展示设置 -->
      <edit-section :title="t('decoration.common.displaySettings')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="radiogroup"
            @update:value="(val) => handleStyleChange(val)"
          >
            <n-space>
              <n-radio value="style-1" :label="t('decoration.common.styleSearchOne')" />
              <n-radio value="style-2" :label="t('decoration.common.styleSearchTwo')" />
              <n-radio value="style-3" :label="t('decoration.common.styleSearchThree')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 搜索内容 -->
      <edit-section :title="t('decoration.common.searchContent')">
        <!-- 提示文字 -->
        <n-form-item :label="t('decoration.common.hintText')">
          <n-input
            :value="currentEditComponent.placeholderText"
            @update:value="(val) => decorationStore.updateEditComponent('placeholderText', val)"
            :placeholder="t('decoration.common.searchPlaceholder')"
            clearable
            maxlength="15"
            show-count
          />
        </n-form-item>
        <!-- logo图 -->
        <n-form-item
          :label="t('decoration.common.logoTitle')"
          v-if="currentEditComponent.searchType.logo"
        >
          <n-space vertical>
            <AttachmentUpload
              v-model:value="currentEditComponent.searchType.logoUrl"
              :maxNumber="1"
            />
            <div class="section-tips mt-2">{{ t('decoration.common.logoTips') }}</div>
          </n-space>
        </n-form-item>
        <!-- 标题 -->
        <n-form-item
          :label="t('decoration.common.searchTitle')"
          v-if="currentEditComponent.searchType.title"
        >
          <n-input
            :value="currentEditComponent.searchType.searchTitle"
            @update:value="
              (val) => decorationStore.updateEditComponent('searchType.searchTitle', val)
            "
            :placeholder="t('decoration.common.searchTitlePlaceholder')"
            clearable
            maxlength="6"
            show-count
          />
        </n-form-item>
      </edit-section>
      <!-- 搜索热词 -->
      <edit-section :title="t('decoration.common.searchHotWord')">
        <div class="section-tips">{{ t('decoration.common.searchTips') }}</div>
        <!-- 显示时间 -->
        <n-form-item :label="t('decoration.common.showTime')">
          <n-input
            :value="currentEditComponent.showTime"
            @update:value="(val) => decorationStore.updateEditComponent('showTime', val)"
            :placeholder="t('decoration.common.showTimePlaceholder')"
            clearable
          >
            <template #suffix>{{ t('decoration.common.time') }}</template>
          </n-input>
        </n-form-item>
        <n-form-item>
          <EditDraggableItem
            :list="currentEditComponent.hotList"
            :placeholder="t('decoration.common.searchHotWordPlaceholder')"
            :maxlength="15"
            :showCount="true"
            @delete-item="deleteItem"
          >
            <template #addBtn>
              <n-button class="add-hot__btn" type="primary" @click="addHotItem()">
                {{
                  `+ ${t('decoration.common.addText')}（${currentEditComponent.hotList.length}/10）`
                }}
              </n-button>
            </template>
          </EditDraggableItem>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <edit-section :title="t('decoration.common.textStyle')">
        <!-- 搜索框 -->
        <n-form-item :label="t('decoration.common.searchBox')">
          <single-color-picker
            :value="currentEditComponent.boxColor"
            default-value="#f2f2f2"
            @update:value="(val) => decorationStore.updateEditComponent('boxColor', val)"
          />
        </n-form-item>
        <!-- 提示文字 -->
        <n-form-item :label="t('decoration.common.hintText')">
          <single-color-picker
            :value="currentEditComponent.placeholderColor"
            default-value="#C7C7C7"
            @update:value="(val) => decorationStore.updateEditComponent('placeholderColor', val)"
          />
        </n-form-item>
        <!-- 热词文字 -->
        <n-form-item :label="t('decoration.common.searchHotWordText')">
          <single-color-picker
            :value="currentEditComponent.hotWordColor"
            default-value="#333333"
            @update:value="(val) => decorationStore.updateEditComponent('hotWordColor', val)"
          />
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, computed } from 'vue'
  import { SingleColorPicker } from '@/components/ColorPicker'
  import { EditDraggableItem } from '@/components/Decoration/src/components/EditDraggableItem'
  import { SearchComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<SearchComponent>())

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  const hotList = ref<string[]>(currentEditComponent.value.hotList)

  /* 添加热词 */
  const addHotItem = () => {
    if (currentEditComponent.value.hotList.length >= 10) {
      return
    }
    hotList.value.push('')
    console.log('hotList', hotList.value)
    decorationStore.updateEditComponent('hotList', hotList.value)
  }

  /* 删除热词 */
  const deleteItem = (listVal) => {
    hotList.value = listVal
    decorationStore.updateEditComponent('hotList', hotList.value)
  }

  // 处理样式变化
  const handleStyleChange = (style: string) => {
    decorationStore.updateEditComponent('style', style, (component) => {
      if (style === 'style-1') {
        component.searchType.title = false
        component.searchType.logo = false
        component.styleName = '搜索'
      } else if (style === 'style-2') {
        component.searchType.title = false
        component.searchType.logo = true
        component.styleName = 'logo-搜索'
      } else if (style === 'style-3') {
        component.searchType.title = true
        component.searchType.logo = false
        component.styleName = '标题-搜索'
      }
    })
  }

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    position: relative;
    top: -20px;
    font-size: 12px;
    color: #999999;
  }
  .add-hot__btn {
    width: 100%;
  }
</style>
