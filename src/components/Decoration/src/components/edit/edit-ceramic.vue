<template>
  <n-form label-width="90" label-placement="left" label-align="left">
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.ceramicShow')">
        <n-form-item :label="t('decoration.common.ceramicNavMode')">
          <n-radio-group
            :value="currentEditComponent.navMode"
            name="radiogroup"
            @update:value="(val) => decorationStore.updateEditComponent('navMode', val)"
          >
            <n-space>
              <n-radio v-for="item in navModeList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item :label="t('decoration.common.ceramicShowStyle')">
          <n-radio-group
            :value="currentEditComponent.show"
            name="radiogroup"
            @update:value="(val) => decorationStore.updateEditComponent('show', val)"
          >
            <n-space>
              <n-radio v-for="item in showList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.ceramicContent')">
        <h3 class="text-[12px] text-[#999] pb-[15px]">
          {{ t('decoration.common.ceramicTips') }}
        </h3>
        <link-selector
          v-model="currentEditComponent.list"
          @update:model-value="(val) => decorationStore.updateEditComponent('list', val)"
          mode="custom"
          :schemas="buttonSchemas"
          :maxNumber="30"
          :minNumber="3"
          showTips
        />
      </edit-section>
    </div>
    <div class="style-wrap" v-show="decorationStore.editTab == 'style'">
      <edit-section :title="t('decoration.common.ceramicImage')">
        <n-form-item :label="t('decoration.common.ceramicImageShape')">
          <n-space :size="40">
            <div v-for="(item, index) in currentEditComponent.imgList" :key="index">
              <div
                class="h-[40px] w-[40px] bg-[#D8D8D8] border cursor-pointer"
                :class="getItemClass(item.style, index)"
                @click.stop="imgTypeClick(item.style, index)"
              ></div>
            </div>
          </n-space>
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.textSet')">
        <n-form-item :label="t('decoration.common.titleColor')">
          <SingleColorPicker
            :value="currentEditComponent.titleColor"
            @update:value="(val) => decorationStore.updateEditComponent('titleColor', val)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.subTitleColor')">
          <SingleColorPicker
            :value="currentEditComponent.subTitleColor"
            @update:value="(val) => decorationStore.updateEditComponent('subTitleColor', val)"
          />
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.indicatorStyle')">
        <n-form-item :label="t('decoration.common.indicatorColor')">
          <n-radio-group
            :value="currentEditComponent.indicatorColor"
            name="radiogroup"
            @update:value="(val) => decorationStore.updateEditComponent('indicatorColor', val)"
          >
            <n-space>
              <n-radio v-for="item in indicatorColorList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useDecorationStore } from '@/store/modules/decoration'
  import LinkSelector from '@/components/SelectLink/src/linkSelector.vue'
  import { useDesignSetting } from '@/hooks/setting/useDesignSetting'
  import { useI18n } from '@/hooks/web/useI18n'
  import { CeramicComponent } from '../../types'
  import { selectorSchemas } from '@/components/SelectLink'

  /**
   * 定义基础接口
   */
  interface BaseOption<T = string> {
    value: T
    label: string
  }

  const decorationStore = useDecorationStore()
  const { getAppTheme } = useDesignSetting()
  const { t } = useI18n()

  let imgIndex = ref(0)
  let navModeList = ref<BaseOption[]>([
    {
      value: 'style-1',
      label: '图片+文字',
    },
    {
      value: 'style-3',
      label: '文字',
    },
  ])
  let showList = ref<BaseOption[]>([
    {
      value: 'styleShow-1',
      label: '固定显示',
    },
    {
      value: 'styleShow-2',
      label: '单行滑动',
    },
    {
      value: 'styleShow-3',
      label: '分页滑动',
    },
  ])
  let indicatorColorList = ref<BaseOption[]>([
    {
      value: '#999999',
      label: '黑色',
    },
    {
      value: '#ffffff',
      label: '白色',
    },
  ])

  // 按钮模式配置
  const buttonSchemas: selectorSchemas[] = [
    {
      field: 'imageUrl',
      component: 'AttachmentUpload',
      label: '图片',
      componentProps: {
        maxNumber: 1,
        width: 84,
        height: 84,
      },
    },
    {
      field: 'buttonText',
      component: 'NInput',
      label: '按钮文本',
      componentProps: {
        placeholder: '请输入按钮文本',
        showCount: true,
        maxlength: 5,
      },
    },
    {
      field: 'tips',
      component: 'NInput',
      label: '提示文本',
      componentProps: {
        placeholder: '请输入提示文本',
        showCount: true,
        maxlength: 6,
      },
    },
    {
      field: 'link',
      component: 'SelectLink',
      label: '链接',
    },
  ]
  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<CeramicComponent>())

  let imgTypeClick = (type: string, index: number) => {
    decorationStore.updateEditComponent('imgType', type)
    imgIndex.value = index
  }

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  // 定义计算属性
  const getItemClass = computed(() => (style: string, index: number) => {
    return {
      'rounded-md': style === 'square-radius',
      'rounded-full': style === 'circle',
      'border-1 active-border': imgIndex.value === index,
    }
  })

  defineExpose({})
</script>
<style lang="scss" scoped>
  .active-border {
    border: 1px solid v-bind(getAppTheme);
  }
</style>
