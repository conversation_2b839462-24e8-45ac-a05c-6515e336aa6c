<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!--内容-->
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.weChatTitle')">
        <n-form-item :label="t('decoration.common.weChatLink')">
          <n-radio-group v-model:value="currentEditComponent.linkUrl" name="radiogroup">
            <n-space>
              <n-radio v-for="item in linkList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          :label="t('decoration.common.weChatLinkUrl')"
          v-show="currentEditComponent.linkUrl === 'linkUrl'"
        >
          <SelectLink v-model="currentEditComponent.link" />
        </n-form-item>
        <n-form-item
          :label="t('decoration.common.weChatImage')"
          v-show="currentEditComponent.linkUrl === 'linkImgUrl'"
        >
          <div class="flex items-center flex-col w-full">
            <AttachmentUpload
              class="w-auto"
              v-model:value="currentEditComponent.imageUrl"
              :maxNumber="1"
            />
            <n-text class="text-[12px] flex-2 pt-2 w-full text-gray-400">
              {{ t('decoration.common.weChatTipsPlaceholder') }}
            </n-text>
          </div>
        </n-form-item>
      </edit-section>
      <edit-section :title="t('decoration.common.weChatSearchContent')">
        <n-form-item :label="t('decoration.common.titleSearchText')">
          <n-input
            v-model:value="currentEditComponent.tips"
            type="textarea"
            :placeholder="t('decoration.common.weChatSearchContentPlaceholder')"
            maxlength="35"
            show-count
            clearable
          />
        </n-form-item>
      </edit-section>
    </div>
    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { WeChatComponent } from '../../types'
  import { SelectLink } from '@/components/SelectLink'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  let linkList = ref([
    {
      label: '链接跳转',
      value: 'linkUrl',
    },
    {
      label: '图片跳转',
      value: 'linkImgUrl',
    },
  ])

  const currentEditComponent = computed(() => decorationStore.getEditComponent<WeChatComponent>())

  /** 初始化时设置ignore属性 */
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])
</script>

<style scoped lang="scss"></style>
