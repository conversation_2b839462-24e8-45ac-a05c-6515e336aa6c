<template>
  <n-form
    ref="titleFormRef"
    :model="currentEditComponent"
    label-placement="left"
    label-align="left"
    label-width="100"
  >
    <div>
      <div class="edit-attr-item-wrap" v-show="decorationStore.editTab === 'content'">
        <!-- 内容 -->
        <edit-section :title="t('decoration.common.carouselImage')">
          <n-form-item :label="t('decoration.common.carouselImageInterval')">
            <n-input-number
              :value="currentEditComponent.intervalNum"
              :show-button="false"
              :validator="intervalValidator"
              :placeholder="t('decoration.common.carouselImageIntervalPlaceholder')"
              @update:value="(val) => decorationStore.updateEditComponent('intervalNum', val)"
            />
            <n-text class="pl-[10px]">
              {{ t('decoration.common.intervalTips') }}
            </n-text>
          </n-form-item>
          <div ref="imageBoxRef">
            <link-selector
              v-model="currentEditComponent.list"
              @update:model-value="(val) => decorationStore.updateEditComponent('list', val)"
              mode="img"
              :minNumber="2"
              :maxNumber="10"
            />
          </div>
        </edit-section>
      </div>

      <!-- 样式 -->
      <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
        <edit-section :title="t('decoration.common.carouselImageSet')">
          <n-form-item :label="t('decoration.common.carouselImageRadiusTopLeft')">
            <slider-input
              :value="currentEditComponent.imageRadiusTopLeft"
              @update:value="
                (val) => decorationStore.updateEditComponent('imageRadiusTopLeft', val)
              "
              :min="0"
              :max="100"
              :default-value="10"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.carouselImageRadiusBottomRight')">
            <slider-input
              :value="currentEditComponent.imageRadiusBottomLeft"
              @update:value="
                (val) => decorationStore.updateEditComponent('imageRadiusBottomLeft', val)
              "
              :min="0"
              :max="100"
              :default-value="10"
            />
          </n-form-item>
        </edit-section>
        <edit-section :title="t('decoration.common.carouselImageIndicator')">
          <n-form-item :label="t('decoration.common.carouselImageIndicatorStyle')">
            <n-radio-group
              :value="currentEditComponent.indicatorStyle"
              name="radiogroup"
              @update:value="(val) => decorationStore.updateEditComponent('indicatorStyle', val)"
            >
              <n-space>
                <n-radio v-for="item in indicatorList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="t('decoration.common.carouselImageIndicatorPosition')">
            <n-radio-group
              :value="currentEditComponent.indicatorPosition"
              name="radiogroup"
              @update:value="(val) => decorationStore.updateEditComponent('indicatorPosition', val)"
            >
              <n-space>
                <n-radio
                  v-for="item in indicatorPositionList"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="t('decoration.common.carouselImageIndicatorSelectedColor')">
            <single-color-picker
              :value="currentEditComponent.indicatorSelectedColor"
              @update:value="
                (val) => decorationStore.updateEditComponent('indicatorSelectedColor', val)
              "
              defaultValue="#E93323"
              placeholder="#FFFFFF"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.carouselImageConventionColor')">
            <single-color-picker
              :value="currentEditComponent.conventionColor"
              @update:value="
                (val) => decorationStore.updateEditComponent('indicatorSelectedColor', val)
              "
              defaultValue="#D7D7D7"
              placeholder="#FFFFFF"
            />
          </n-form-item>
        </edit-section>
        <!-- 组件样式 -->
        <slot name="style"></slot>
      </div>
    </div>
  </n-form>

  <!-- </n-form> -->
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick, computed } from 'vue'
  import { useDecorationStore } from '@/store/modules/decoration'
  import Sortable from 'sortablejs'
  import { range } from 'lodash-es'
  import { useI18n } from '@/hooks/web/useI18n'
  import { CarouselImageComponent } from '../../types'
  import LinkSelector from '@/components/SelectLink/src/linkSelector.vue'
  import { LinkSelectorItem } from '@/components/SelectLink'

  interface IndicatorOption {
    label: string
    value: 'circle' | 'square' | 'triangle' | 'block'
  }
  /**
   * 指示器的位置
   */
  interface PositionOption {
    label: string
    value: 'left' | 'center' | 'right'
  }
  const { t } = useI18n()
  const decorationStore = useDecorationStore()
  let imageBoxRef = ref()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<CarouselImageComponent>(),
  )
  // 修改变量定义
  const indicatorList = ref<IndicatorOption[]>([
    {
      label: '样式一',
      value: 'circle',
    },
    {
      label: '样式二',
      value: 'square',
    },
    {
      label: '样式三',
      value: 'triangle',
    },
    {
      label: '样式四',
      value: 'block',
    },
  ])

  const indicatorPositionList = ref<PositionOption[]>([
    {
      label: '左对齐',
      value: 'left',
    },
    {
      label: '居中对齐',
      value: 'center',
    },
    {
      label: '右对齐',
      value: 'right',
    },
  ])

  // 修改验证器函数
  const intervalValidator = (val: number | null | undefined): boolean => {
    return val !== null && val !== undefined && val > 0
  }
  currentEditComponent.value.list.forEach((item: LinkSelectorItem) => {
    if (!item.id) item.id = decorationStore.generateRandom()
  })
  // TODO：组件验证,后续看接口处理

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', [
    'containerMargin',
    'textColor',
    'itemStyle',
    'borderRadius',
  ])

  onMounted(() => {
    nextTick(() => {
      if (imageBoxRef.value) {
        const sortable = Sortable.create(imageBoxRef.value, {
          group: 'item-wrap',
          animation: 200,
          onEnd: (event) => {
            const temp = currentEditComponent.value.list[event.oldIndex!]
            currentEditComponent.value.list.splice(event.oldIndex!, 1)
            currentEditComponent.value.list.splice(event.newIndex!, 0, temp)
            sortable.sort(
              range(currentEditComponent.value.list.length).map((value) => {
                return value.toString()
              }),
            )
          },
        })
      }
    })
  })
  defineExpose({})
</script>

<style lang="scss" scoped></style>
