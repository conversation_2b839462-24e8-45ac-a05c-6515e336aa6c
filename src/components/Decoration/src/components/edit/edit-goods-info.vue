<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 商品信息 -->
      <edit-section :title="t('decoration.common.goodsInfo')">
        <n-form-item :label="t('decoration.common.titleContent')">
          <n-checkbox-group
            v-model:value="currentEditComponent.showInfo"
            @update:value="(val) => decorationStore.updateEditComponent('showInfo', val)"
          >
            <n-grid x-gap="5" y-gap="10" :cols="3">
              <n-gi v-for="(item, index) in infoList" :key="index">
                <n-checkbox :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, onMounted, computed } from 'vue'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import { GoodsInfoComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  //展示信息
  const infoList = [
    {
      name: t('decoration.common.goodsInfoPrice'),
      value: 'goodsInfoPrice',
    },
    {
      name: t('decoration.common.goodsInfoCollect'),
      value: 'goodsInfoCollect',
    },
    {
      name: t('decoration.common.goodsInfoShare'),
      value: 'goodsInfoShare',
    },
    {
      name: t('decoration.common.goodsInfoTitle'),
      value: 'goodsInfoTitle',
    },
    {
      name: t('decoration.common.goodsInfoSubTitle'),
      value: 'goodsInfoSubTitle',
    },
    {
      name: t('decoration.common.goodsInfoSubSale'),
      value: 'goodsInfoSubSale',
    },
    {
      name: t('decoration.common.goodsInfoVolume'),
      value: 'goodsInfoVolume',
    },
    {
      name: t('decoration.common.goodsInfoUsePrice'),
      value: 'goodsInfoUsePrice',
    },
    {
      name: t('decoration.common.pointsGiveaway'),
      value: 'pointsGiveaway',
    },
    {
      name: t('decoration.common.pointsDeduction'),
      value: 'pointsDeduction',
    },
    {
      name: t('decoration.common.goodsInfoService'),
      value: 'goodsInfoService',
    },
  ]

  const typeOptions = ref<typeOptions[]>([])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsInfoComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  onMounted(() => {
    getGoodsTypeList()
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
