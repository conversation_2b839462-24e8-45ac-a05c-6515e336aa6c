<template>
  <n-form label-width="80" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 展示设置 -->
      <edit-section :title="t('decoration.common.displaySettings')">
        <!-- 风格 -->
        <n-form-item :label="t('decoration.common.selectLine')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="radiogroup"
            @update:value="(val) => handleStyleChange(val)"
          >
            <n-space>
              <n-radio value="solid" :label="t('decoration.common.solidLine')" />
              <n-radio value="dashed" :label="t('decoration.common.dashedLine')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 线条颜色 -->
        <n-form-item :label="t('decoration.common.lineColor')">
          <single-color-picker
            :value="currentEditComponent.lineColor"
            default-value="#333333"
            @update:value="(val) => decorationStore.updateEditComponent('lineColor', val)"
          />
        </n-form-item>
        <!-- 线条宽度 -->
        <n-form-item :label="t('decoration.common.lineWidth')">
          <slider-input
            v-model:value="currentEditComponent.lineWidth"
            :min="0"
            :max="50"
            :default-value="1"
            @update:value="(val) => decorationStore.updateEditComponent('lineWidth', val)"
          />
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { SingleColorPicker } from '@/components/ColorPicker'
  import { HorizontalLineComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<HorizontalLineComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', [
    'itemStyle',
    'containerMargin',
    'textColor',
    'borderRadius',
    'background',
    'containerGradient',
  ])

  // 处理样式变化
  const handleStyleChange = (style: string) => {
    decorationStore.updateEditComponent('style', style, () => {})
  }

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    position: relative;
    top: -20px;
    font-size: 12px;
    color: #999999;
  }
  .add-hot__btn {
    width: 100%;
  }
</style>
