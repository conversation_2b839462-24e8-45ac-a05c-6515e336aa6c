<template>
  <div class="edit-graphic-nav">
    <!-- 内容 -->
    <div class="content-wrap" v-show="decorationStore.editTab == 'content'">
      <edit-section :title="t('decoration.common.graphicNavModeTitle')">
        <n-form label-width="80px" class="px-[10px]" label-placement="left" label-align="left">
          <n-form-item :label="t('decoration.common.graphicNavSelectMode')">
            <n-radio-group
              :value="currentEditComponent.mode"
              @update:value="decorationStore.updateEditComponent('mode', $event)"
            >
              <n-space>
                <n-radio value="graphic">
                  {{ t('decoration.common.modeGraphic') }}
                </n-radio>
                <n-radio value="img">
                  {{ t('decoration.common.modeImg') }}
                </n-radio>
                <n-radio value="text">
                  {{ t('decoration.common.modeText') }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item :label="t('decoration.common.graphicNavColumnCount')">
            <n-radio-group
              :value="currentEditComponent.columnCount"
              @update:value="decorationStore.updateEditComponent('columnCount', $event)"
            >
              <n-space>
                <n-radio :value="3">3{{ t('decoration.common.piece') }}</n-radio>
                <n-radio :value="4">4{{ t('decoration.common.piece') }}</n-radio>
                <n-radio :value="5">5{{ t('decoration.common.piece') }}</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>

          <n-form-item :label="t('decoration.common.graphicNavShowStyle')">
            <n-radio-group
              :value="currentEditComponent.showStyle"
              @update:value="decorationStore.updateEditComponent('showStyle', $event)"
            >
              <n-space>
                <n-radio value="fixed">{{ t('decoration.common.graphicNavStyleFixed') }}</n-radio>
                <n-radio value="pageSlide">
                  {{ t('decoration.common.graphicNavStylePageSlide') }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            :label="t('decoration.common.graphicNavRowCount')"
            v-show="currentEditComponent.showStyle == 'pageSlide'"
          >
            <n-radio-group
              :value="currentEditComponent.rowCount"
              @update:value="decorationStore.updateEditComponent('rowCount', $event)"
            >
              <n-space>
                <n-radio :value="1">{{ t('decoration.common.oneLine') }}</n-radio>
                <n-radio :value="2">{{ t('decoration.common.twoLine') }}</n-radio>
                <n-radio :value="3">{{ t('decoration.common.threeLine') }}</n-radio>
                <n-radio :value="4">{{ t('decoration.common.fourLine') }}</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
        </n-form>
      </edit-section>

      <edit-section :title="t('decoration.common.titleContentSet')">
        <n-form label-width="80px" class="pr-[10px]" label-placement="left" label-align="left">
          <p class="text-sm text-gray-400 mb-[10px]">
            {{ t('decoration.common.iconGroupLinkTips') }}
          </p>
          <div ref="imageBoxRef ">
            <link-selector
              v-model="currentEditComponent.list"
              @update:model-value="(val) => decorationStore.updateEditComponent('list', val)"
              mode="graphic"
              :maxNumber="30"
              showLabel
            />
          </div>
        </n-form>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab == 'style'">
      <edit-section
        :title="t('decoration.common.imageSet')"
        v-show="['graphic', 'img'].includes(currentEditComponent.mode)"
      >
        <n-form label-width="80px" class="px-[10px]" label-placement="left" label-align="left">
          <n-form-item :label="t('decoration.common.graphicNavImageShape')">
            <n-space :size="40">
              <div
                v-for="item in shapeOptions"
                :key="item.value"
                class="w-[35px] h-[35px] bg-[#D8D8D8] border"
                :class="{
                  'border-[var(--primary-color)]': currentEditComponent.imageShape === item.value,
                  'rounded-[50%]': item.value === 'circle',
                  'rounded-md': item.value === 'rounded',
                }"
                @click="decorationStore.updateEditComponent('imageShape', item.value)"
              ></div>
            </n-space>
          </n-form-item>
        </n-form>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted, nextTick, computed } from 'vue'

  import Sortable from 'sortablejs'
  import { img } from '@/utils/common'
  import { range } from 'lodash-es'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { GraphicNavComponent } from '../../types'

  const decorationStore = useDecorationStore()
  const { t } = useI18n()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GraphicNavComponent>(),
  )
  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  // TODO: 组件验证
  // currentEditComponent.value.verify = (index: number) => {
  //   const res = { code: true, message: '' }

  //   currentEditComponent.value.list.forEach((item: any) => {
  //     if (
  //       (currentEditComponent.value.mode === 'graphic' ||
  //         currentEditComponent.value.mode === 'img') &&
  //       item.imageUrl === ''
  //     ) {
  //       res.code = false
  //       res.message = t('decoration.common.imageUrlTip')
  //       return res
  //     }
  //     if (
  //       (currentEditComponent.value.mode === 'graphic' ||
  //         currentEditComponent.value.mode === 'text') &&
  //       item.title === ''
  //     ) {
  //       res.code = false
  //       res.message = t('decoration.common.graphicNavTitlePlaceholder')
  //       return res
  //     }
  //   })
  //   return res
  // }

  currentEditComponent.value.list.forEach((item: any) => {
    if (!item.id) item.id = decorationStore.generateRandom()
  })

  watch(
    () => currentEditComponent.value.list,
    () => {
      // 设置图片宽高
      currentEditComponent.value.list.forEach((item: any) => {
        const image = new Image()
        image.src = img(item.imageUrl)
        image.onload = async () => {
          item.imgWidth = image.width
          item.imgHeight = image.height
        }
      })
    },
    { deep: true },
  )

  const shapeOptions = [
    { label: '圆形', value: 'circle' },
    { label: '方形', value: 'square' },
    { label: '圆角', value: 'rounded' },
  ]

  const imageBoxRef = ref()
  onMounted(() => {
    nextTick(() => {
      if (imageBoxRef.value) {
        const sortable = Sortable.create(imageBoxRef.value, {
          group: 'item-wrap',
          animation: 200,
          onEnd: (event) => {
            const temp = currentEditComponent.value.list[event.oldIndex!]
            currentEditComponent.value.list.splice(event.oldIndex!, 1)
            currentEditComponent.value.list.splice(event.newIndex!, 0, temp)
            sortable.sort(
              range(currentEditComponent.value.list.length).map((value) => {
                return value.toString()
              }),
            )
          },
        })
      }
    })
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
