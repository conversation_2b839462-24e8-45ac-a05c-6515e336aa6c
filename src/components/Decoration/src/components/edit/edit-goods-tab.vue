<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 展示设置 -->
      <edit-section :title="t('decoration.common.displaySettings')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.tabStyle"
            name="tabgroup"
            @update:value="(val) => decorationStore.updateEditComponent('tabStyle', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.tabStyle1')" />
              <n-radio :value="2" :label="t('decoration.common.tabStyle2')" />
              <n-radio :value="3" :label="t('decoration.common.tabStyle3')" />
              <n-radio :value="4" :label="t('decoration.common.tabStyle4')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 选项卡设置 -->
      <edit-section :title="t('decoration.common.tabSet')">
        <div class="section-tips mt-4">{{ t('decoration.common.tabSetTips') }}</div>
        <EditTabDraggableItem
          :tabStyle="currentEditComponent.tabStyle"
          :list="tabList"
          @change-tab="changeTabItem"
        >
          <template #addBtn>
            <n-button class="w-full mb-5" type="primary" @click="addTabList">
              {{ `+${t('decoration.common.addText')}(${tabList.length}/20)` }}
            </n-button>
          </template>
        </EditTabDraggableItem>
      </edit-section>
      <!-- 列表设置 -->
      <edit-section :title="t('decoration.common.goodsShow')">
        <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="stylegroup"
            @update:value="(val) => decorationStore.updateEditComponent('style', val)"
          >
            <n-grid y-gap="12" :cols="2">
              <n-gi v-for="(item, index) in styleList" :key="index">
                <n-radio :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 显示内容 -->
      <edit-section :title="t('decoration.common.showContent')">
        <!-- 展示信息 -->
        <n-form-item :label="t('decoration.common.showInfo')">
          <n-checkbox-group
            v-model:value="currentEditComponent.showInfo"
            @update:value="(val) => decorationStore.updateEditComponent('showInfo', val)"
          >
            <n-grid x-gap="24" y-gap="12" :cols="2">
              <n-gi v-for="(item, index) in infoList" :key="index">
                <n-checkbox :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form-item>
      </edit-section>
      <!-- 购物车按钮 -->
      <edit-section :title="t('decoration.common.shoppingCarBtn')">
        <!-- 是否显示 -->
        <n-form-item :label="t('decoration.common.isShowCar')">
          <n-radio-group
            :value="currentEditComponent.isShowCar"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('isShowCar', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.showComponent')" />
              <n-radio class="ml-7" :value="0" :label="t('decoration.common.hideComponent')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 按钮样式 -->
        <n-form-item
          :label="t('decoration.common.carBtnStyle')"
          v-if="currentEditComponent.isShowCar === 1"
        >
          <n-space>
            <n-tag
              round
              class="cursor-pointer"
              :type="currentEditComponent.carBtnStyle === 1 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(1)"
            >
              <n-button size="tiny" color="#EB3534" round>
                {{ currentEditComponent.carBtnText }}
              </n-button>
            </n-tag>
            <n-tag
              round
              class="cursor-pointer ml-3"
              :type="currentEditComponent.carBtnStyle === 2 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(2)"
            >
              <n-avatar :src="catBtnStyle1" color="#EB3534" />
            </n-tag>
            <n-tag
              round
              class="cursor-pointer ml-3"
              :type="currentEditComponent.carBtnStyle === 3 ? 'primary' : 'default'"
              @click="() => handleCarBtnStyle(3)"
            >
              <n-avatar :src="catBtnStyle2" color="#EB3534" />
            </n-tag>
          </n-space>
        </n-form-item>
        <!-- 按钮文字 -->
        <n-form-item
          :label="t('decoration.common.buttonText')"
          v-if="currentEditComponent.isShowCar === 1 && currentEditComponent.carBtnStyle === 1"
        >
          <n-input
            :value="currentEditComponent.carBtnText"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnText', val)"
            :placeholder="t('decoration.common.placeholderCarContent')"
            clearable
            maxlength="2"
            show-count
          />
        </n-form-item>
        <!-- 按钮效果 -->
        <n-form-item
          :label="t('decoration.common.carBtnEffect')"
          v-if="currentEditComponent.isShowCar === 1"
        >
          <n-radio-group
            :value="currentEditComponent.carBtnEffect"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnEffect', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.carBtnEffect1')" />
              <n-radio class="ml-7" :value="0" :label="t('decoration.common.carBtnEffect2')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
      <!-- 角标设置 -->
      <edit-section :title="t('decoration.common.subscriptSet')">
        <!-- 是否显示 -->
        <n-form-item :label="t('decoration.common.subscriptStyle')">
          <n-radio-group
            :value="currentEditComponent.subscriptStyle"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('subscriptStyle', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.subscriptStyle1')" />
              <n-radio :value="2" :label="t('decoration.common.subscriptStyle2')" />
              <n-radio :value="3" :label="t('decoration.common.subscriptStyle3')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 选择风格 -->
        <n-form-item
          :label="t('decoration.common.selectStyle')"
          v-if="currentEditComponent.subscriptStyle === 2"
        >
          <n-space>
            <n-tag
              class="cursor-pointer sub-btn__box"
              :type="currentEditComponent.subBtnStyle === index + 1 ? 'primary' : 'default'"
              @click="() => handleSubBtnStyle(item.type)"
              v-for="(item, index) in subBtnList"
              :key="index"
            >
              <img :src="item.imgUrl" :style="{ width: `${index === 1 ? '30px' : '42px'}` }" />
              <div
                class="sub-btn__text"
                :style="{
                  top: `${index === 2 ? '12px' : '7px'}`,
                  left: `${index === 1 ? '11px' : '14px'}`,
                }"
              >
                {{ currentEditComponent.subBtnText }}
              </div>
            </n-tag>
          </n-space>
        </n-form-item>
        <!-- 角标文字 -->
        <n-form-item
          :label="t('decoration.common.subBtnText')"
          v-if="currentEditComponent.subscriptStyle === 2"
        >
          <n-input
            :value="currentEditComponent.subBtnText"
            @update:value="(val) => decorationStore.updateEditComponent('subBtnText', val)"
            :placeholder="t('decoration.common.placeholderCarContent')"
            clearable
            maxlength="2"
            show-count
          />
        </n-form-item>
        <!-- 添加图片 -->
        <n-form-item
          :label="t('decoration.common.subBtnImg')"
          v-if="currentEditComponent.subscriptStyle === 3"
        >
          <n-space vertical>
            <AttachmentUpload v-model:value="currentEditComponent.subBtnImg" :maxNumber="1" />
            <div class="section-tips mt-4">{{ t('decoration.common.subBtnImgSize') }}</div>
          </n-space>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 选项卡样式 -->
      <edit-section :title="t('decoration.common.tabStyle')">
        <!-- 背景 -->
        <n-form-item :label="t('decoration.common.tabBackground')">
          <single-color-picker
            :value="currentEditComponent.tabBackground"
            default-value="#ffffff"
            @update:value="(val) => decorationStore.updateEditComponent('tabBackground', val)"
          />
        </n-form-item>
        <!-- 标题未选 -->
        <n-form-item :label="t('decoration.common.tabTitleUnCheckColor')">
          <single-color-picker
            :value="currentEditComponent.tabTitleUnCheckColor"
            default-value="#999999"
            @update:value="
              (val) => decorationStore.updateEditComponent('tabTitleUnCheckColor', val)
            "
          />
        </n-form-item>
        <!-- 标题选中 -->
        <n-form-item :label="t('decoration.common.tabTitleCheckColor')">
          <single-color-picker
            :value="currentEditComponent.tabTitleCheckColor"
            default-value="#EB3534"
            @update:value="(val) => decorationStore.updateEditComponent('tabTitleCheckColor', val)"
          />
        </n-form-item>
        <!-- 选项条 -->
        <n-form-item
          :label="t('decoration.common.isTabLine')"
          v-if="currentEditComponent.tabStyle === 1"
        >
          <n-radio-group
            :value="currentEditComponent.isTabLine"
            name="cargroup"
            @update:value="(val) => handTabLine(1, val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.isTrue')" />
              <n-radio :value="0" :label="t('decoration.common.isFalse')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 副标题 -->
        <n-form-item
          :label="t('decoration.common.isTabSubTitle')"
          v-if="currentEditComponent.tabStyle === 1"
        >
          <n-radio-group
            :value="currentEditComponent.isTabSubTitle"
            name="cargroup"
            @update:value="(val) => handTabLine(2, val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.isTrue')" />
              <n-radio :value="0" :label="t('decoration.common.isFalse')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 未选中 -->
        <n-form-item
          :label="t('decoration.common.unchecked')"
          v-if="currentEditComponent.tabStyle === 1"
        >
          <single-color-picker
            :value="currentEditComponent.unSubTitleColor"
            default-value="#999999"
            @update:value="(val) => decorationStore.updateEditComponent('unSubTitleColor', val)"
          />
        </n-form-item>
        <!-- 选中 -->
        <n-form-item
          :label="t('decoration.common.checked')"
          v-if="currentEditComponent.tabStyle === 1"
        >
          <single-color-picker
            :value="currentEditComponent.subTitleColor"
            default-value="#ffffff"
            @update:value="(val) => decorationStore.updateEditComponent('subTitleColor', val)"
          />
        </n-form-item>
        <!-- 框颜色 -->
        <n-form-item
          :label="t('decoration.common.boxColor')"
          v-if="currentEditComponent.tabStyle === 3 || currentEditComponent.tabStyle === 4"
        >
          <single-color-picker
            :value="currentEditComponent.boxColor"
            default-value="#EB3534"
            @update:value="(val) => decorationStore.updateEditComponent('boxColor', val)"
          />
        </n-form-item>
      </edit-section>
      <!-- 商品样式 -->
      <edit-section :title="t('decoration.common.goodsStyle')">
        <!-- 商品名称 -->
        <n-form-item :label="t('decoration.common.goodsNameStyle')">
          <n-radio-group
            :value="currentEditComponent.goodsNameStyle"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('goodsNameStyle', val)"
          >
            <n-space>
              <n-radio value="normal" :label="t('decoration.common.goodsNameStyle1')" />
              <n-radio value="bold" :label="t('decoration.common.goodsNameStyle2')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <!-- 文字颜色 -->
        <n-form-item :label="t('decoration.common.textColor')">
          <single-color-picker
            :value="currentEditComponent.goodsNameColor"
            default-value="#333333"
            @update:value="(val) => decorationStore.updateEditComponent('goodsNameColor', val)"
          />
        </n-form-item>
        <!-- 上圆角 -->
        <n-form-item :label="t('decoration.common.imgBuildRadiusTop')">
          <slider-input
            v-model:value="currentEditComponent.imgBuildRadiusTop"
            :min="0"
            :max="50"
            :default-value="5"
            @update:value="(val) => decorationStore.updateEditComponent('imgBuildRadiusTop', val)"
          />
        </n-form-item>
        <!-- 下圆角 -->
        <n-form-item :label="t('decoration.common.imgBuildRadiusBottom')">
          <slider-input
            v-model:value="currentEditComponent.imgBuildRadiusBottom"
            :min="0"
            :max="50"
            :default-value="5"
            @update:value="
              (val) => decorationStore.updateEditComponent('imgBuildRadiusBottom', val)
            "
          />
        </n-form-item>
      </edit-section>
      <!-- 按钮样式 -->
      <edit-section :title="t('decoration.common.buyBtn')">
        <!-- 文字颜色 -->
        <n-form-item :label="t('decoration.common.textColor')">
          <single-color-picker
            :value="currentEditComponent.carBtnTextColor"
            default-value="#ffffff"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnTextColor', val)"
          />
        </n-form-item>
        <!-- 按钮颜色 -->
        <n-form-item :label="t('decoration.common.buyBtnColor')">
          <gradient-color-picker
            v-model:startColor="currentEditComponent.shoppingCarStyle.startColor"
            v-model:endColor="currentEditComponent.shoppingCarStyle.endColor"
            v-model:angle="currentEditComponent.shoppingCarStyle.angle"
            :defaultStartColor="'#EB3534'"
            :defaultEndColor="'#EB3534'"
            show-angle-control
            @change="(val) => decorationStore.updateEditComponent('shoppingCarStyle', val)"
          />
        </n-form-item>
        <!-- 圆角 -->
        <n-form-item :label="t('decoration.common.carBtnTBuildRadius')">
          <slider-input
            v-model:value="currentEditComponent.carBtnTBuildRadius"
            :min="0"
            :max="20"
            :default-value="20"
            @update:value="(val) => decorationStore.updateEditComponent('carBtnTBuildRadius', val)"
          />
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { computed } from 'vue'
  import { SingleColorPicker } from '@/components/ColorPicker'
  import catBtnStyle1 from '@/assets/images/decoration/goodsList/catBtnStyle1.png'
  import catBtnStyle2 from '@/assets/images/decoration/goodsList/catBtnStyle2.png'
  import subBtnStyle1 from '@/assets/images/decoration/goodsList/subBtnStyle1.png'
  import subBtnStyle2 from '@/assets/images/decoration/goodsList/subBtnStyle2.png'
  import subBtnStyle3 from '@/assets/images/decoration/goodsList/subBtnStyle3.png'
  import { GoodsListTabComponent } from '../../types'
  import EditTabDraggableItem from '../EditTabDraggableItem/src/EditTabDraggableItem.vue'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsListTabComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  //展示风格
  const styleList = [
    {
      name: t('decoration.common.oneCols'),
      value: 0,
    },
    {
      name: t('decoration.common.towColsVertical'),
      value: 1,
    },
    {
      name: t('decoration.common.threeColsVertical'),
      value: 2,
    },
    {
      name: t('decoration.common.towColsHorizontal'),
      value: 3,
    },
    {
      name: t('decoration.common.bigImage'),
      value: 4,
    },
    {
      name: t('decoration.common.toSlide'),
      value: 5,
    },
  ]

  //展示信息
  const infoList = [
    {
      name: t('decoration.common.goodsName'),
      value: 'goodsName',
    },
    {
      name: t('decoration.common.goodsSubName'),
      value: 'goodsSubName',
    },
    {
      name: t('decoration.common.goodsPrice'),
      value: 'goodsPrice',
    },
    {
      name: t('decoration.common.originalPrice'),
      value: 'originalPrice',
    },
    {
      name: t('decoration.common.goodsSale'),
      value: 'goodsSale',
    },
    {
      name: t('decoration.common.userPrice'),
      value: 'userPrice',
    },
  ]

  //角标风格
  const subBtnList = [
    {
      type: 1,
      imgUrl: subBtnStyle1,
    },
    {
      type: 2,
      imgUrl: subBtnStyle2,
    },
    {
      type: 3,
      imgUrl: subBtnStyle3,
    },
  ]

  const tabList = computed(() => {
    if (currentEditComponent.value.tabList.length > 0) {
      return currentEditComponent.value.tabList
    } else {
      return [
        {
          title: '推荐',
          subTitle: '猜你喜欢',
          icon: '',
          checkType: 0,
          goodIds: [],
          cateIds: [],
          goodsNum: 0,
          goodsSort: 'sortAll',
        },
      ]
    }
  })

  /* 选择购物车按钮样式 */
  const handleCarBtnStyle = (type: number) => {
    decorationStore.updateEditComponent('carBtnStyle', type, () => {})
  }

  /* 选择角标样式 */
  const handleSubBtnStyle = (type: number) => {
    decorationStore.updateEditComponent('subBtnStyle', type, () => {})
  }

  /* 修改保存选项卡 */
  const changeTabList = () => {
    decorationStore.updateEditComponent('tabList', tabList.value)
  }

  const changeTabItem = (val) => {
    decorationStore.updateEditComponent('tabList', val)
  }

  /* 添加选项卡 */
  const addTabList = () => {
    if (tabList.value.length >= 20) return
    let item = {
      title: '标题',
      subTitle: '副标题',
      icon: '',
      checkType: 0,
      goodIds: [],
      cateIds: [],
      goodsNum: 0,
      goodsSort: 'sortAll',
    }
    tabList.value.push(item)
    changeTabList()
  }

  const handTabLine = (type, val) => {
    if (type === 1) {
      decorationStore.updateEditComponent('isTabLine', val)
      //decorationStore.updateEditComponent('isTabSubTitle', val === 1 ? 0 : 1)
    } else {
      decorationStore.updateEditComponent('isTabSubTitle', val)
      //decorationStore.updateEditComponent('isTabLine', val === 1 ? 0 : 1)
    }
  }

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    position: relative;
    top: -20px;
    font-size: 12px;
    color: #999999;
  }
  .sub-btn__box {
    position: relative;
    img {
      width: 42px;
      height: 24px;
    }
  }
  .sub-btn__text {
    position: absolute;
    top: 7px;
    left: 14px;
    font-size: 10px;
    color: #ffffff;
  }
</style>
