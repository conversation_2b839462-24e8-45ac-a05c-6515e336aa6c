<template>
  <n-form label-width="110" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 显示状态 -->
      <edit-section :title="t('decoration.common.goodsInfoStatus')">
        <n-form-item :label="t('decoration.common.isShowCar')">
          <n-radio-group
            :value="currentEditComponent.isShowEvaluate"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('isShowEvaluate', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.showComponent')" />
              <n-radio :value="0" :label="t('decoration.common.hideComponent')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item :label="t('decoration.common.isShowNum')">
          <n-radio-group
            :value="currentEditComponent.isShowNum"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('isShowNum', val)"
          >
            <n-space>
              <n-radio :value="1" label="1" />
              <n-radio :value="2" label="2" />
              <n-radio :value="3" label="3" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, onMounted, computed } from 'vue'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import { GoodsEvaluateComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  const typeOptions = ref<typeOptions[]>([])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsEvaluateComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  onMounted(() => {
    getGoodsTypeList()
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
