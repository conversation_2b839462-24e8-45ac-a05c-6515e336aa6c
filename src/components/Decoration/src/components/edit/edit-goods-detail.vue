<template>
  <n-form label-width="110" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 显示状态 -->
      <edit-section :title="t('decoration.common.goodsDetailSet')">
        <n-form-item :label="t('decoration.common.detailTop')">
          <slider-input
            v-model:value="currentEditComponent.detailTop"
            :min="0"
            :max="50"
            :default-value="0"
            @update:value="(val) => decorationStore.updateEditComponent('detailTop', val)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.detailBottom')">
          <slider-input
            v-model:value="currentEditComponent.detailBottom"
            :min="0"
            :max="50"
            :default-value="0"
            @update:value="(val) => decorationStore.updateEditComponent('detailBottom', val)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.detailLeftOrRight')">
          <slider-input
            v-model:value="currentEditComponent.detailLeftOrRight"
            :min="0"
            :max="50"
            :default-value="0"
            @update:value="(val) => decorationStore.updateEditComponent('detailLeftOrRight', val)"
          />
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, onMounted, computed } from 'vue'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import { GoodsDetailComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  const typeOptions = ref<typeOptions[]>([])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsDetailComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  onMounted(() => {
    getGoodsTypeList()
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
