<template>
  <!-- 内容 -->
  <div>
    <div class="content-wrap" v-show="decorationStore.editTab == 'content'">
      <div class="edit-attr-item-wrap">
        <h3 class="mb-[10px]">{{ t('decoration.common.richTextContentSet') }}</h3>
        <QuillRichEditor v-model="currentEditComponent.html" height="600px" />
      </div>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab == 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { RichTextComponent } from '../../types'
  import QuillRichEditor from '@/components/Editor/QuillRichEditor.vue'

  const decorationStore = useDecorationStore()
  const { t } = useI18n()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<RichTextComponent>())

  // 更新editComponent的属性
  const updateEditComponent = (path: string, value: any) => {
    try {
      decorationStore.updateEditComponent(path, value)
    } catch (error) {
      console.error('更新组件属性失败:', error)
    }
  }
  onMounted(() => {
    // 初始化时设置ignore属性
    updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
