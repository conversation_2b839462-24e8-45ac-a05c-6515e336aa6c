<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 菜单信息 -->
      <edit-section :title="t('decoration.common.menuInfo')">
        <!-- 内容设置 -->
        <n-form-item :label="t('decoration.common.titleContent')">
          <n-checkbox-group
            v-model:value="currentEditComponent.menuInfo"
            @update:value="(val) => decorationStore.updateEditComponent('menuInfo', val)"
          >
            <n-grid x-gap="5" y-gap="10" :cols="3">
              <n-gi v-for="(item, index) in infoList" :key="index">
                <n-checkbox :value="item.value" :label="item.name" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form-item>
        <!-- 首页文字 -->
        <n-form-item :label="t('decoration.common.homeText')">
          <n-input
            :value="currentEditComponent.homeText"
            @update:value="(val) => decorationStore.updateEditComponent('homeText', val)"
            :placeholder="t('decoration.common.placeholderHomeText')"
            clearable
            maxlength="3"
            show-count
          />
        </n-form-item>
        <!-- 加购按钮 -->
        <n-form-item :label="t('decoration.common.showBuyBtn')">
          <n-radio-group
            :value="currentEditComponent.showBuyBtn"
            name="cargroup"
            @update:value="(val) => decorationStore.updateEditComponent('showBuyBtn', val)"
          >
            <n-space>
              <n-radio :value="1" :label="t('decoration.common.showComponent')" />
              <n-radio :value="0" :label="t('decoration.common.hideComponent')" />
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, onMounted, computed } from 'vue'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import { GoodsMenuComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  //展示信息
  const infoList = [
    {
      name: t('decoration.common.home'),
      value: 'home',
    },
    {
      name: t('decoration.common.customer'),
      value: 'customer',
    },
    {
      name: t('decoration.common.shoppingCar'),
      value: 'shoppingCar',
    },
  ]

  const typeOptions = ref<typeOptions[]>([])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<GoodsMenuComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  onMounted(() => {
    getGoodsTypeList()
  })

  defineExpose({})
</script>

<style lang="scss" scoped></style>
