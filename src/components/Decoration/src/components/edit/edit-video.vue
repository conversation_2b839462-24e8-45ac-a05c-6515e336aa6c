<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.videoContent')">
        <n-form-item :label="t('decoration.common.videoType')">
          <n-radio-group
            :value="currentEditComponent.videoType"
            name="radiogroup"
            @update:value="(val) => decorationStore.updateEditComponent('videoType', val)"
          >
            <n-space>
              <n-radio v-for="item in videoTypeList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          :label="t('decoration.common.uploadVideo')"
          v-show="currentEditComponent.videoType === 'upFile'"
        >
          <AttachmentUpload
            class="w-auto"
            type="video"
            v-model:value="currentEditComponent.videoUrl"
            @update:value="upVideoChange"
            :maxNumber="1"
          />
        </n-form-item>
        <n-form-item
          :label="t('decoration.common.videoUrl')"
          v-show="currentEditComponent.videoType === 'url'"
        >
          <n-input
            :value="currentEditComponent.thirdUrl"
            type="text"
            :placeholder="t('decoration.common.thirdUrlPlaceholder')"
            clearable
            @update:value="(val) => decorationStore.updateEditComponent('thirdUrl', val)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.posterUrl')">
          <AttachmentUpload
            class="w-auto"
            v-model:value="currentEditComponent.posterUrl"
            :maxNumber="1"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.videoRatio')">
          <n-radio-group
            :value="currentEditComponent.videoRatio"
            name="radiogroup"
            @update:value="(val) => decorationStore.updateEditComponent('videoRatio', val)"
          >
            <n-space>
              <n-radio v-for="item in videoRatio" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
      </edit-section>
    </div>
    <!--样式-->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { VideoComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  let videoTypeList = ref([
    {
      value: 'upFile',
      label: '手动上传',
    },
    {
      value: 'url',
      label: '视频链接',
    },
  ])
  let videoRatio = ref([
    {
      value: '16:9',
      label: '16:9',
    },
    {
      value: '4:3',
      label: '4:3',
    },
    {
      value: '1:1',
      label: '1:1',
    },
  ])

  /** 初始化时设置ignore属性 */
  decorationStore.updateEditComponent('ignore', [
    'itemStyle',
    'containerMargin',
    'textColor',
    'background',
  ])

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<VideoComponent>())

  const upVideoChange = (val) => {
    decorationStore.updateEditComponent('style', val, (component: VideoComponent) => {
      component.videoUrl = val
    })
  }
</script>
<style lang="scss" scoped>
  .video-class {
    :deep(.video-box) {
      width: 180px;
      height: auto;
    }
  }
</style>
