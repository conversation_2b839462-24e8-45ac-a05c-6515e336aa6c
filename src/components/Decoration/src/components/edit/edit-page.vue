<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div class="content-wrap" v-show="decorationStore.editTab === 'content'">
      <edit-section>
        <n-form-item :label="t('decoration.common.pageTitle')">
          <n-input
            :value="decorationStore.pageConfig.title"
            @update:value="(val) => (decorationStore.pageConfig.title = val.trim())"
            :placeholder="t('decoration.common.diyPageTitlePlaceholder')"
            clearable
            maxlength="16"
            show-count
          />
        </n-form-item>

        <!-- 表单布局 页面设置 -->
        <slot name="content"></slot>

        <!-- 导航栏 -->
        <n-form-item :label="t('decoration.common.styleSelect')" class="display-block">
          <n-radio-group
            :value="decorationStore.pageConfig.navBar.style"
            @update:value="updateNavBarStyle"
          >
            <n-radio :value="StyleEnum.STYLE_1">{{ t('decoration.common.styleOne') }}</n-radio>
            <n-radio :value="StyleEnum.STYLE_2">{{ t('decoration.common.styleTwo') }}</n-radio>
            <n-radio :value="StyleEnum.STYLE_3">{{ t('decoration.common.styleThree') }}</n-radio>
          </n-radio-group>
        </n-form-item>
        <!-- 风格1 -->
        <n-form-item
          :label="t('decoration.common.textAlign')"
          v-show="decorationStore.pageConfig.navBar.style === StyleEnum.STYLE_1"
        >
          <n-radio-group
            :value="decorationStore.pageConfig.navBar.textAlign"
            @update:value="(val) => (decorationStore.pageConfig.navBar.textAlign = val)"
          >
            <n-radio value="center">{{ t('decoration.common.textAlignCenter') }}</n-radio>
            <n-radio value="left">{{ t('decoration.common.textAlignLeft') }}</n-radio>
          </n-radio-group>
        </n-form-item>
        <!-- 风格2 -->
        <n-form-item
          :label="t('decoration.common.backgroundImage')"
          v-if="decorationStore.pageConfig.navBar.style === StyleEnum.STYLE_2"
        >
          <n-space>
            <attachment-upload
              v-model:value="decorationStore.pageConfig.navBar.imgUrl"
              :maxNumber="1"
            />
            <div class="text-xs text-gray-400">
              {{ t('decoration.common.pageSetBackgroundImageTips') }}
            </div>
          </n-space>
        </n-form-item>
        <!-- 风格3 -->
        <n-form-item
          :label="t('decoration.common.isShowTitle')"
          v-if="decorationStore.pageConfig.navBar.style === StyleEnum.STYLE_3"
        >
          <n-radio-group
            :value="decorationStore.pageConfig.navBar.isShow"
            @update:value="(val) => (decorationStore.pageConfig.navBar.isShow = val)"
          >
            <n-radio :value="1">{{ t('decoration.common.showText') }}</n-radio>
            <n-radio :value="0">{{ t('decoration.common.hideText') }}</n-radio>
          </n-radio-group>
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 背景设置 -->
      <edit-section :title="t('decoration.common.backgroundSet')">
        <n-form-item :label="t('decoration.common.backgroundColor')">
          <gradient-color-picker
            :startColor="decorationStore.pageConfig.background?.gradient?.startColor"
            :endColor="decorationStore.pageConfig.background?.gradient?.endColor"
            :angle="decorationStore.pageConfig.background?.gradient?.angle"
            show-angle-control
            @change="decorationStore.updateEditComponent('background.gradient', $event)"
          />
        </n-form-item>
        <n-form-item :label="t('decoration.common.backgroundSize')">
          <n-space vertical>
            <n-radio-group
              :value="decorationStore.pageConfig.bgSizeType"
              @update:value="
                (val) => {
                  decorationStore.pageConfig.bgSizeType = val
                  if (val !== BackgroundSizeEnum.CUSTOM) {
                    decorationStore.pageConfig.bgHeightScale = 100
                  }
                }
              "
            >
              <n-radio :value="BackgroundSizeEnum.COVER">
                {{ t('decoration.common.sizeCover') }}
              </n-radio>
              <n-radio :value="BackgroundSizeEnum.CONTAIN">
                {{ t('decoration.common.sizeContain') }}
              </n-radio>
              <n-radio :value="BackgroundSizeEnum.CUSTOM">
                {{ t('decoration.common.sizeCustom') }}
              </n-radio>
            </n-radio-group>

            <n-space v-if="decorationStore.pageConfig.bgSizeType === BackgroundSizeEnum.CUSTOM">
              <slider-input
                :value="decorationStore.pageConfig.bgHeightScale ?? 100"
                @update:value="(val) => (decorationStore.pageConfig.bgHeightScale = val)"
              />
              <div class="text-xs text-gray-400">
                {{ t('decoration.common.backgroundHeightScaleTip') }}
              </div>
            </n-space>
          </n-space>
        </n-form-item>

        <n-form-item :label="t('decoration.common.backgroundImage')">
          <n-space>
            <attachment-upload v-model:value="decorationStore.pageConfig.bgUrl" :maxNumber="1" />
            <div class="text-xs text-gray-400">
              {{ t('decoration.common.backgroundImageTip') }}
            </div>
          </n-space>
        </n-form-item>
      </edit-section>

      <!-- 边距设置 -->
      <edit-section :title="t('decoration.common.spacingSet')">
        <n-form-item :label="t('decoration.common.horizontalSpacing')">
          <n-space>
            <slider-input :value="horizontalPadding" @update:value="updateHorizontalSpacing" />
            <div class="text-xs text-gray-400">
              {{ t('decoration.common.horizontalSpacingTip') }}
            </div>
          </n-space>
        </n-form-item>
      </edit-section>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { NRadio, NRadioGroup, NForm, NFormItem, NInput } from 'naive-ui'
  import SliderInput from '@/components/SliderInput'
  import { EditSection, StyleEnum, BackgroundSizeEnum } from '@/components/Decoration'

  const { t } = useI18n('decoration')
  const decorationStore = useDecorationStore()

  const horizontalPadding = ref(decorationStore.pageConfig.padding.left)
  // 改变页面的左右边距时，更新所有组件的数值
  const updateHorizontalSpacing = (value: number) => {
    decorationStore.pageConfig.padding.left = value
    decorationStore.pageConfig.padding.right = value
    horizontalPadding.value = value
  }

  // 更新导航栏样式
  const updateNavBarStyle = (val: string) => {
    decorationStore.pageConfig.navBar.style = val
    // 当样式为样式2时，将textAlign设置为left
    if (val === StyleEnum.STYLE_2) {
      decorationStore.pageConfig.navBar.textAlign = 'left'
    }
  }
</script>

<style lang="scss" scoped></style>
