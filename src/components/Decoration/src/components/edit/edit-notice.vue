<template>
  <!-- 内容 -->
  <div class="content-wrap notice-content-wrap">
    <div v-show="decorationStore.editTab == 'content'">
      <div class="edit-attr-item-wrap">
        <edit-section :title="t('decoration.common.noticeStyle')">
          <n-form label-width="90px" label-placement="left" label-align="left">
            <n-form-item :label="t('decoration.common.noticeTypeTitle')">
              <n-radio-group
                :value="currentEditComponent.noticeType"
                name="radiogroup"
                @update:value="(val) => decorationStore.updateEditComponent('noticeType', val)"
              >
                <n-radio label="img" class="pr-[15px]" value="img">
                  {{ t('decoration.common.noticeTypeImg') }}
                </n-radio>
                <n-radio label="text" value="text">
                  {{ t('decoration.common.noticeTypeText') }}
                </n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              :label="t('decoration.common.uploadImage')"
              v-if="currentEditComponent.noticeType === 'img'"
            >
              <div class="flex items-center flex-col w-full">
                <AttachmentUpload
                  class="w-auto"
                  :value="currentEditComponent.imageUrl"
                  @update:value="(val) => decorationStore.updateEditComponent('imageUrl', val)"
                  :maxNumber="1"
                />
                <n-text class="text-[12px] flex-2 w-full text-gray-400">
                  {{ t('decoration.common.uploadImageTips') }}
                </n-text>
              </div>
            </n-form-item>

            <n-form-item
              :label="t('decoration.common.noticeTitle')"
              v-show="currentEditComponent.noticeType == 'text'"
            >
              <n-input
                :value="currentEditComponent.noticeTitle"
                @update:value="(val) => decorationStore.updateEditComponent('noticeTitle', val)"
                :placeholder="t('decoration.common.titlePlaceholder')"
                clearable
                maxlength="20"
                show-word-limit
              />
            </n-form-item>
          </n-form>
        </edit-section>
      </div>

      <div class="edit-attr-item-wrap">
        <n-form
          ref="titleFormRef"
          :model="currentEditComponent"
          label-placement="left"
          label-align="left"
          label-width="80px"
        >
          <n-form-item :label="t('decoration.common.noticeScrollWay')">
            <n-radio-group
              :value="currentEditComponent.scrollWay"
              @update:value="(val) => decorationStore.updateEditComponent('scrollWay', val)"
            >
              <n-radio label="horizontal" value="horizontal">
                {{ t('decoration.common.noticeHorizontal') }}
              </n-radio>
              <n-radio label="upDown" value="upDown" class="pr-[15px]">
                {{ t('decoration.common.noticeUpDown') }}
              </n-radio>
            </n-radio-group>
          </n-form-item>
          <edit-section :title="t('decoration.common.buttonSet')">
            <n-form-item :label="t('decoration.common.buttonPosition')">
              <n-radio-group
                :value="currentEditComponent.showType"
                @update:value="(val) => decorationStore.updateEditComponent('showType', val)"
              >
                <n-radio label="show" class="pr-[15px]" value="show">
                  {{ t('decoration.common.show') }}
                </n-radio>
                <n-radio label="hide" value="hide">{{ t('decoration.common.hide') }}</n-radio>
              </n-radio-group>
            </n-form-item>
          </edit-section>

          <edit-section :title="t('decoration.common.noticeText')">
            <div ref="noticeBoxRef">
              <link-selector v-model="currentEditComponent.list" mode="text" />
            </div>
          </edit-section>
        </n-form>
      </div>
    </div>
    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <n-form label-width="115" label-placement="left" label-align="left">
        <edit-section :title="t('decoration.common.textStyle')">
          <n-form-item :label="t('decoration.common.textColor')">
            <single-color-picker
              :value="currentEditComponent.textColor"
              :default-value="currentEditComponent.textColor"
              @update:value="(val) => decorationStore.updateEditComponent('textColor', val)"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.textFontSize')">
            <slider-input
              v-model:value="currentEditComponent.fontSize"
              :min="12"
              :max="16"
              :default-value="16"
              @update:value="decorationStore.updateEditComponent('fontSize', $event)"
            />
          </n-form-item>
          <n-form-item :label="t('decoration.common.textColor')">
            <n-radio-group
              :value="currentEditComponent.fontWeight"
              name="radiogroup"
              @update:value="(val) => decorationStore.updateEditComponent('fontWeight', val)"
            >
              <n-space>
                <n-radio v-for="song in fontList" :key="song.value" :value="song.value">
                  {{ song.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
        </edit-section>
      </n-form>
      <slot name="style"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { NText } from 'naive-ui'
  import { range } from 'lodash-es'
  import Sortable from 'sortablejs'
  import { FontOption } from '../../types/noticeComponent'
  import LinkSelector from '@/components/SelectLink/src/linkSelector.vue'
  import { NoticeComponent } from '../../types'
  import { LinkType } from '@/components/SelectLink'

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<NoticeComponent>())
  // 使用类型注解
  const fontList = ref<FontOption[]>([
    {
      label: '正常',
      value: 'normal',
    },
    {
      label: '倾斜',
      value: 'italic',
    },
    {
      label: '加粗',
      value: 'bold',
    },
  ])

  const { t } = useI18n()
  const titleFormRef = ref()
  const noticeBoxRef = ref()
  const decorationStore = useDecorationStore()

  onMounted(() => {
    // 初始化时设置ignore属性
    decorationStore.updateEditComponent('ignore', ['containerMargin', 'textColor', 'itemStyle'])
    // 添加默认公告数据
    decorationStore.updateEditComponent('style', 'list', (component: NoticeComponent) => {
      if (component.list.length === 0) {
        component.list.push({
          id: decorationStore.generateRandom(),
          text: '公告内容',
          link: { key: '', title: '', url: '', type: LinkType.DECORATED_PAGE },
        })
      }
    })
    nextTick(() => {
      const sortable = Sortable.create(noticeBoxRef.value, {
        group: 'item-wrap',
        animation: 200,
        onEnd: (event) => {
          const temp = currentEditComponent.value.list[event.oldIndex!]
          currentEditComponent.value.list.splice(event.oldIndex!, 1)
          currentEditComponent.value.list.splice(event.newIndex!, 0, temp)
          sortable.sort(
            range(currentEditComponent.value.list.length).map((value) => {
              return value.toString()
            }),
          )
        },
      })
    })
  })

  defineExpose({})
</script>

<style lang="scss">
  .notice-content-wrap {
    .add-notice-width {
      width: calc(100% - 20px);
    }

    .diy-upload-img {
      .image-wrap {
        width: 50px !important;
        height: 50px !important;
        margin-right: 0 !important;
        background: #fff;
      }

      .content-wrap {
        div {
          display: none;
        }
      }
    }
  }
</style>
