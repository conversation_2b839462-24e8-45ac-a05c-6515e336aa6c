<template>
  <n-form label-width="115" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <edit-section :title="t('decoration.common.displaySettings')">
        <!-- TODO:非一期内容 -->
        <!-- <n-form-item :label="t('decoration.common.selectStyle')">
          <n-radio-group
            :value="currentEditComponent.style"
            name="radiogroup"
            @update:value="(val) => handleStyleChange(val)"
          >
            <n-space>
              <n-radio
                v-for="option in styleOptions"
                :key="option.value"
                :value="option.value"
                :label="t(option.i18nKey)"
              />
            </n-space>
          </n-radio-group>
        </n-form-item> -->
      </edit-section>
      <edit-section :title="t('decoration.common.titleContent')">
        <n-form-item :label="t('decoration.common.titleText')">
          <n-input
            :value="currentEditComponent.titleText"
            @update:value="(val) => decorationStore.updateEditComponent('titleText', val)"
            :placeholder="t('decoration.common.titlePlaceholder')"
            clearable
            maxlength="15"
            show-count
          />
        </n-form-item>
        <template v-if="currentEditComponent.subTitle.control">
          <n-form-item :label="t('decoration.common.subTitle')">
            <n-input
              :value="currentEditComponent.subTitle.text"
              @update:value="(val) => decorationStore.updateEditComponent('subTitle.text', val)"
              :placeholder="t('decoration.common.subTitlePlaceholder')"
              clearable
              maxlength="30"
              show-count
            />
          </n-form-item>
        </template>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <edit-section :title="t('decoration.common.titleStyleSet')">
        <!-- 左右外边距 -->
        <n-form-item :label="t('decoration.common.containerHorizontalPadding')">
          <slider-input
            :value="currentEditComponent.subTitle.text"
            :min="0"
            :max="50"
            :default-value="0"
            @update:value="(val) => decorationStore.updateEditComponent('subTitle.text', val)"
          />
        </n-form-item>
      </edit-section>
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDecorationStore } from '@/store/modules/decoration'

  // import { getStyleI18nKey, getComponentStyleOptions } from '@/components/Decoration'

  import { OrderInfoComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<OrderInfoComponent>(),
  )

  /** 订单信息组件的风格选项 TODO:非一期内容 */
  // const styleOptions = getComponentStyleOptions('OrderInfo')

  /** 初始化时设置ignore属性 */
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /**
   * 处理样式变化 TODO:非一期内容
   * @param style 样式
   */
  // const handleStyleChange = (style: string) => {
  //   decorationStore.updateEditComponent('style', style, (component) => {
  //     // 更新样式名称，使用国际化键
  //     component.styleName = t(getStyleI18nKey(style))
  //   })
  // }

  defineExpose({})
</script>

<style lang="scss" scoped></style>
