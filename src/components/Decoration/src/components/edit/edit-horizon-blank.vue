<template>
  <n-form label-width="80" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 高度设置 -->
      <edit-section :title="t('decoration.common.titleHeight')">
        <!-- 组件高度 -->
        <n-form-item :label="t('decoration.common.blankHeight')">
          <slider-input
            v-model:value="currentEditComponent.blankHeight"
            :min="0"
            :max="50"
            :default-value="0"
            @update:value="(val) => decorationStore.updateEditComponent('blankHeight', val)"
          />
        </n-form-item>
      </edit-section>
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { computed } from 'vue'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { HorizontalBlankComponent } from '../../types'

  const { t } = useI18n()
  const decorationStore = useDecorationStore()

  /** 当前编辑组件 */
  const currentEditComponent = computed(() =>
    decorationStore.getEditComponent<HorizontalBlankComponent>(),
  )

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    position: relative;
    top: -20px;
    font-size: 12px;
    color: #999999;
  }
  .add-hot__btn {
    width: 100%;
  }
</style>
