<template>
  <n-form label-width="75" label-placement="left" label-align="left">
    <!-- 内容 -->
    <div v-show="decorationStore.editTab === 'content'">
      <!-- 内容设置 -->
      <edit-section :title="t('decoration.common.titleContentSet')">
        <n-form-item>
          <p class="section-tips">{{ t('decoration.common.titleContentTips') }}</p>
        </n-form-item>
      </edit-section>
      <!-- 热区预览 -->
      <edit-section :title="t('decoration.common.hotPreview')">
        <n-form-item>
          <div
            v-if="currentEditComponent.imageUrl"
            class="item-wrap content-box relative bg-gray-100 border border-dashed border-gray-500 bg-no-repeat"
            :style="{
              backgroundImage: `url(${currentEditComponent.imageUrl})`,
              width: contentBoxWidth + 'px',
              height: contentBoxHeight + 'px',
            }"
          >
            <div
              v-for="(item, index) in currentEditComponent.heatMapData"
              :id="'box_' + index"
              :key="index"
              class="area-box border border-solid border-[#ccc] absolute select-none p-[5px]"
              :style="{
                left: item.left + item.unit,
                top: item.top + item.unit,
                width: item.width + item.unit,
                height: item.height + item.unit,
              }"
            >
              <span>{{ index + 1 }}</span>
              <template v-if="item.link.title">
                <span class="p-[4px]">|</span>
                <span>{{ item.link.title }}</span>
              </template>
            </div>
            <div class="remove-img">
              <n-icon
                size="20"
                color="#ffffff"
                class="cursor-pointer"
                :component="CloseOutline"
                @click="() => removeImage()"
              />
            </div>
          </div>
          <n-space vertical v-else>
            <AttachmentUpload
              v-model:value="currentEditComponent.imageUrl"
              :maxNumber="1"
              @update:value="changeImageUrl"
            />
          </n-space>
        </n-form-item>
        <!-- 添加/编辑热区 -->
        <n-form-item :label="t('decoration.common.hotSet')">
          <n-space class="flex items-center">
            <div v-if="currentEditComponent.heatMapData.length">
              {{
                `${t('decoration.common.toSetHot')} ${currentEditComponent.heatMapData.length} ${t('decoration.common.toSetHotNum')}`
              }}
            </div>
            <n-button text type="primary" @click="addHotArea">
              {{
                currentEditComponent.heatMapData.length
                  ? t('decoration.common.editHotArea')
                  : t('decoration.common.addHotArea')
              }}
            </n-button>
          </n-space>
        </n-form-item>
      </edit-section>
      <!-- 热区设置 -->
      <EditHotModel
        ref="hotAreaModalRef"
        :modelValue="currentEditComponent"
        @model-value="changeModelValue"
      />
    </div>

    <!-- 样式 -->
    <div class="style-wrap" v-show="decorationStore.editTab === 'style'">
      <!-- 组件样式 -->
      <slot name="style"></slot>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { computed } from 'vue'
  import { useDecorationStore } from '@/store/modules/decoration'
  import { ref, nextTick } from 'vue'
  import { EditHotModel } from '@/components/Decoration/src/components/EditHotModel'
  import { CloseOutline } from '@vicons/ionicons5'
  import { img } from '@/utils/common'
  import { useMessage } from 'naive-ui'
  import { HotAreaComponent } from '../../types'

  const { t } = useI18n()
  const message = useMessage()
  const hotAreaModalRef = ref()
  const decorationStore = useDecorationStore()
  const contentBoxWidth = ref(360)
  const contentBoxHeight = ref(400)
  const imgRatio = ref(1) // 图片比例

  /** 当前编辑组件 */
  const currentEditComponent = computed(() => decorationStore.getEditComponent<HotAreaComponent>())

  // 初始化时设置ignore属性
  decorationStore.updateEditComponent('ignore', ['itemStyle', 'containerMargin', 'textColor'])

  /* 添加热区弹窗 */
  const addHotArea = () => {
    if (!currentEditComponent.value.imageUrl) {
      message.warning(t('decoration.common.uploadImgTips'))
      return
    }
    hotAreaModalRef.value?.openModal()
    nextTick(() => {
      hotAreaModalRef.value?.setFieldsValue()
    })
  }

  /* 上传图片 */
  const changeImageUrl = (value: string) => {
    decorationStore.updateEditComponent('imageUrl', value)
    const image = new Image()
    image.src = img(currentEditComponent.value.imageUrl)
    image.onload = async () => {
      decorationStore.updateEditComponent('imgWidth', image.width)
      decorationStore.updateEditComponent('imgHeight', image.height)
    }
    setImageSize(image.width, image.height)
  }

  /* 删除图片 */
  const removeImage = () => {
    decorationStore.updateEditComponent('imageUrl', '')
  }

  /* 设置图片宽高 */
  const setImageSize = (imgWidth, imgHeight) => {
    // 计算图片比例
    imgRatio.value = imgHeight / imgWidth

    // 根据图片比例，调整图片高度
    contentBoxHeight.value = Math.floor(contentBoxWidth.value * imgRatio.value)
  }

  /* 获取热区值 */
  const changeModelValue = (value) => {
    decorationStore.updateEditComponent('heatMapData', value)
  }

  defineExpose({})
</script>

<style lang="scss" scoped>
  .section-tips {
    font-size: 14px;
    color: #999999;
  }
  .item-wrap {
    width: 360px;
    background-size: 100%;
  }
  .remove-img {
    position: absolute;
    top: -10px;
    right: -10px;
    padding: 1px 2px;
    background-color: #999999;
    border-radius: 50%;
  }
  .area-box {
    background-color: rgba(255, 255, 255, 0.7);
  }
  :deep(.upload-card-item) {
    width: 360px !important;
    height: 200px !important;
  }
</style>
