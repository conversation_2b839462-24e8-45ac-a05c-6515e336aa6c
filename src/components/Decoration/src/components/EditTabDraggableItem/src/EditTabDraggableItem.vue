<template>
  <div class="edit-draggable">
    <Draggable animation="300" :list="tabList" group="tabDrag" itemKey="index">
      <template #item="{ index }">
        <div class="tab-item flex items-center">
          <n-icon size="20" :component="ReorderFour" class="cursor-move mr-4" />
          <div>
            <!-- 图片 -->
            <n-form-item :label="t('decoration.common.modeImg')" v-if="tabStyle === 2">
              <n-space vertical>
                <AttachmentUpload v-model:value="tabList[index].icon" :maxNumber="1" />
              </n-space>
            </n-form-item>
            <!-- 标题 -->
            <n-form-item :label="t('decoration.common.tabTitle')">
              <n-input
                v-model:value="tabList[index].title"
                :placeholder="t('decoration.common.placeholderCarContent')"
                @update:value="() => changeTabList()"
                clearable
                maxlength="4"
                show-count
              />
            </n-form-item>
            <!-- 副标题 -->
            <n-form-item :label="t('decoration.common.tabSubTitle')" v-if="tabStyle === 1">
              <n-input
                v-model:value="tabList[index].subTitle"
                :placeholder="t('decoration.common.placeholderCarContent')"
                @update:value="() => changeTabList()"
                clearable
                maxlength="4"
                show-count
              />
            </n-form-item>
            <!-- 选择方式 -->
            <n-form-item :label="t('decoration.common.selectType')">
              <n-radio-group v-model:value="tabList[index].checkType" name="typegroup">
                <n-space>
                  <n-radio :value="0" :label="t('decoration.common.isGoods')" />
                  <n-radio class="ml-7" :value="1" :label="t('decoration.common.isType')" />
                </n-space>
              </n-radio-group>
            </n-form-item>
            <!-- 选择商品 -->
            <n-form-item
              :label="t('decoration.common.checkGoods')"
              v-if="tabList[index].checkType === 0"
            >
              <n-space class="flex items-center">
                <SelectLink
                  v-model:ids="tabList[index].goodIds"
                  :only="['goods']"
                  :only-second="[GOODS_MENU.SHOP_GOODS_SELECT]"
                  goods-multiple
                  @confirm="(e) => checkGoodsList(e, index)"
                >
                  <n-button type="primary">+{{ t('decoration.common.isGoods') }}</n-button>
                </SelectLink>
                <span v-if="tabList[index].goodIds.length">
                  <span>{{ `${t('decoration.common.checkTabGoods')}` }}</span>
                  <span>{{ tabList[index].goodIds.length }}</span>
                  <span>{{ `${t('decoration.common.piece')}` }}</span>
                </span>
              </n-space>
            </n-form-item>
            <!-- 选择分类 -->
            <n-form-item
              :label="t('decoration.common.checkType')"
              v-if="tabList[index].checkType === 1"
            >
              <n-cascader
                v-model:value="tabList[index].cateIds"
                multiple
                clearable
                :options="typeOptions"
                :placeholder="t('decoration.common.placeholderCarGoodsType')"
                :cascade="false"
                :check-strategy="'all'"
                :show-path="true"
                value-field="id"
                label-field="name"
                @update:value="() => changeTabList()"
              />
            </n-form-item>
            <!-- 商品数量 -->
            <n-form-item
              :label="t('decoration.common.goodsNum')"
              v-if="tabList[index].checkType === 1"
            >
              <slider-input
                v-model:value="tabList[index].goodsNum"
                :min="0"
                :max="50"
                :default-value="1"
                @update:value="() => changeTabList()"
              />
            </n-form-item>
            <!-- 商品排序 -->
            <n-form-item
              :label="t('decoration.common.goodsSort')"
              v-if="tabList[index].checkType === 1"
            >
              <n-radio-group
                v-model:value="tabList[index].goodsSort"
                name="sortgroup"
                @update:value="() => changeTabList()"
              >
                <n-space>
                  <n-radio value="sortAll" :label="t('decoration.common.sortAll')" />
                  <n-radio value="sales" :label="t('decoration.common.sortVolume')" />
                  <n-radio value="price" :label="t('decoration.common.sortPrice')" />
                </n-space>
              </n-radio-group>
            </n-form-item>
          </div>
          <n-icon
            size="20"
            color="#666666"
            class="delete-tab"
            :component="CloseCircleOutline"
            @click="deleteTabList(index)"
          />
        </div>
      </template>
    </Draggable>
    <div class="mt-3">
      <slot name="addBtn"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useI18n } from '@/hooks/web/useI18n'
  import { onMounted, ref, computed } from 'vue'
  import { ReorderFour, CloseCircleOutline } from '@vicons/ionicons5'
  import Draggable from 'vuedraggable'
  import { getGoodsType } from '@/api/mall/goods/goods'
  import { typeOptions } from '@/api/mall/goods/goods/type'
  import { GOODS_MENU } from '@/components/SelectLink/src/config/constants'

  const { t } = useI18n()

  const props = defineProps({
    tabStyle: {
      type: Number,
      default: 1,
    },
    list: {
      type: Array as any,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '',
    },
    maxlength: {
      type: Number,
      default: 15,
    },
    showCount: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['change-tab'])

  const typeOptions = ref<typeOptions[]>([])
  const nowIndex = ref(0)
  const tabList = computed(() => {
    return props.list
  })

  const checkGoodsList = (linkValue, index) => {
    nowIndex.value = index
    tabList.value[nowIndex.value].goodIds = linkValue?.goodsIds || []
    console.log(index, tabList.value[nowIndex.value].goodIds)
    changeTabList()
  }

  /* 删除选项卡 */
  const deleteTabList = (index) => {
    tabList.value.splice(index, 1)
    changeTabList()
  }

  const changeTabList = () => {
    emit('change-tab', tabList.value)
  }

  /**
   * 获取商品分类
   */
  const getGoodsTypeList = async () => {
    const result = await getGoodsType()
    typeOptions.value = result
  }

  onMounted(() => {
    getGoodsTypeList()
  })
</script>

<style lang="scss" scoped>
  .edit-draggable {
    width: 100%;
  }
  .tab-item {
    position: relative;
    padding: 20px 10px 0 10px;
    margin-bottom: 20px;
    border: 1px dashed #eeeeee;
  }
  .delete-tab {
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
  }
  .check-goods {
    width: 100%;
    padding: 5px 10px;
    color: #999999;
    cursor: pointer;
    border: 1px solid #eeeeee;
    border-radius: 3px;
  }
</style>
