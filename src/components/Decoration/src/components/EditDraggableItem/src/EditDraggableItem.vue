<template>
  <div class="edit-draggable">
    <Draggable animation="300" :list="draggableList" group="specifications" itemKey="index">
      <template #item="{ index }">
        <n-space class="flex items-center mt-2">
          <n-icon size="20" :component="ReorderFour" class="cursor-move" />
          <n-input
            v-model:value="draggableList[index]"
            :placeholder="placeholder"
            clearable
            :maxlength="maxlength"
            :show-count="showCount"
            style="width: 290px"
          />
          <n-icon
            size="20"
            color="#666666"
            class="cursor-pointer"
            :component="CloseCircleOutline"
            @click="deleteHotItem(index)"
          />
        </n-space>
      </template>
    </Draggable>
    <div class="mt-3">
      <slot name="addBtn"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, PropType } from 'vue'
  import { ReorderFour, CloseCircleOutline } from '@vicons/ionicons5'
  import Draggable from 'vuedraggable'

  const props = defineProps({
    list: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '',
    },
    maxlength: {
      type: Number,
      default: 15,
    },
    showCount: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['delete-item'])

  const draggableList = ref<string[]>(props.list)

  /* 删除热词 */
  const deleteHotItem = (index: number) => {
    draggableList.value.splice(index, 1)
    emit('delete-item', draggableList.value)
  }
</script>

<style lang="scss" scoped>
  .edit-draggable {
    width: 100%;
  }
</style>
