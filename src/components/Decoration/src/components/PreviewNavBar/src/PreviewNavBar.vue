<template>
  <div
    class="preview-head"
    :class="[navBar.style]"
    :style="{ backgroundColor: navBar.bgColor }"
    @click="emit('click')"
  >
    <!-- 顶部状态栏 - 风格1 -->
    <div v-if="navBar.style === StyleEnum.STYLE_1 && navBar.isShow" class="content-wrap">
      <div
        class="title-wrap !text-[14px]"
        :style="{
          color: navBar.textColor,
          textAlign: navBar.textAlign,
        }"
      >
        {{ title }}
      </div>
    </div>

    <!-- 顶部状态栏 - 风格2 -->
    <div v-if="navBar.style === StyleEnum.STYLE_2 && navBar.isShow" class="content-wrap">
      <div class="title-wrap" :style="{ color: navBar.textColor }">
        <div class="h-[28px] max-w-[150px] mr-[8px]" v-if="navBar.imgUrl">
          <img class="max-w-[100%] max-h-[100%]" :src="navBar.imgUrl" mode="heightFix" />
        </div>
        <div :style="{ color: navBar.textColor }">
          {{ title }}
        </div>
      </div>
    </div>

    <!-- 顶部状态栏 - 风格3 -->
    <div v-if="navBar.style === StyleEnum.STYLE_3 && navBar.isShow" class="content-wrap">
      <span class="iconfont icon-dizhi !text-[14px]" :style="{ color: navBar.textColor }"></span>
      <div class="title-wrap" :style="{ color: navBar.textColor }">
        {{ t('decoration.common.myLocation') }}
      </div>
      <span
        class="iconfont icon-jiantou-you !text-[12px]"
        :style="{ color: navBar.textColor }"
      ></span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { StyleEnum, NavBar } from '@/components/Decoration'
  import { useI18n } from '@/hooks/web/useI18n'

  defineProps<{
    navBar: NavBar
    title: string
  }>()

  const emit = defineEmits(['click'])
  const { t } = useI18n()
</script>

<style lang="scss" scoped>
  @use '@/components/Decoration/src/css/index' as *;

  .preview-head {
    position: relative;
    z-index: 10;
    height: $preview-header-height;
    padding: calc($preview-header-height - $nav-title-height) 12px 0;
    cursor: pointer;
    background-color: var(--primary-color);
    background-image: url(@/assets/images/decoration/head/diy_preview_head.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;

    .content-wrap {
      display: flex;
      justify-content: center;
      height: $nav-title-height;
      line-height: $nav-title-height;
      text-align: center;
    }

    &.style-1 {
      .content-wrap {
        .title-wrap {
          width: 100%;
          height: $nav-title-height;
          overflow: hidden;
          font-weight: 500;
          line-height: $nav-title-height;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    &.style-2 {
      .content-wrap {
        justify-content: flex-start;

        .title-wrap {
          display: flex;
          align-items: center;

          > div {
            max-width: 150px;
            height: $nav-title-height;
            font-size: 14px;
            font-weight: 500;
            line-height: $nav-title-height;

            &:last-child {
              flex: 1;
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }

    &.style-3 {
      .content-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .iconfont {
          display: inline-block;
          transition: all 0.3s;
        }

        .title-wrap {
          flex: none;
          max-width: 180px;
          margin: 0 5px;
          overflow: hidden;
          font-size: 14px;
          font-weight: 500;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
</style>
