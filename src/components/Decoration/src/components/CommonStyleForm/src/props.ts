import type { PropType } from 'vue'
import type { CommonStyle, ignoreField } from '../../../types'
import { predefineColors, defaultCommonStyle } from '../../../config/defaultConfig'

export const basicProps = {
  // 组件数据
  data: {
    type: Object as PropType<CommonStyle>,
    required: true,
    default: () => ({ ...defaultCommonStyle }),
  },
  // 默认样式
  defaultStyle: {
    type: Object as PropType<CommonStyle>,
    default: () => ({ ...defaultCommonStyle }),
  },
  // 忽略的设置项
  ignore: {
    type: Array as () => ignoreField[],
    default: () => [],
  },
  // 外边距设置模式：'grouped'(上下和左右分组) 或 'separate'(上下左右分开)
  containerMarginMode: {
    type: String,
    default: 'grouped',
    validator: (value: string) => ['grouped', 'separate'].includes(value),
  },
  // 内边距设置模式：'grouped'(上下和左右分组) 或 'separate'(上下左右分开)
  containerPaddingMode: {
    type: String,
    default: 'grouped',
    validator: (value: string) => ['grouped', 'separate'].includes(value),
  },
  // 圆角设置模式：'grouped'(上下分组) 或 'separate'(四个角分开)
  radiusMode: {
    type: String,
    default: 'grouped',
    validator: (value: string) => ['grouped', 'separate'].includes(value),
  },
  // 子项圆角设置模式：'grouped'(上下分组) 或 'separate'(四个角分开)
  radiusModeForItem: {
    type: String,
    default: 'grouped',
    validator: (value: string) => ['grouped', 'separate'].includes(value),
  },
  // 是否启用背景图片设置
  enableBackgroundImage: {
    type: Boolean,
    default: false,
  },
  // 颜色选择器色板
  colorSwatches: {
    type: Array as () => string[],
    default: () => predefineColors,
  },
}
