<template>
  <n-form class="common-style-setting" label-placement="left" label-align="left" label-width="80">
    <!-- 文字颜色 -->
    <n-form-item v-if="!isIgnored('textColor')" :label="t('decoration.common.textColor')">
      <single-color-picker
        v-model:value="textColor"
        :defaultValue="'#303133'"
        :placeholder="'#FFFFFF'"
        :swatches="colorSwatches"
        @update:value="updateField('textColor', $event)"
      />
    </n-form-item>

    <!-- 容器背景渐变 -->
    <n-form-item
      v-if="!isIgnored('containerGradient')"
      :label="t('decoration.common.containerGradient')"
    >
      <gradient-color-picker
        v-model:startColor="gradientStartColor"
        v-model:endColor="gradientEndColor"
        v-model:angle="gradientAngle"
        :defaultStartColor="'#f5f5f5'"
        :defaultEndColor="'#f5f5f5'"
        @change="updateField('containerGradient', $event)"
      />
    </n-form-item>

    <!-- 组件背景渐变 -->
    <n-form-item v-if="!isIgnored('background')" :label="t('decoration.common.backgroundGradient')">
      <gradient-color-picker
        v-model:startColor="backgroundGradientStartColor"
        v-model:endColor="backgroundGradientEndColor"
        v-model:angle="backgroundGradientAngle"
        :defaultStartColor="'#ffffff'"
        :defaultEndColor="'#ffffff'"
        show-angle-control
        :swatches="colorSwatches"
        @change="updateField('background.gradient', $event)"
      />
    </n-form-item>

    <!-- 背景图片 -->
    <n-form-item
      v-if="!isIgnored('background') && enableBackgroundImage"
      :label="t('decoration.common.backgroundImage')"
    >
      <n-input
        v-model:value="backgroundImageUrl"
        :placeholder="t('decoration.common.backgroundImagePlaceholder')"
        @update:value="updateField('background.imageUrl', $event)"
      />

      <!-- 图片透明度设置 -->
      <div class="opacity-slider" v-if="backgroundImageUrl">
        <span class="opacity-label">{{ t('decoration.common.opacity') }}</span>
        <slider-input
          v-model:value="displayBackgroundOpacity"
          :min="0"
          :max="100"
          :default-value="100"
          @update:value="updateBackgroundOpacity"
        />
      </div>
    </n-form-item>

    <!-- 容器外边距设置 -->
    <template v-if="!isIgnored('containerMargin')">
      <!-- 上外边距 -->
      <n-form-item :label="t('decoration.common.containerTopMargin')">
        <slider-input
          v-model:value="topMargin"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerMargin.top', $event)"
        />
      </n-form-item>
      <!-- 右外边距 -->
      <n-form-item
        v-if="!isGroupedContainerMarginMode"
        :label="t('decoration.common.containerRightMargin')"
      >
        <slider-input
          v-model:value="rightMargin"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerMargin.right', $event)"
        />
      </n-form-item>
      <!-- 下外边距 -->
      <n-form-item :label="t('decoration.common.containerBottomMargin')">
        <slider-input
          v-model:value="bottomMargin"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerMargin.bottom', $event)"
        />
      </n-form-item>
      <!-- 左外边距 -->
      <n-form-item
        v-if="!isGroupedContainerMarginMode"
        :label="t('decoration.common.containerLeftMargin')"
      >
        <slider-input
          v-model:value="leftMargin"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerMargin.left', $event)"
        />
      </n-form-item>
      <!-- 左右外边距 -->
      <n-form-item
        v-if="isGroupedContainerMarginMode"
        :label="t('decoration.common.containerHorizontalMargin')"
      >
        <slider-input
          v-model:value="displayHorizontalMargin"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateHorizontalMargin"
        />
      </n-form-item>
    </template>

    <!-- 容器内边距设置 -->
    <template v-if="!isIgnored('containerPadding')">
      <!-- 上内边距 -->
      <n-form-item :label="t('decoration.common.containerTopPadding')">
        <slider-input
          v-model:value="topPadding"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerPadding.top', $event)"
        />
      </n-form-item>
      <!-- 右内边距 -->
      <n-form-item
        v-if="!isGroupedContainerPaddingMode"
        :label="t('decoration.common.containerRightPadding')"
      >
        <slider-input
          v-model:value="rightPadding"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerPadding.right', $event)"
        />
      </n-form-item>
      <!-- 下内边距 -->
      <n-form-item :label="t('decoration.common.containerBottomPadding')">
        <slider-input
          v-model:value="bottomPadding"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerPadding.bottom', $event)"
        />
      </n-form-item>
      <!-- 左内边距 -->
      <n-form-item
        v-if="!isGroupedContainerPaddingMode"
        :label="t('decoration.common.containerLeftPadding')"
      >
        <slider-input
          v-model:value="leftPadding"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateField('containerPadding.left', $event)"
        />
      </n-form-item>
      <!-- 左右内边距 -->
      <n-form-item
        v-if="isGroupedContainerPaddingMode"
        :label="t('decoration.common.containerHorizontalPadding')"
      >
        <slider-input
          v-model:value="displayHorizontalPadding"
          :min="0"
          :max="50"
          :default-value="0"
          @update:value="updateHorizontalPadding"
        />
      </n-form-item>
    </template>

    <!-- 圆角设置 -->
    <template v-if="!isIgnored('borderRadius')">
      <!-- 圆角设置（上左、上右、下左、下右） -->
      <n-form-item v-if="!isGroupedRadiusMode" :label="t('decoration.common.borderRadius')">
        <div class="radius-inputs">
          <div class="radius-input" v-for="corner in corners" :key="corner">
            <span class="radius-label">{{ t(`decoration.common.${corner}`) }}</span>
            <slider-input
              :value="cornerRadiusValues[corner]"
              :min="0"
              :max="30"
              :default-value="0"
              @update:value="updateCornerRadius(corner, $event)"
            />
          </div>
        </div>
      </n-form-item>
      <template v-else>
        <!-- 上圆角（上左、上右） -->
        <n-form-item :label="t('decoration.common.topRadius')">
          <slider-input
            v-model:value="displayTopRadius"
            :min="0"
            :max="30"
            :default-value="0"
            @update:value="updateTopRadius"
          />
        </n-form-item>
        <!-- 下圆角（下左、下右） -->
        <n-form-item :label="t('decoration.common.bottomRadius')">
          <slider-input
            v-model:value="displayBottomRadius"
            :min="0"
            :max="30"
            :default-value="0"
            @update:value="updateBottomRadius"
          />
        </n-form-item>
      </template>
    </template>

    <!-- 子项样式 -->
    <n-form-item v-if="!isIgnored('itemStyle')" :label="t('decoration.common.itemStyle')">
      <single-color-picker
        v-model:value="itemBackgroundColor"
        :defaultValue="'#ffffff'"
        :placeholder="'#FFFFFF'"
        :swatches="colorSwatches"
        @update:value="updateField('itemStyle.backgroundColor', $event)"
      />
    </n-form-item>
  </n-form>
</template>

<script lang="ts" setup>
  import { reactive, watch, computed } from 'vue'
  import { NForm, NFormItem, NInput } from 'naive-ui'
  import { GradientColorPicker, SingleColorPicker } from '@/components/ColorPicker'
  import SliderInput from '@/components/SliderInput'
  import { useI18n } from '@/hooks/web/useI18n'
  import { cloneDeep, debounce } from 'lodash-es'
  import { basicProps } from './props'
  import type { CommonStyle, CornerRadii, ignoreField } from '../../../types'

  const { t } = useI18n('decoration')

  // 定义组件属性
  const props = defineProps(basicProps)

  // 定义事件
  const emit = defineEmits(['update:data', 'change'])

  const corners = ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']

  // 计算属性：是否使用分组外边距模式
  const isGroupedContainerMarginMode = computed(() => props.containerMarginMode === 'grouped')

  // 计算属性：是否使用分组内边距模式
  const isGroupedContainerPaddingMode = computed(() => props.containerPaddingMode === 'grouped')

  // 计算属性：是否使用分组圆角模式
  const isGroupedRadiusMode = computed(() => props.radiusMode === 'grouped')

  // 获取是否启用背景图片
  const enableBackgroundImage = computed(() => props.enableBackgroundImage)

  // 创建包含完整结构的本地数据副本，避免类型错误
  const localData = reactive<CommonStyle>({
    ...cloneDeep(props.defaultStyle),
    ...cloneDeep(props.data),
  })

  // 监听数据变化
  watch(
    () => props.data,
    (newVal) => {
      // 合并新值到本地数据
      Object.assign(localData, cloneDeep(newVal))
    },
    { deep: true, immediate: true },
  )

  // 安全的文本颜色
  const textColor = computed({
    get: () => localData.textColor || '',
    set: (value) => updateField('textColor', value),
  })

  // 安全的渐变起始颜色
  const gradientStartColor = computed({
    get: () => localData.containerGradient?.startColor || '',
    set: (value) => updateField('containerGradient.startColor', value),
  })

  // 安全的渐变结束颜色
  const gradientEndColor = computed({
    get: () => localData.containerGradient?.endColor || '',
    set: (value) => updateField('containerGradient.endColor', value),
  })

  // 安全的渐变角度
  const gradientAngle = computed({
    get: () => localData.containerGradient?.angle || 0,
    set: (value) => updateField('containerGradient.angle', value),
  })

  // 安全的背景渐变起始颜色
  const backgroundGradientStartColor = computed({
    get: () => localData.background?.gradient?.startColor || '',
    set: (value) => updateField('background.gradient.startColor', value),
  })

  // 安全的背景渐变结束颜色
  const backgroundGradientEndColor = computed({
    get: () => localData.background?.gradient?.endColor || '',
    set: (value) => updateField('background.gradient.endColor', value),
  })

  // 安全的背景渐变角度
  const backgroundGradientAngle = computed({
    get: () => localData.background?.gradient?.angle || 0,
    set: (value) => updateField('background.gradient.angle', value),
  })

  // 安全的背景图片URL
  const backgroundImageUrl = computed({
    get: () => localData.background?.imageUrl || '',
    set: (value) => updateField('background.imageUrl', value),
  })

  // 安全的背景透明度
  const displayBackgroundOpacity = computed({
    get: () => (localData.background?.opacity ?? 1) * 100,
    set: (value) => updateBackgroundOpacity(value),
  })

  // 安全的顶部外边距
  const topMargin = computed({
    get: () => localData.containerMargin?.top ?? 0,
    set: (value) => updateField('containerMargin.top', value),
  })

  // 安全的右侧外边距
  const rightMargin = computed({
    get: () => localData.containerMargin?.right ?? 0,
    set: (value) => updateField('containerMargin.right', value),
  })

  // 安全的底部外边距
  const bottomMargin = computed({
    get: () => localData.containerMargin?.bottom ?? 0,
    set: (value) => updateField('containerMargin.bottom', value),
  })

  // 安全的左侧外边距
  const leftMargin = computed({
    get: () => localData.containerMargin?.left ?? 0,
    set: (value) => updateField('containerMargin.left', value),
  })

  // 安全的水平外边距
  const displayHorizontalMargin = computed({
    get: () => localData.containerMargin?.left ?? 0,
    set: (value) => updateHorizontalMargin(value),
  })

  // 安全的顶部内边距
  const topPadding = computed({
    get: () => localData.containerPadding?.top ?? 0,
    set: (value) => updateField('containerPadding.top', value),
  })

  // 安全的右侧内边距
  const rightPadding = computed({
    get: () => localData.containerPadding?.right ?? 0,
    set: (value) => updateField('containerPadding.right', value),
  })

  // 安全的底部内边距
  const bottomPadding = computed({
    get: () => localData.containerPadding?.bottom ?? 0,
    set: (value) => updateField('containerPadding.bottom', value),
  })

  // 安全的左侧内边距
  const leftPadding = computed({
    get: () => localData.containerPadding?.left ?? 0,
    set: (value) => updateField('containerPadding.left', value),
  })

  // 安全的水平内边距
  const displayHorizontalPadding = computed({
    get: () => localData.containerPadding?.left ?? 0,
    set: (value) => updateHorizontalPadding(value),
  })

  // 用于显示的上圆角
  const displayTopRadius = computed({
    get: () => localData.borderRadius?.topLeft ?? 0,
    set: (value) => updateTopRadius(value),
  })

  // 用于显示的下圆角
  const displayBottomRadius = computed({
    get: () => localData.borderRadius?.bottomLeft ?? 0,
    set: (value) => updateBottomRadius(value),
  })

  // 安全的子项背景色
  const itemBackgroundColor = computed({
    get: () => localData.itemStyle?.backgroundColor || '',
    set: (value) => updateField('itemStyle.backgroundColor', value),
  })

  // 圆角值的计算属性映射
  const cornerRadiusValues = computed(() => {
    const result: Record<string, number> = {}
    for (const corner of corners) {
      result[corner] = localData.borderRadius?.[corner as keyof CornerRadii] ?? 0
    }
    return result
  })

  // 更新特定圆角
  const updateCornerRadius = (corner: string, value: number) => {
    updateField(`borderRadius.${corner}`, value)
  }

  // 更新背景透明度
  const updateBackgroundOpacity = (value: number) => {
    if (localData.background) {
      localData.background.opacity = value / 100
      emitChange()
    }
  }

  // 更新水平外边距
  const updateHorizontalMargin = (value: number) => {
    if (localData.containerMargin) {
      localData.containerMargin.left = value
      localData.containerMargin.right = value
      emitChange()
    }
  }

  // 更新水平内边距
  const updateHorizontalPadding = (value: number) => {
    if (localData.containerPadding) {
      localData.containerPadding.left = value
      localData.containerPadding.right = value
      emitChange()
    }
  }

  // 更新上圆角
  const updateTopRadius = (value: number) => {
    if (localData.borderRadius) {
      localData.borderRadius.topLeft = value
      localData.borderRadius.topRight = value
      emitChange()
    }
  }

  // 更新下圆角
  const updateBottomRadius = (value: number) => {
    if (localData.borderRadius) {
      localData.borderRadius.bottomLeft = value
      localData.borderRadius.bottomRight = value
      emitChange()
    }
  }

  // 检查是否忽略某个设置项
  const isIgnored = (field: ignoreField): boolean => {
    return props.ignore.includes(field)
  }

  // 发送变更事件（节流）
  const emitChange = debounce(() => {
    emit('update:data', cloneDeep(localData))
    emit('change', cloneDeep(localData))
  }, 100)

  // 通用更新方法
  const updateField = (fieldPath: string, value: any) => {
    const fields = fieldPath.split('.')
    let target = localData as any

    // 检查路径是否存在，如果不存在就创建
    for (let i = 0; i < fields.length - 1; i++) {
      if (target[fields[i]] === undefined) {
        target[fields[i]] = {}
      }
      target = target[fields[i]]
    }

    target[fields[fields.length - 1]] = value
    emitChange()
  }
</script>

<style lang="scss" scoped>
  .common-style-setting {
    padding: 8px 0;

    .opacity-slider {
      display: flex;
      align-items: center;
      margin-top: 8px;

      .opacity-label {
        width: 60px;
        font-size: 14px;
        color: #606266;
      }
    }

    .radius-inputs {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .radius-input {
        display: flex;
        align-items: center;

        .radius-label {
          width: 60px;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
</style>
