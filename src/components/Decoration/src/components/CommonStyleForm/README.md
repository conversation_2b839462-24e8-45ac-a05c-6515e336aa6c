# CommonStyleSetting 通用样式设置组件

这个组件用于设置通用样式，包括文字颜色、背景渐变、边距、圆角和子项样式等。

## 使用方法

```vue
<template>
  <common-style-setting
    v-model:component-data="componentData"
    :ignore="['itemStyle']"
    margin-mode="grouped"
    radius-mode="grouped"
    @change="handleStyleChange"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { CommonStyleSetting } from '@/components'

// 组件数据
const componentData = ref({
  textColor: '#303133',
  containerGradient: {
    startColor: '#f5f5f5',
    endColor: '#f5f5f5',
    alpha: 1,
    angle: '90deg',
  },
  background: {
    imageUrl: '',
    opacity: 100,
    gradient: {
      startColor: '#ffffff',
      endColor: '#ffffff',
      alpha: 1,
      angle: '90deg',
    },
  },
  borderRadius: {
    topLeft: 0,
    topRight: 0,
    bottomLeft: 0,
    bottomRight: 0,
  },
  itemStyle: {
    backgroundColor: '#ffffff',
    borderRadius: {
      topLeft: 0,
      topRight: 0,
      bottomLeft: 0,
      bottomRight: 0,
    },
  },
  margin: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
})

// 处理样式变更
const handleStyleChange = (data) => {
  console.log('样式已更新:', data)
}
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| componentData | Object | - | 组件数据，包含所有样式设置 |
| ignore | Array | [] | 忽略的设置项，可选值见下方"可忽略的设置项" |
| marginMode | String | 'grouped' | 边距设置模式，可选值：'grouped'(上下和左右分组)、'separate'(上下左右分开) |
| radiusMode | String | 'grouped' | 圆角设置模式，可选值：'grouped'(上下分组)、'separate'(四个角分开) |
| enableBackgroundImage | Boolean | false | 是否启用背景图片设置，设为true时才会显示背景图片和透明度设置项 |
| colorSwatches | Array | [...] | 颜色选择器色板 |

## 可忽略的设置项

`ignore` 属性接受一个字符串数组，用于指定需要隐藏的设置项。可用的设置项包括：

### 基于 CommonStyleTemplate 类型的键名：
- `textColor` - 文字颜色
- `containerGradient` - 容器背景渐变
- `background` - 背景设置（包含背景渐变和背景图片）
- `borderRadius` - 组件圆角（包含所有圆角设置）
- `itemStyle` - 子项样式（包含子项背景色和圆角）
- `margin` - 组件外边距（包含所有边距设置）
- `hidden` - 组件可见性

当忽略某个键名时，与该键名相关的所有设置项都会被隐藏。例如，忽略 `margin` 将隐藏所有边距相关的设置项，忽略 `borderRadius` 将隐藏所有圆角相关的设置项。

## 事件

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| update:componentData | 组件数据更新时触发 | 更新后的组件数据 |
| change | 样式变更时触发 | 更新后的组件数据 |

## 边距和圆角设置模式

### 边距设置模式

组件提供了两种边距设置模式，通过 `marginMode` 属性控制：

1. **分组设置模式 (grouped)**：默认模式，将左右边距合并为一个设置项
   - 显示上边距、下边距和水平边距（左右边距）三个设置项
   - 适合简化界面，快速设置对称的左右边距

2. **分开设置模式 (separate)**：上、下、左、右边距单独设置
   - 显示上边距、下边距、左边距、右边距四个设置项
   - 适合需要精细控制每个方向边距的场景

### 圆角设置模式

组件提供了两种圆角设置模式，通过 `radiusMode` 属性控制：

1. **分组设置模式 (grouped)**：默认模式，将圆角分为上圆角和下圆角两组进行设置
   - 显示上圆角和下圆角两个设置项
   - 适合简化界面，快速设置对称圆角

2. **分开设置模式 (separate)**：左上、右上、左下、右下圆角单独设置
   - 显示四个角的圆角设置项
   - 适合需要精细控制每个角圆角的场景

子项圆角设置也遵循相同的模式。

## 组件数据结构

```typescript
/**
 * 圆角半径配置 (单位: px)
 * @interface CornerRadii
 */
interface CornerRadii {
  /** 左上角半径 @default 0 */
  topLeft: number
  /** 右上角半径 @default 0 */
  topRight: number
  /** 左下角半径 @default 0 */
  bottomLeft: number
  /** 右下角半径 @default 0 */
  bottomRight: number
}

/**
 * 渐变样式配置
 * @interface GradientStyle
 */
interface GradientStyle {
  /** 渐变起始颜色 (HEX/RGBA) */
  startColor: string
  /** 渐变结束颜色 (HEX/RGBA) */
  endColor: string
  /** 渐变角度 (单位: 度) @default "90deg" */
  angle: string
}

/**
 * 间距配置 (单位: px)
 * @interface Spacing
 */
interface Spacing {
  /** 上边距 @default 0 */
  top: number
  /** 右边距 @default 0 */
  right: number
  /** 下边距 @default 0 */
  left: number
  /** 左边距 @default 0 */
  bottom: number
}

/**
 * 全局模板配置
 * @interface GlobalTemplate
 * @description 定义组件的通用样式规范，用于统一管理设计系统的基础样式
 */
interface GlobalTemplate {
  /**
   * 主要文字颜色 (HEX格式)
   * @default "#333333"
   * @example "#ff0000"
   */
  textColor: string

  /**
   * 容器背景渐变配置
   * @description 应用于容器级的渐变效果，会覆盖纯色背景
   */
  containerGradient: GradientStyle

  /** 组件背景配置 */
  background: {
    /** 背景图片地址 (支持 URL/base64) */
    imageUrl: string
    /** 背景图片透明度 (0-100) @default 100 */
    opacity: number
    /** 背景渐变配置 (与图片共存时叠加显示) */
    gradient: GradientStyle
  }

  /** 组件圆角配置 */
  borderRadius: CornerRadii

  /** 子项通用样式 */
  itemStyle: {
    /** 背景颜色 (HEX/RGBA) @default "#ffffff" */
    backgroundColor: string
    /** 子项圆角配置 */
    borderRadius: CornerRadii
  }

  /** 组件外边距配置 */
  margin: Spacing
}
``` 