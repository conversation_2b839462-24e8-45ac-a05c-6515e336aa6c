<template id="diy-rubik-cube">
  <div class="diy-rubik-cube" :class="`w-[${cubeWidth / 2}px]`">
    <div class="decorate-cube relative">
      <ul v-for="(n, nIndex) in densityNum" :key="nIndex" class="float-left p-0 m-0 list-none">
        <li
          v-for="(i, iIndex) in densityNum"
          :key="iIndex + 100"
          :class="[
            'cube-item text-center cursor-pointer  border-b border-l border-gray-300 flex items-center justify-center',
            { 'item-selecting': isSelecting(n, i), 'item-selected': isSelected(n, i) },
          ]"
          :style="{ width: `${cubeCellWidth}px`, height: `${cubeCellHeight}px` }"
          :data-x="n"
          :data-y="i"
          @click="onClickCubeItem"
          @mouseenter="onEnterCubeItem"
        >
          <n-icon size="16" color="#999">
            <PlusOutlined />
          </n-icon>
        </li>
      </ul>

      <div
        v-for="(item, index) in selectedList"
        :key="index + 100"
        class="cube-selected absolute box-border text-center cursor-pointer bg-blue-50 border"
        :class="{ 'click-cube-selected': index == selCubeIndex }"
        :style="{
          width: `${getSelectedWidth(item)}px`,
          height: `${getSelectedHeight(item)}px`,
          top: `${getSelectedTop(item)}px`,
          left: `${getSelectedLeft(item)}px`,
        }"
        @click="selCubeInfo(index)"
      >
        <template v-if="item.cubeData && item.cubeData.imageUrl">
          <img
            :src="item.cubeData.imageUrl"
            class="w-full h-full custom-image"
            object-fit="cover"
          />
        </template>
        <template v-else>
          <div class="absolute top-1/2 left-1/2 w-full text-xs -translate-x-1/2 -translate-y-1/2">
            {{
              Math.round(
                (props.cubeHeight / props.density) *
                  (parseInt(item.end.x) - parseInt(item.start.x) + 1),
              )
            }}
            x
            {{
              Math.round(
                (props.cubeWidth / props.density) *
                  (parseInt(item.end.y) - parseInt(item.start.y) + 1),
              )
            }}
            像素
          </div>
        </template>

        <div
          v-show="index === selCubeIndex && styleArr.length === 0"
          class="absolute -top-2 -right-2 z-10 bg-white rounded-full"
          @click.stop="delCubeList(index)"
        >
          <n-icon size="20" color="#999">
            <CloseCircleFilled />
          </n-icon>
        </div>
      </div>
    </div>
    <div :style="{ width: `${cubeExhibitWidth}px`, height: `${cubeExhibitHeight}px` }"></div>
    <link-selector
      v-show="selCubeIndex !== -1"
      v-model="cubeImgInfo"
      mode="img"
      @change="addCubeImg"
      :clearable="false"
      :addable="false"
      class="mt-5"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue'
  import { useMessage } from 'naive-ui'
  import { CloseCircleFilled, PlusOutlined } from '@vicons/antd'
  import LinkSelector from '@/components/SelectLink/src/linkSelector.vue'
  import { LinkSelectorItem } from '@/components/SelectLink'
  import { cloneDeep } from 'lodash-es'
  import type { RubikCubeItem } from '@/components/Decoration/src/types/rubikCubeComponent'

  /**
   * 魔方选择项接口
   */
  interface SelectingItem {
    /** 临时起始坐标 */
    tempStart: { x: number; y: number } | null
    /** 临时结束坐标 */
    tempEnd: { x: number; y: number } | null
    /** 实际起始坐标 */
    start: { x: number; y: number } | null
    /** 实际结束坐标 */
    end: { x: number; y: number } | null
  }

  /**
   * 魔方组件属性接口
   */
  interface RubikCubeProps {
    /** 魔方密度 - 控制网格数量 */
    density?: number
    /** 魔方宽度 */
    cubeWidth?: number
    /** 魔方高度 */
    cubeHeight?: number
    /** 魔方展示宽度 */
    cubeExhibitWidth?: number
    /** 魔方展示高度 */
    cubeExhibitHeight?: number
    /** 魔方列表数据 */
    cubeList?: RubikCubeItem[]
    /** 魔方样式列表 */
    styleArr?: any[]
  }

  const props = withDefaults(defineProps<RubikCubeProps>(), {
    density: 4,
    cubeWidth: 750,
    cubeHeight: 750,
    cubeExhibitWidth: 320,
    cubeExhibitHeight: 320,
    cubeList: () => [],
    styleArr: () => [],
  })

  const emit = defineEmits(['change', 'max'])
  const message = useMessage()

  // 状态管理
  const selectingItem = ref<SelectingItem>({
    tempStart: null,
    tempEnd: null,
    start: null,
    end: null,
  })

  /** 已选中的魔方单元列表 */
  const selectedList = ref<RubikCubeItem[]>([])
  /** 当前选中的魔方索引 */
  const selCubeIndex = ref(-1)
  /** 已选中的格子坐标数组 */
  const addGridArr = ref<{ x: number; y: number }[]>([])

  /** 默认图片信息配置 */
  const defaultImgInfo = {
    link: {
      name: '',
      title: '',
      url: '',
    },
    imageUrl: '',
    title: '',
  }

  /** 魔方图片信息 */
  const cubeImgInfo = ref<LinkSelectorItem[]>([cloneDeep(defaultImgInfo)])
  /** 魔方最大高度 */
  const maxHeight = ref(0)

  // 计算属性
  const densityNum = computed(() => parseInt(String(props.density)))
  const cubeCellHeight = computed(() => Number(props.cubeExhibitHeight) / densityNum.value)
  const cubeCellWidth = computed(() => Number(props.cubeExhibitWidth) / densityNum.value)

  /**
   * 获取网格坐标数据
   * @param start 起始坐标
   * @param end 结束坐标
   * @param type 操作类型：1-添加，2-删除
   * @returns 网格坐标数组
   */
  const getGridSeat = (
    start: { x: number; y: number },
    end: { x: number; y: number },
    type = 1,
  ) => {
    const gridData: { x: number; y: number }[] = []

    for (let i = start.x; i <= end.x; i++) {
      for (let j = start.y; j <= end.y; j++) {
        if (type === 1) {
          gridData.push({ x: i, y: j })
        } else {
          const startIndex = addGridArr.value.findIndex((v) => v.x === i && v.y === j)
          if (startIndex !== -1) {
            addGridArr.value.splice(startIndex, 1)
          }
        }
      }
    }

    if (type === 2) {
      getCubeMaxHeight()
    }

    return gridData
  }

  /**
   * 计算魔方最大高度
   */
  const getCubeMaxHeight = () => {
    const size = Number(props.cubeWidth) / densityNum.value
    let maxY = 0

    for (let i = 1; i <= densityNum.value; i++) {
      const yInfo = addGridArr.value.filter((v) => v.y === i)
      if (yInfo.length) {
        const y = yInfo[yInfo.length - 1].y
        maxY = Math.max(maxY, y)
      }
    }

    maxHeight.value = maxY * size
    emit('max', maxHeight.value)
  }

  /**
   * 获取魔方样式
   * @param start 起始坐标
   * @param end 结束坐标
   * @returns 样式对象
   */
  const getCubeStyle = (start: { x: number; y: number }, end: { x: number; y: number }) => {
    const infoWidth = Math.round(
      (Number(props.cubeHeight) / densityNum.value) * (end.x - start.x + 1),
    )
    const infoHeight = Math.round(
      (Number(props.cubeWidth) / densityNum.value) * (end.y - start.y + 1),
    )
    const infoTop = Math.round((Number(props.cubeWidth) / densityNum.value) * (start.y - 1))
    const infoLeft = Math.round((Number(props.cubeWidth) / densityNum.value) * (start.x - 1))

    return {
      top: `${infoTop / 2}px`,
      left: `${infoLeft / 2}px`,
      width: `${infoWidth / 2}px`,
      height: `${infoHeight / 2}px`,
    }
  }

  /**
   * 更新数据并触发change事件
   */
  const updateData = () => {
    emit('change', selectedList.value)
  }

  /**
   * 更新选择状态
   */
  const updateSelecting = () => {
    const tempStart = selectingItem.value.tempStart
    const tempEnd = selectingItem.value.tempEnd
    if (!tempStart || !tempEnd) return

    selectingItem.value.start = {
      x: Math.min(tempStart.x, tempEnd.x),
      y: Math.min(tempStart.y, tempEnd.y),
    }
    selectingItem.value.end = {
      x: Math.max(tempStart.x, tempEnd.x),
      y: Math.max(tempStart.y, tempEnd.y),
    }
  }

  /**
   * 清除选择状态
   */
  const clearSelecting = () => {
    selectingItem.value.tempStart = null
    selectingItem.value.tempEnd = null
    selectingItem.value.start = null
    selectingItem.value.end = null
  }

  /**
   * 从事件中获取坐标
   * @param event 鼠标事件
   * @returns 坐标对象
   */
  const coordFromCubeEvent = (event: MouseEvent) => {
    const el = event.currentTarget as HTMLElement
    const x = el.getAttribute('data-x')
    const y = el.getAttribute('data-y')
    return { x: Number(x), y: Number(y) }
  }

  /**
   * 判断坐标是否在指定范围内
   * @param x x坐标
   * @param y y坐标
   * @param item 范围对象
   * @returns 是否在范围内
   */
  const isContain = (
    x: number,
    y: number,
    item: { start: { x: number; y: number }; end: { x: number; y: number } },
  ) => {
    return item.start.x <= x && x <= item.end.x && item.start.y <= y && y <= item.end.y
  }

  /**
   * 魔方点击事件处理
   * @param event 鼠标事件
   */
  const onClickCubeItem = (event: MouseEvent) => {
    const el = event.currentTarget as HTMLElement
    if (el.classList.contains('item-selected')) {
      message.warning('该位置已被占用')
      return
    }

    const coord = coordFromCubeEvent(event)
    // 开始点击
    if (!selectingItem.value.tempStart) {
      selectingItem.value.tempStart = coord
      selectingItem.value.tempEnd = coord
      selectingItem.value.start = coord
      selectingItem.value.end = coord
      return
    }

    selectingItem.value.tempEnd = coord
    updateSelecting()

    // 添加已选中的格子
    const gridData = getGridSeat(selectingItem.value.start!, selectingItem.value.end!)
    // 用于判断格子是否被选中
    const exists = verifyGridExist(selectingItem.value.start!, selectingItem.value.end!)

    if (exists) return

    addGridArr.value.push(...gridData)
    getCubeMaxHeight()
    //加入选中的
    const selectedItem: RubikCubeItem = {
      start: selectingItem.value.start!,
      end: selectingItem.value.end!,
      cubeData: cloneDeep(defaultImgInfo),
      style: {
        top: '0px',
        left: '0px',
        width: '0px',
        height: '0px',
      },
    }
    //计算选中层的宽度。
    const style = getCubeStyle(selectedItem.start, selectedItem.end)
    selectedItem.style = style
    selectedList.value.push(selectedItem)
    selCubeInfo(selectedList.value.length - 1)
    // 清除状态
    clearSelecting()
    updateData()
  }

  /**
   * 验证格子是否已存在
   * @param start 起始坐标
   * @param end 结束坐标
   * @returns 是否已存在
   */
  const verifyGridExist = (start: { x: number; y: number }, end: { x: number; y: number }) => {
    const gridData = getGridSeat(start, end)
    const mergeArr = [...addGridArr.value, ...gridData]
    const delRepeatArr: { x: number; y: number }[] = []
    let repeatCount = 0

    mergeArr.forEach((a) => {
      const boo = delRepeatArr.every((b) => a.x !== b.x || a.y !== b.y)
      boo ? delRepeatArr.push(a) : repeatCount++
    })

    if (repeatCount > 0) {
      message.error('该位置无法选择')
      return true
    }

    return false
  }

  /**
   * 鼠标进入魔方格子事件
   * @param event 鼠标事件
   */
  const onEnterCubeItem = (event: MouseEvent) => {
    if (selectingItem.value.tempStart) {
      const coord = coordFromCubeEvent(event)
      selectingItem.value.tempEnd = coord
      updateSelecting()
    }
  }

  /**
   * 判断格子是否正在被选择
   * @param x x坐标
   * @param y y坐标
   * @returns 是否正在被选择
   */
  const isSelecting = (x: number, y: number) => {
    const item = selectingItem.value
    if (item.tempStart && item.tempEnd) {
      return isContain(x, y, {
        start: item.tempStart,
        end: item.tempEnd,
      })
    }
    return false
  }

  /**
   * 判断格子是否已被选择
   * @param x x坐标
   * @param y y坐标
   * @returns 是否已被选择
   */
  const isSelected = (x: number, y: number) => {
    return selectedList.value.some((item) => isContain(x, y, item))
  }

  /**
   * 选择魔方信息
   * @param index 魔方索引
   */
  const selCubeInfo = (index: number) => {
    selCubeIndex.value = index
    if (selectedList.value[selCubeIndex.value]) {
      cubeImgInfo.value = [
        selectedList.value[selCubeIndex.value].cubeData || cloneDeep(defaultImgInfo),
      ]
    }
  }

  /**
   * 删除魔方列表项
   * @param index 要删除的索引
   */
  const delCubeList = (index: number) => {
    getGridSeat(selectedList.value[index].start, selectedList.value[index].end, 2)
    selectedList.value.splice(index, 1)
    if (index === selCubeIndex.value) {
      selCubeInfo(selCubeIndex.value - 1)
    }
    updateData()
  }

  /**
   * 添加魔方图片
   * @param imgArr 图片数组
   */
  const addCubeImg = (imgArr: any[]) => {
    if (selCubeIndex.value !== -1) {
      selectedList.value[selCubeIndex.value].cubeData = imgArr[0]
    }
    updateData()
  }

  /**
   * 获取选中项的宽度
   * @param item 魔方项
   * @returns 宽度值
   */
  const getSelectedWidth = (item: RubikCubeItem) => {
    return (parseInt(String(item.end.x)) - parseInt(String(item.start.x)) + 1) * cubeCellWidth.value
  }

  /**
   * 获取选中项的高度
   * @param item 魔方项
   * @returns 高度值
   */
  const getSelectedHeight = (item: RubikCubeItem) => {
    return (
      (parseInt(String(item.end.y)) - parseInt(String(item.start.y)) + 1) * cubeCellHeight.value
    )
  }

  /**
   * 获取选中项的顶部位置
   * @param item 魔方项
   * @returns 顶部位置值
   */
  const getSelectedTop = (item: RubikCubeItem) => {
    return (item.start.y - 1) * cubeCellHeight.value
  }

  /**
   * 获取选中项的左侧位置
   * @param item 魔方项
   * @returns 左侧位置值
   */
  const getSelectedLeft = (item: RubikCubeItem) => {
    return (item.start.x - 1) * cubeCellWidth.value
  }

  // 监听属性变化
  watch(
    () => props.cubeList,
    (val) => {
      selectedList.value = val
      addGridArr.value = []
      val.forEach((v) => {
        const gridData = getGridSeat(v.start, v.end)
        addGridArr.value.push(...gridData)
      })
    },
    { deep: true, immediate: true },
  )

  watch(
    () => props.styleArr,
    (val) => {
      if (!val.length) return
      addGridArr.value = []
      const newList: RubikCubeItem[] = []

      val.forEach((v) => {
        const gridData = getGridSeat(v.start, v.end)
        const dataObj: RubikCubeItem = {
          start: v.start,
          end: v.end,
          cubeData: cloneDeep(defaultImgInfo),
          style: v.style,
        }

        if (selectedList.value.length > 0) {
          const dataIndex = selectedList.value.findIndex(
            (listVal) =>
              listVal.start.x === v.start.x &&
              listVal.start.y === v.start.y &&
              listVal.end.x === v.end.x &&
              listVal.end.y === v.end.y,
          )
          if (dataIndex !== -1) {
            Object.assign(dataObj, selectedList.value[dataIndex])
          }
        }

        const style = getCubeStyle(v.start, v.end)
        dataObj.style = style
        addGridArr.value.push(...gridData)
        newList.push(dataObj)
      })

      selCubeIndex.value = -1
      cubeImgInfo.value = []
      selectedList.value = newList
      getCubeMaxHeight()
      updateData()
    },
    { deep: true, immediate: true },
  )
</script>

<style lang="scss" scoped>
  .diy-rubik-cube {
    display: inline-block;
    width: 100%;
  }

  .decorate-cube {
    position: relative;
  }

  .decorate-cube .cube-item {
    border-right: 1px solid #b7b7b7;
  }

  .decorate-cube .cube-selected {
    position: absolute;
    box-sizing: border-box;
    color: #88c4dc;
    text-align: center;
    cursor: pointer;
    background-color: #e8f7fd;
    border: 1px solid #bbddff;
  }

  .decorate-cube .click-cube-selected {
    border: 1px solid #3388ff;
  }

  .decorate-cube .cube-selected-text {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    font-size: 12px;
    transform: translateX(-50%) translateY(-50%);
  }

  .decorate-cube .cube-col {
    float: left;
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .decorate-cube .cube-item:first-child {
    border-top: 1px solid #b7b7b7;
  }

  .decorate-cube .cube-item {
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    background: #fff;
    border-bottom: 1px solid #b7b7b7;
    border-left: 1px solid #b7b7b7;
  }

  .decorate-cube .cube-item.item-selecting {
    background: #e0edff;
  }

  .decorate-cube .cube-item.item-selected {
    visibility: hidden;
    background: #e0edff;
  }

  .cube-del-icon {
    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 10;
  }

  .del-sel-icon {
    font-size: 19px;
    color: #666;
    background: #fff;
    border-radius: 50%;
  }

  .custom-image {
    position: relative;
    display: inline-block;
    overflow: hidden;
  }
</style>
