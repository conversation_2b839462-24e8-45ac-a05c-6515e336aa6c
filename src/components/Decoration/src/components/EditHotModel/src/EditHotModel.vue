<template>
  <basicModal @register="modalRegister" ref="modalRef" @on-ok="submitForm">
    <template #default>
      <div class="flex min-h-[400px]">
        <!-- 热区预览设置 -->
        <div
          class="hot-area-img-wrap content-box relative bg-gray-100 border border-dashed border-gray-500 bg-no-repeat"
          :style="{
            backgroundImage: 'url(' + img(value.imageUrl) + ')',
            width: contentBoxWidth + 'px',
            height: contentBoxHeight + 'px',
          }"
        >
          <div
            v-for="(item, index) in dragBoxArr"
            :id="'box_' + index"
            :key="index"
            class="area-box border border-solid border-[#ccc] absolute select-none p-[5px]"
            :style="{
              left: item.left + item.unit,
              top: item.top + item.unit,
              width: item.width + item.unit,
              height: item.height + item.unit,
            }"
            @mousedown="mouseDown($event, index)"
          >
            <span>{{ index + 1 }}</span>
            <template v-if="item.link.title">
              <span class="p-[4px]">|</span>
              <span>{{ item.link.title }}</span>
            </template>
            <span class="box1" @mousedown.stop="resizeMouseDown($event, index)"></span>
            <span class="box2" @mousedown.stop="resizeMouseDown($event, index)"></span>
            <span class="box3" @mousedown.stop="resizeMouseDown($event, index)"></span>
            <span class="box4" @mousedown.stop="resizeMouseDown($event, index)"></span>
          </div>
        </div>
        <!-- 添加热区 -->
        <div class="pl-[25px]">
          <h3 class="mb-4 text-lg text-black">{{ t('decoration.common.hotManage') }}</h3>
          <n-button type="primary" class="mb-4" @click="addArea">
            {{ t('decoration.common.addHotArea') }}
          </n-button>
          <div class="max-h-[300px] overflow-y-auto">
            <template v-for="(item, index) in dragBoxArr" :key="index">
              <div class="mb-4" v-if="item">
                <n-space class="flex items-center">
                  <div>{{ t('decoration.common.hotArea') + (index + 1) }}</div>
                  <SelectLink v-model="item.link" />
                  <n-icon
                    size="20"
                    color="#666666"
                    class="del cursor-pointer"
                    :component="CloseCircleOutline"
                    @click="dragBoxArr.splice(index, 1)"
                  />
                </n-space>
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, reactive } from 'vue'
  import { basicModal, useModal } from '@/components/Modal'
  import { useMessage } from 'naive-ui'
  import { CloseCircleOutline } from '@vicons/ionicons5'
  import { useI18n } from '@/hooks/web/useI18n'
  import { img } from '@/utils/common'
  import { cloneDeep } from 'lodash-es'
  import { SelectLink } from '@/components/SelectLink'

  const { t } = useI18n()

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['model-value', 'register'])

  const value: any = computed(() => {
    return props.modelValue
  })

  const message = useMessage()
  const contentBoxWidth = ref(360)
  const contentBoxHeight = ref(400)
  const dragBoxArr: any = reactive([])

  const imgRatio = ref(1) // 图片比例

  // 热区尺寸
  const areaRadio = ref(0.25) // 占位图比例
  const areaWidth = ref(100)
  const areaHeight = ref(100)
  const areaNum = ref(4) // 每行显示的数量

  /* 回显设置 */
  const setFieldsValue = () => {
    // 计算图片比例
    imgRatio.value = value.value.imgHeight / value.value.imgWidth

    // 根据图片比例，调整图片高度
    contentBoxHeight.value = Math.floor(contentBoxWidth.value * imgRatio.value)

    areaWidth.value = Math.floor(areaRadio.value * contentBoxWidth.value)
    areaHeight.value = Math.floor(areaRadio.value * contentBoxHeight.value)
    areaNum.value = Math.floor(contentBoxWidth.value / areaWidth.value)

    if (Object.keys(value.value.heatMapData).length) {
      dragBoxArr.splice(0, dragBoxArr.length, ...value.value.heatMapData)
    } else {
      dragBoxArr.splice(0, dragBoxArr.length)
      addArea()
    }
  }

  /**
   * 热区弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    title: t('decoration.common.hotSet'),
    subBtuText: t('common.confirmText'), //'确定',
    width: 760,
  })

  /**
   * 提交处理
   */
  const submitForm = async () => {
    try {
      setSubLoading(true)
      let isOk = true
      for (let i = 0; i < dragBoxArr.length; i++) {
        if (!dragBoxArr[i].link.title) {
          message.error(
            `${t('decoration.common.hotArea')}${i + 1}${t('decoration.common.hotAreaRule')}`,
          )
          isOk = false
          break
        }
      }

      if (!isOk) return

      dragBoxArr.forEach((item: any, index: number) => {
        const box: any = document.getElementById('box_' + index)
        item.width = parseFloat((box.offsetWidth / contentBoxWidth.value) * 100).toFixed(2)
        item.height = parseFloat((box.offsetHeight / contentBoxHeight.value) * 100).toFixed(2)
        item.left = parseFloat((box.offsetLeft / contentBoxWidth.value) * 100).toFixed(2)
        item.top = parseFloat((box.offsetTop / contentBoxHeight.value) * 100).toFixed(2)
        item.unit = '%'
      })

      value.value.heatMapData = cloneDeep(dragBoxArr)
      emit('model-value', value.value.heatMapData)
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('goods.service.operationFailed'))
    } finally {
      setSubLoading(false)
    }
  }

  // 添加热区
  const addArea = () => {
    let left = (dragBoxArr.length % areaNum.value) * areaWidth.value
    let top = Math.floor(dragBoxArr.length / areaNum.value) * areaHeight.value
    let edgeHeight = top + areaHeight.value / 2
    if (top >= contentBoxHeight.value || edgeHeight >= contentBoxHeight.value) {
      top = 0
      left = 0
    }

    dragBoxArr.push({
      left,
      top,
      width: areaWidth.value,
      height: areaHeight.value,
      unit: 'px',
      link: {
        name: '',
        title: '',
        url: '',
        appid: '',
        page: '',
        mobile: '',
      },
    })
  }

  // 移动事件
  const mouseDown = (e: any, index: number) => {
    const box: any = document.getElementById('box_' + index)
    const disX = e.clientX - box.offsetLeft
    const disY = e.clientY - box.offsetTop

    // 鼠标移动时
    document.onmousemove = function (e) {
      box.style.left = e.clientX - disX + 'px'
      box.style.top = e.clientY - disY + 'px'

      // 边界判断
      if (e.clientX - disX < 0) {
        box.style.left = 0
      }

      if (e.clientX - disX > contentBoxWidth.value - box.offsetWidth) {
        box.style.left = contentBoxWidth.value - box.offsetWidth + 'px'
      }

      if (e.clientY - disY < 0) {
        box.style.top = 0
      }

      if (e.clientY - disY > contentBoxHeight.value - box.offsetHeight) {
        box.style.top = contentBoxHeight.value - box.offsetHeight + 'px'
      }

      dragBoxArr[index].left = box.offsetLeft
      dragBoxArr[index].top = box.offsetTop
      dragBoxArr[index].width = box.offsetWidth
      dragBoxArr[index].height = box.offsetHeight
      dragBoxArr[index].unit = 'px'
    }

    // 鼠标抬起时
    document.onmouseup = function () {
      document.onmousemove = null
    }
  }

  // 拖拽大小事件
  const resizeMouseDown = (e: any, index: number) => {
    const oEv = e
    oEv.stopPropagation()
    const box: any = document.getElementById('box_' + index)
    const className = e.target.className

    // 获取移动前盒子的宽高，
    const oldWidth = box.offsetWidth
    const oldHeight = box.offsetHeight

    // 获取鼠标距离屏幕的left和top值
    const oldX = oEv.clientX
    const oldY = oEv.clientY

    // 元素相对于最近的父级定位
    const oldLeft = box.offsetLeft
    const oldTop = box.offsetTop

    // 设置最小的宽度
    const minWidth = 50
    const minHeight = 50

    document.onmousemove = function (e) {
      const oEv = e

      // 左上角
      if (className == 'box1') {
        let width = oldWidth - (oEv.clientX - oldX)
        const maxWidth = contentBoxWidth.value

        let height = oldHeight - (oEv.clientY - oldY)
        const maxHeight = contentBoxHeight.value - oldTop

        let left = oldLeft + (oEv.clientX - oldX)
        let top = oldTop + (oEv.clientY - oldY)

        if (width < minWidth) {
          width = minWidth
        }
        if (width > maxWidth) {
          width = maxWidth
        }

        if (height < minHeight) {
          height = minHeight
        }
        if (height > maxHeight) {
          height = maxHeight
        }

        if (oldLeft == 0 && oldTop == 0) {
          // 坐标：left = 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 最小宽度，top = 最小高度
            left = minWidth
            top = minHeight
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 最小宽度，top = 不予处理
            left = minWidth
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，left = 不予处理，top = 最小高度
            top = minHeight
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，left = 不予处理，top = 不予处理
          }
        } else if (oldLeft == 0 && oldTop > 0) {
          // 坐标：left = 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 最小宽度，top = 元素上偏移位置
            left = minWidth
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 最小宽度，top = 元素上偏移位置
            left = minWidth
            top = box.offsetTop
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，left = 不予处理，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，left = 不予处理，top = 不予处理
          }
        } else if (oldLeft > 0 && oldTop == 0) {
          // 坐标：left > 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 元素左偏移位置，top = 元素上偏移位置
            left = box.offsetLeft
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 元素左偏移位置，top = 0
            left = box.offsetLeft
            top = 0
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，left = 不予处理，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，left = 不予处理，top = 不予处理
          }
        } else if (oldLeft > 0 && oldTop > 0) {
          // 坐标：left > 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 元素左偏移位置，top = 元素上偏移位置
            left = box.offsetLeft
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 元素左偏移位置，top = 元素上偏移位置
            left = box.offsetLeft
            top = box.offsetTop
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，left = 不予处理，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，left = 不予处理，top = 不予处理
          }
        }

        // 左上宽
        if (left < 0) {
          left = 0
          width = oldWidth - (oEv.clientX - oldX) + (oldLeft + (oEv.clientX - oldX))
        }

        // 左上 高
        if (top < 0) {
          top = 0
          height = oldTop + (oEv.clientY - oldY) + (oldHeight - (oEv.clientY - oldY))
        }

        box.style.width = width + 'px'
        box.style.height = height + 'px'
        box.style.left = left + 'px'
        box.style.top = top + 'px'
      } else if (className == 'box2') {
        // 右上角

        let width = oldWidth + (oEv.clientX - oldX)
        const maxWidth = contentBoxWidth.value - oldLeft

        let height = oldHeight - (oEv.clientY - oldY)
        const maxHeight = contentBoxHeight.value - oldTop

        let top = oldTop + (oEv.clientY - oldY)

        if (width < minWidth) {
          width = minWidth
        }
        if (width > maxWidth) {
          width = maxWidth
        }

        if (height < minHeight) {
          height = minHeight
        }
        if (height > maxHeight) {
          height = maxHeight
        }

        if (oldLeft == 0 && oldTop == 0) {
          // 坐标：left = 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，top = 最小高度
            top = minHeight
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，不予处理
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，top = 最小高度
            top = minHeight
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft == 0 && oldTop > 0) {
          // 坐标：left = 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft > 0 && oldTop == 0) {
          // 坐标：left = 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，top = 0
            top = 0
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft > 0 && oldTop > 0) {
          // 坐标：left > 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，top = 元素上偏移位置
            top = box.offsetTop
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        }

        // 右上高
        if (top < 0) {
          top = 0
          height = oldTop + (oEv.clientY - oldY) + (oldHeight - (oEv.clientY - oldY))
        }

        box.style.width = width + 'px'
        box.style.height = height + 'px'
        box.style.top = top + 'px'
      } else if (className == 'box3') {
        // 左下角

        let width = oldWidth - (oEv.clientX - oldX)
        const maxWidth = contentBoxWidth.value

        let height = oldHeight + (oEv.clientY - oldY)
        const maxHeight = contentBoxHeight.value - oldTop

        let left = oldLeft + (oEv.clientX - oldX)

        if (width < minWidth) {
          width = minWidth
        }
        if (width > maxWidth) {
          width = maxWidth
        }

        if (height < minHeight) {
          height = minHeight
        }
        if (height > maxHeight) {
          height = maxHeight
        }

        if (oldLeft == 0 && oldTop == 0) {
          // 坐标：left = 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 最小宽度
            left = minWidth
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 最小宽度
            left = minWidth
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，不予处理
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft == 0 && oldTop > 0) {
          // 坐标：left = 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 最小宽度
            left = minWidth
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 最小宽度
            left = minWidth
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，不予处理
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft > 0 && oldTop == 0) {
          // 坐标：left > 0，top = 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 元素左偏移位置
            left = box.offsetLeft
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 元素左偏移位置
            left = box.offsetLeft
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，不予处理
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        } else if (oldLeft > 0 && oldTop > 0) {
          // 坐标：left > 0，top > 0

          if (width == minWidth && height == minHeight) {
            // 宽高 = 最小值，left = 元素左偏移位置
            left = box.offsetLeft
          } else if (width == minWidth && height > minHeight) {
            // 宽 = 最小值，高 > 最小值，left = 元素左偏移位置
            left = box.offsetLeft
          } else if (width > minWidth && height == minHeight) {
            // 宽 > 最小值，高 = 最小值，不予处理
          } else if (width > minWidth && height > minHeight) {
            // 宽 > 最小值，高 > 最小值，不予处理
          }
        }

        if (left < 0) {
          left = 0
          width = oldWidth - (oEv.clientX - oldX) + (oldLeft + (oEv.clientX - oldX))
        }

        box.style.width = width + 'px'
        box.style.height = height + 'px'
        box.style.left = left + 'px'
      } else if (className == 'box4') {
        // 右下角

        let width = oldWidth + (oEv.clientX - oldX)
        const maxWidth = contentBoxWidth.value - oldLeft

        let height = oldHeight + (oEv.clientY - oldY)
        const maxHeight = contentBoxHeight.value - oldTop

        if (width < minWidth) {
          width = minWidth
        }
        if (width > maxWidth) {
          width = maxWidth
        }

        if (height < minHeight) {
          height = minHeight
        }
        if (height > maxHeight) {
          height = maxHeight
        }

        box.style.width = width + 'px'
        box.style.height = height + 'px'
      }

      dragBoxArr[index].left = box.offsetLeft
      dragBoxArr[index].top = box.offsetTop
      dragBoxArr[index].width = box.offsetWidth
      dragBoxArr[index].height = box.offsetHeight
      dragBoxArr[index].unit = 'px'
    }

    // 鼠标抬起时
    document.onmouseup = function () {
      document.onmousemove = null
      document.onmouseup = null
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    openModal,
    closeModal,
    submitForm,
    setFieldsValue,
  })
</script>

<style scoped lang="scss">
  .hot-area-img-wrap {
    background-size: 100%;
  }

  .area-box {
    background-color: rgba(255, 255, 255, 0.7);
  }

  .box1,
  .box2,
  .box3,
  .box4 {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #fff;
    border: 1px solid #333;
    border-radius: 50%;
  }

  .box1 {
    top: -5px;
    left: -5px;
    cursor: nw-resize;
  }

  .box2 {
    top: -5px;
    right: -5px;
    cursor: ne-resize;
  }

  .box3 {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize;
  }

  .box4 {
    right: -5px;
    bottom: -5px;
    cursor: se-resize;
  }
</style>
