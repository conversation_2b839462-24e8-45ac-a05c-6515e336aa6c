<template>
  <div class="component-list w-[290px]">
    <n-scrollbar class="p-4">
      <n-collapse
        arrow-placement="right"
        :value="activeNames"
        @update:value="(val) => emit('update:activeNames', val)"
        :default-expanded-names="defaultExpandedNames"
      >
        <n-collapse-item
          class="component-item"
          v-for="(item, key) in componentList"
          :key="key"
          :name="key"
          :title="item.title"
        >
          <n-grid :x-gap="8" :y-gap="8" :cols="3">
            <n-grid-item v-for="(compItem, compKey) in item.list" :key="compKey">
              <div
                class="component-item-wrap flex flex-col items-center justify-center h-[65px] hover:bg-[var(--primary-color-10)] cursor-pointer transition-colors rounded"
                @click="handleAddComponent(compKey as any, compItem)"
              >
                <Icon
                  v-if="compItem.icon"
                  :icon="compItem.icon"
                  size="20px"
                  class="component-item-icon mb-[14px]"
                />
                <Icon
                  v-else
                  icon="iconfont icon-morentubiao"
                  size="20px"
                  class="component-item-icon mb-[14px]"
                />
                <span class="component-item-title truncate text-center">
                  {{ compItem.title }}
                </span>
              </div>
            </n-grid-item>
          </n-grid>
        </n-collapse-item>
      </n-collapse>
    </n-scrollbar>
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from '@/components/Icon'

  interface ComponentItem {
    title: string
    icon?: string
    list: Record<string, any>
  }

  interface Props {
    componentList: Record<string, ComponentItem>
    defaultExpandedNames?: string[]
    activeNames: string[]
  }

  const { componentList, defaultExpandedNames = [], activeNames } = defineProps<Props>()
  const emit = defineEmits<{
    (e: 'add-component', key: string, item: any): void
    (e: 'update:activeNames', value: string[]): void
  }>()

  const handleAddComponent = (key: string, item: any) => {
    emit('add-component', key, item)
  }
</script>

<style lang="scss" scoped>
  .component-list {
    background: var(--base-color);

    .component-item {
      :deep(.n-collapse-item__header-main) {
        display: flex;
        justify-content: space-between;
      }
    }

    .component-item-wrap {
      .component-item-icon {
        color: var(--primary-color);
      }

      .component-item-title {
        color: var(--text-color);
      }
    }
  }
</style>
