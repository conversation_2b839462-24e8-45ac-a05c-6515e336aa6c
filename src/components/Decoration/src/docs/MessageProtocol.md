# 装修功能前后端消息通信机制

> 本文档详细说明了移动端（uni-app）与后台管理系统（admin）在装修功能中的消息通信机制，包括消息类型、数据结构、通信流程、最佳实践、常见问题及代码示例，适用于开发、维护和扩展团队成员参考。

---

## 一、消息类型全览

### 1.1 消息类型（MessageTypeEnum）

| 枚举名              | 字符串值           | 方向         | 说明/典型场景                         |
|--------------------|--------------------|--------------|------------------------------------|
| ON_LAUNCH          | appOnLaunch        | 前端→后台    | App 启动时发送，前端通知后台页面加载  |
| ON_READY           | appOnReady         | 双向         | App 准备就绪时发送，前后端握手确认    |
| UPDATE             | update             | 后台→前端    | 数据更新时发送，常用于组件状态变化    |
| INIT               | init               | 前端→后台    | 初始化请求时发送，前端请求后台数据    |
| CHANGE             | change             | 双向         | 发送数据变更通知，组件内容变化       |
| DATA               | data               | 后台→前端    | 传递数据时发送，通常包含全量数据      |
| SELECT             | select             | 双向         | 组件选中时发送，同步选中状态          |
| ADD                | add                | 后台→前端    | 添加组件时发送，通知前端渲染新组件    |
| DELETE             | delete             | 后台→前端    | 删除组件时发送，通知前端移除组件      |
| MOVE               | move               | 后台→前端    | 移动组件时发送，通知前端调整顺序      |
| COPY               | copy               | 后台→前端    | 复制组件时发送，通知前端添加副本      |
| TOGGLE_VISIBILITY  | toggleVisibility   | 后台→前端    | 切换组件可见性时发送                 |
| RECEIVED           | received           | 前端→后台    | 前端收到消息后回传确认                |

> 注意：消息类型定义在 `src/enums/common.ts` 的 `MessageTypeEnum` 枚举中。如需扩展新类型，请先在该枚举中定义并确保前后端统一。

### 1.2 各消息类型详细说明

| 枚举名              | 触发时机/说明                                                   | 主要字段                                     |
|--------------------|-----------------------------------------------------------------|----------------------------------------------|
| ON_LAUNCH          | 前端页面（uni-app）加载完成后立即发送，通知后台准备初始化数据    | type, pageMode, message, timestamp          |
| ON_READY           | 前端初始化完成或后台准备好数据后发送，用于握手确认               | type, message, timestamp                     |
| UPDATE             | 后台数据更新后发送，通知前端更新组件状态                         | type, pageConfig, componentsData, timestamp  |
| INIT               | 前端需要初始化数据时发送，请求后台提供装修数据                   | type, pageMode, timestamp                    |
| CHANGE             | 组件内容变更、切换选中组件等，需要同步状态时发送                 | type, index, component, timestamp           |
| DATA               | 传递完整装修数据，包含页面配置和所有组件                         | type, pageConfig, componentsData, currentIndex, timestamp |
| SELECT             | 选中组件时发送，同步当前操作的组件索引                           | type, index, component, timestamp           |
| ADD                | 添加新组件时发送，通知前端在指定位置渲染                         | type, component, index, timestamp           |
| DELETE             | 删除组件时发送，通知前端移除组件                                 | type, index, timestamp                       |
| MOVE               | 移动组件时发送，通知前端调整组件顺序                             | type, fromIndex, toIndex, timestamp         |
| COPY               | 复制组件时发送，通知前端添加组件副本                             | type, component, index, timestamp           |
| TOGGLE_VISIBILITY  | 切换组件显示/隐藏状态时发送                                      | type, index, isHidden, timestamp            |
| RECEIVED           | 前端收到任何消息后，回传确认，带上原消息类型和当前状态           | type, received, currentIndexAfter, timestamp |

---

## 二、消息数据结构

### 2.1 通用结构（MessageData）

```typescript
interface MessageData {
  type: string; // 消息类型，来自MessageTypeEnum
  pageMode?: string; // 页面模式，如'decoration'
  currentIndex?: number; // 当前选中组件索引
  pageConfig?: any; // 页面配置对象
  componentsData?: any[]; // 组件数据数组
  component?: any; // 当前选中组件数据或新增组件
  message?: string; // 附加消息内容
  timestamp?: number; // 时间戳
  index?: number; // 操作的组件索引
  fromIndex?: number; // 移动操作的源索引
  toIndex?: number; // 移动操作的目标索引
  isHidden?: boolean; // 组件可见性状态
  received?: string; // 被确认的消息类型（仅RECEIVED类型用）
  currentIndexAfter?: number; // 确认后当前索引（仅RECEIVED类型用）
}
```

---

## 三、典型通信流程

### 3.1 初始化与握手

1. 前端加载后发送 `ON_LAUNCH` (appOnLaunch)。
2. 后台收到后返回 `ON_READY` (appOnReady)。
3. 前端收到 `ON_READY`，再次回传 `ON_READY`，握手完成。
4. 前端发送 `INIT` (init)，后台返回 `DATA` (data)，包含完整装修数据。

**时序图：初始化与握手**
```
前端(uni-app)                                    后台(admin)
    |                                               |
    |------ ON_LAUNCH (appOnLaunch) -------------->|
    |                                               |
    |<----- ON_READY (appOnReady) ----------------|
    |                                               |
    |------ ON_READY (appOnReady) -------------->|
    |                                               |
    |------ INIT (init) ------------------------->|
    |                                               |
    |<----- DATA (data) [完整装修数据] ------------|
    |                                               |
    |------ RECEIVED (received) ----------------->|
    |                                               |
```

### 3.2 编辑与同步流程

#### 3.2.1 选中组件流程 (SELECT)

1. 任一端选中组件时：
   - 发送 `SELECT` 消息，包含组件索引和数据
   - 对方收到后更新当前选中状态
   - 前端收到后回传 `RECEIVED` 确认

**时序图：选中组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ SELECT (select, index=2) ------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.2 添加组件流程 (ADD)

1. 后台添加组件：
   - 后台添加组件到本地数据
   - 后台发送 `ADD` 消息，包含组件数据和索引
   - 前端收到后渲染新组件
   - 前端回传 `RECEIVED` 确认
   - 后台可能随后发送 `SELECT` 选中新添加的组件

**时序图：添加组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ ADD (add) ---------------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ SELECT (select) ---------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.3 删除组件流程 (DELETE)

1. 后台删除组件：
   - 后台从本地数据中删除组件
   - 后台发送 `DELETE` 消息，包含被删除组件的索引
   - 前端收到后移除对应组件
   - 前端回传 `RECEIVED` 确认
   - 后台可能随后发送 `SELECT` 选中其他组件

**时序图：删除组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ DELETE (delete, index=2) ------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ SELECT (select, index=1) ------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.4 复制组件流程 (COPY)

1. 后台复制组件：
   - 后台复制组件并添加到本地数据
   - 后台发送 `COPY` 消息，包含新组件数据和索引
   - 前端收到后添加复制的组件
   - 前端回传 `RECEIVED` 确认
   - 后台随后发送 `SELECT` 选中新复制的组件

**时序图：复制组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ COPY (copy) -------------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ SELECT (select) ---------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.5 移动组件流程 (MOVE)

1. 后台移动组件：
   - 后台调整组件顺序
   - 后台发送 `MOVE` 消息，包含源索引和目标索引
   - 前端收到后重新排序组件
   - 前端回传 `RECEIVED` 确认
   - 后台可能随后发送 `DATA` 同步完整数据以确保顺序一致

**时序图：移动组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ MOVE (move, fromIndex=2, toIndex=4) -->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ DATA (data) [完整数据] --------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.6 切换组件可见性流程 (TOGGLE_VISIBILITY)

1. 后台切换组件可见性：
   - 后台更新组件可见性状态
   - 后台发送 `TOGGLE_VISIBILITY` 消息，包含组件索引和可见性状态
   - 前端收到后更新组件显示状态
   - 前端回传 `RECEIVED` 确认

**时序图：切换可见性**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ TOGGLE_VISIBILITY (toggleVisibility) ->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.7 组件内容变更流程 (CHANGE/UPDATE)

1. 后台编辑组件属性：
   - 后台更新组件属性
   - 后台可发送 `CHANGE` 消息，包含组件变更信息
   - 或发送 `UPDATE` 消息，包含更新的状态
   - 前端收到后更新组件渲染
   - 前端回传 `RECEIVED` 确认

**时序图：组件内容变更**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ CHANGE/UPDATE ------------------------>|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

#### 3.2.8 完整数据同步流程 (DATA)

1. 需要全量同步数据时：
   - 后台发送 `DATA` 消息，包含页面配置和所有组件数据
   - 前端收到后完全刷新渲染
   - 前端回传 `RECEIVED` 确认

**时序图：完整数据同步**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ DATA (data) [完整数据] --------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

### 3.3 典型业务场景流程

#### 3.3.1 保存装修完整流程

1. 后台点击"保存"按钮：
   - 后台向服务器保存装修数据
   - 后台发送 `DATA` 消息，包含最新保存的数据
   - 前端收到后更新本地状态，确保与后台一致
   - 前端回传 `RECEIVED` 确认

**时序图：保存装修**
```
后台(admin)         服务器API             前端(uni-app)
    |                  |                       |
    |-- 保存请求 ----->|                       |
    |                  |                       |
    |<- 保存成功 ------|                       |
    |                  |                       |
    |------------------ DATA ---------------->|
    |                  |                       |
    |<----------------- RECEIVED -------------|
    |                  |                       |
```

#### 3.3.2 添加并配置新组件流程

1. 完整的添加并配置新组件流程：
   - 后台添加新组件并发送 `ADD` 消息
   - 前端确认并回传 `RECEIVED`
   - 后台选中新组件并发送 `SELECT` 消息
   - 前端确认并回传 `RECEIVED`
   - 后台编辑组件属性并发送多个 `CHANGE` 消息
   - 前端每次确认并回传 `RECEIVED`
   - 后台最后发送 `DATA` 同步最终结果
   - 前端确认并回传 `RECEIVED`

**时序图：添加并配置新组件**
```
后台(admin)                                     前端(uni-app)
    |                                               |
    |------ ADD (add) ---------------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ SELECT (select) ---------------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ CHANGE (change) [属性1] -------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ CHANGE (change) [属性2] -------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
    |------ DATA (data) [完整数据] --------------->|
    |                                               |
    |<----- RECEIVED (received) -------------------|
    |                                               |
```

### 3.4 错误与异常处理

- **消息解析错误**：收到无法解析的JSON时，记录错误但不中断流程
- **未知消息类型**：收到未知类型时，记录警告并忽略
- **索引超出范围**：操作索引不存在的组件时，忽略操作并记录警告
- **组件类型不匹配**：使用ID而非索引查找组件，避免类型不匹配
- **重复消息处理**：使用 `timestamp` 识别并过滤重复消息

---

## 四、通信实现

### 4.1 后台端（admin）

后台管理系统使用 `postMessage` 方法向前端发送消息：

```typescript
// store/modules/decoration.ts
function postMessage(type: MessageTypeEnum = MessageTypeEnum.UPDATE, message?: string) {
  const data: MessageData = {
    type,
    pageMode: pageMode.value,
    currentIndex: currentIndex.value,
    message,
    pageConfig: toRaw(pageConfig.value),
    componentsData: toRaw(componentsData.value),
    timestamp: Date.now(),
  }
  
  if (window.previewIframe) {
    window.previewIframe.contentWindow!.postMessage(JSON.stringify(data), '*')
  }
}
```

在各种组件操作函数中显式调用 `postMessage` 方法，使用恰当的消息类型：

```typescript
// 添加组件示例
function addComponent(key: string, data: any): OperationResult {
  // 添加组件逻辑...
  
  // 发送消息通知前端
  postMessage(MessageTypeEnum.ADD)
  return { success: true }
}

// 删除组件示例
function deleteComponent(): OperationResult {
  // 删除组件逻辑...
  
  // 发送消息通知前端
  postMessage(MessageTypeEnum.DELETE)
  return { success: true }
}
```

### 4.2 前端端（uni-app）

前端使用 `window.addEventListener` 监听消息，并回传确认：

```typescript
// decoration/index.ts
const handleMessage = (event) => {
  try {
    const data = typeof event.data === 'string' 
      ? JSON.parse(event.data) 
      : event.data;
      
    // 处理消息...
    
    // 回传确认
    window.parent.postMessage(JSON.stringify({
      type: MessageTypeEnum.RECEIVED,
      received: data.type,
      timestamp: Date.now(),
      currentIndexAfter: currentIndex
    }), '*');
  } catch (e) {
    console.error('处理消息错误', e);
  }
}

window.addEventListener('message', handleMessage, false);
```

---

## 五、最佳实践

### 5.1 消息发送

- 避免在 `watch` 中自动发送消息，这会导致频繁通信
- 在每个操作函数（如添加、删除、移动组件）结束时显式调用 `postMessage`
- 所有消息发送都通过 `window.parent.postMessage(JSON.stringify(data), '*')`，并严格使用 `MessageTypeEnum` 枚举值。

**推荐：在具体操作后显式发送消息**
```typescript
function updateComponentStyle(component, newStyle) {
  // 更新样式...
  Object.assign(component, newStyle);
  
  // 发送更新消息
  postMessage(MessageTypeEnum.DATA, '样式已更新');
}
```

**不推荐：在 watch 中自动发送**
```typescript
watch(componentsData, () => {
  postMessage(); // 会导致任何数据变化都发送消息
}, { deep: true });
```

### 5.2 消息处理

- 使用 `switch` 语句根据消息类型分发处理
- 对异常情况进行适当处理和日志记录
- 合理使用 `try-catch` 捕获解析或处理过程中的错误

```typescript
const handleMessage = (event) => {
  try {
    const data = parseMessageData(event.data);
    
    switch (data.type) {
      case MessageTypeEnum.DATA:
        handleDataMessage(data);
        break;
      case MessageTypeEnum.ADD:
        handleAddMessage(data);
        break;
      // 其他消息类型...
      default:
        console.warn('未知消息类型:', data.type);
    }
  } catch (e) {
    console.error('消息处理错误', e);
  }
}
```

### 5.3 API 设计

- 所有消息类型统一用 `MessageTypeEnum` 枚举管理，避免硬编码字符串。
- 保持一致的参数命名，如 `index`、`component` 等。
- 使用 TypeScript 接口定义消息结构，避免类型错误。
- 在函数命名上明确动作，如 `handleAddMessage`、`handleChangeMessage` 等。

### 5.4 错误处理

- **消息解析错误**：捕获和记录 JSON 解析错误。
- **无效消息**：检查必要字段是否存在，如 `type`、`timestamp`。
- **越界索引**：验证组件索引是否在有效范围内。
- **未知消息类型警告**：检查是否使用了未定义的消息类型，确保与 `MessageTypeEnum` 一致。

### 5.5 调试和日志

- 使用 `decorationDebug` 工具记录关键事件，便于排查问题。
- 在开发环境下可以添加更详细的日志。
- 使用 timestamp 跟踪消息的时序关系。

### 5.6 统一消息监听

- **单一监听入口**：仅在一个地方设置 `window.addEventListener('message', handleMessage)`，避免多处监听造成冲突
- **禁止重复监听**：避免在 App.vue、store/decoration.ts 等多处设置监听，确保消息只被处理一次
- **清理监听**：在组件卸载时 (`onUnmounted`) 移除消息监听 `window.removeEventListener('message', handleMessage)`
- **集中管理消息处理逻辑**：所有消息处理逻辑集中在一个函数内，按类型分发处理，提高可维护性

```typescript
// 推荐：统一的消息监听入口
// views/decoration/edit.vue

// 只添加一个事件监听器
window.addEventListener('message', handleMessage, false)

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

// 集中的消息处理函数
const handleMessage = (event) => {
  try {
    const data = parseMessageData(event.data)
    
    switch (data.type) {
      case MessageTypeEnum.ON_READY:
        handleAppReady()
        break
      case MessageTypeEnum.DATA:
        handleData(data)
        break
      // 其他消息类型...
    }
  } catch (e) {
    decorationDebug.error('消息处理错误', e)
  }
}
```

---

## 六、常见问题与解决方案

### 6.1 消息不同步问题

**症状**: 前后端组件状态不一致，如选中的组件索引不同。

**解决方案**:
- 确保在每个重要操作后发送适当的消息类型。
- 定期发送 `DATA` 消息进行全量同步。
- 前端收到消息后回传 `RECEIVED` 确认，便于排查问题。

### 6.2 消息风暴问题

**症状**: 短时间内发送大量重复消息，导致性能问题。

**解决方案**:
- 避免在 `watch` 中无条件发送消息。
- 考虑使用节流（throttle）或防抖（debounce）技术。
- 在必要时合并多个操作为一次消息。

### 6.3 消息类型错误

**症状**: 使用了错误的消息类型或字符串直接量。

**解决方案**:
- 始终使用 `MessageTypeEnum` 枚举值，而非字符串。

```typescript
// 正确示例
type: MessageTypeEnum.SELECT, // 使用枚举值 'select'

// 错误示例
type: 'select', // 直接使用字符串
```

### 6.4 前端接收消息误差问题

**症状**: 前端无法正确解析或处理后台发送的消息。

**解决方案**:
- 统一使用 JSON 字符串传输消息。
- 确保前后端使用相同的字段名和数据结构。
- 在消息处理中添加兼容性检查逻辑。

### 6.5 组件索引问题

**症状**: 复制、删除组件后，索引不一致导致操作错误组件或无法找到组件。

**解决方案**:
- **索引重置**: 在每次删除、复制等数组操作后，重新生成组件ID以避免引用错误
- **边界检查**: 在处理组件索引时始终检查是否超出边界
- **数据同步**: 在索引可能变化的操作后，发送`DATA`消息进行全量同步
- **ID优先**: 尽量使用组件ID而非索引进行查找，减少索引依赖
- **操作后验证**: 每次组件操作后，验证componentsData数组是否正确

```typescript
// 示例：删除组件后重设索引并同步数据
function deleteComponent(): OperationResult {
  if (currentIndex.value < 0) return { success: false }
  
  // 保存删除前的索引位置
  const deletedIndex = currentIndex.value
  
  // 执行删除操作
  componentsData.value.splice(deletedIndex, 1)
  
  // 重新设置当前选中索引
  if (componentsData.value.length === 0) {
    currentIndex.value = -1 // 页面配置状态
  } else if (deletedIndex >= componentsData.value.length) {
    currentIndex.value = componentsData.value.length - 1 // 选中最后一个
  } else {
    currentIndex.value = deletedIndex // 保持原位置
  }
  
  // 重新生成ID以避免引用错误
  componentsData.value.forEach(item => {
    if (item) item.id = generateRandom()
  })
  
  // 发送完整数据到前端确保同步
  postMessage(MessageTypeEnum.DATA)
  
  return { success: true }
}
```

---

## 七、完整消息处理示例

### 7.1 后台消息处理示例

```typescript
// 统一的消息处理函数
const handleMessage = (event) => {
  try {
    const data = parseMessageData(event.data);
    
    switch (data.type) {
      case MessageTypeEnum.ON_LAUNCH: // appOnLaunch
      case MessageTypeEnum.ON_READY: // appOnReady
        handleAppReady();
        break;
      case MessageTypeEnum.DATA: // data
        handleDataMessage(data);
        break;
      case MessageTypeEnum.CHANGE: // change
        handleChangeMessage(data);
        break;
      case MessageTypeEnum.RECEIVED: // received
        // 可选：记录确认消息
        break;
      default:
        console.warn('未知消息类型:', data.type);
    }
  } catch (e) {
    console.error('消息处理错误', e);
  }
}
```

### 7.2 前端消息处理示例

```typescript
// decoration/index.ts
// 处理来自后台的消息
const handleMessage = (event) => {
  try {
    const data = typeof event.data === 'string' 
      ? JSON.parse(event.data) 
      : event.data;
    
    // 处理消息...
    if (data.type === MessageTypeEnum.ADD) {
      // 处理添加组件
    } else if (data.type === MessageTypeEnum.DELETE) {
      // 处理删除组件
    }
    
    // 回传确认
    window.parent.postMessage(JSON.stringify({
      type: MessageTypeEnum.RECEIVED,
      received: data.type,
      timestamp: Date.now()
    }), '*');
  } catch (e) {
    console.error('处理消息错误', e);
  }
}
```

---

## 八、总结

本文档详细描述了基于 `MessageTypeEnum` 枚举的装修功能前后端消息通信机制。通过严格定义消息类型、数据结构和处理流程，实现了前后端解耦、实时同步和数据一致性。

团队开发时应严格遵循这些约定，确保消息类型与枚举一致，避免使用未定义的消息类型。如需扩展新的消息类型，请先在 `src/enums/common.ts` 的 `MessageTypeEnum` 中定义，并在前后端同步实现相关处理逻辑。 