import { ComponentInstance } from './componentInstance'

/**
 * 富文本组件值类型
 * @interface RichTextComponentValue
 */
export interface RichTextComponentValue {
  /**
   * 富文本内容
   */
  html: string
}

/**
 * 富文本内容组件完整类型定义
 *
 * 将RichTextComponentValue与通用组件属性结合
 * 代表页面上实际渲染的富文本内容组件实例完整类型
 *
 * @type RichTextComponent
 */
export type RichTextComponent = ComponentInstance<RichTextComponentValue> & {
  componentName: 'RichText'
}
