import { ShowStatusEnum } from '../enums'
import { ComponentInstance } from './componentInstance'
import { LinkSelectorItem } from '@/components/SelectLink'

/**
 * 图形导航标签类型
 * @interface GraphicNavLabel
 */
export interface GraphicNavLabel {
  /**
   * 是否启用标签
   */
  control: ShowStatusEnum
  /**
   * 标签文本
   */
  text: string
  /**
   * 文本颜色
   */
  textColor: string
  /**
   * 背景颜色
   */
  bgColor: string
}

/**
 * 图形导航组件值类型
 * @interface GraphicNavComponentValue
 */
export interface GraphicNavComponentValue {
  /**
   * 导航模式
   * graphic: 图文
   * img: 图片
   * text: 文字
   */
  mode: 'graphic' | 'img' | 'text'
  /**
   * 导航类型
   * img: 图片
   * text: 文字
   */
  type: 'img' | 'text'
  /**
   * 显示样式
   * fixed: 固定
   * pageSlide: 分页滑动
   */
  showStyle: 'fixed' | 'pageSlide'
  /**
   * 每行显示数量
   * 3: 3个
   * 4: 4个
   * 5: 5个
   */
  columnCount: 3 | 4 | 5
  /**
   * 每页显示行数
   * 1: 1行
   * 2: 2行
   * 3: 3行
   * 4: 4行
   */
  rowCount: 1 | 2 | 3 | 4

  /**
   * 图片形状
   * circle: 圆形
   * rounded: 圆角
   * square: 方形
   */
  imageShape: 'circle' | 'rounded' | 'square'

  /**
   * 导航项列表
   */
  list: LinkSelectorItem[]
}

/**
 * 图形导航组件完整类型定义
 *
 * 将GraphicNavComponentValue与通用组件属性结合
 * 代表页面上实际渲染的图形导航组件实例完整类型
 *
 * @type GraphicNavComponent
 */
export type GraphicNavComponent = ComponentInstance<GraphicNavComponentValue> & {
  componentName: 'GraphicNav'
}
