import type { Property } from 'csstype'
import { Spacing, CommonStyle } from './styles'
import { ComponentInstance } from './componentInstance'
import { BackgroundSizeEnum } from '../enums'

/**
 * 导航栏配置
 * @description 定义页面顶部导航栏的样式和内容配置
 */
export interface NavBar {
  /** 是否显示(1:显示,0:隐藏) */
  isShow?: number
  /** 背景颜色 */
  bgColor?: Property.BackgroundColor
  /** 样式类型 */
  style: string
  /** 文本颜色 */
  textColor?: Property.Color
  /** 文本对齐方式 */
  textAlign?: Property.TextAlign
  /** 图片URL */
  imgUrl?: string
}

/**
 * 页面组件值类型
 * @description 定义页面组件特有的属性和配置
 *
 * 与其他组件值类型一样，PageComponentValue 仅包含页面特有的业务数据，
 * 不包含基础组件属性和公共样式属性。
 */
export interface PageComponentValue {
  /** 页面标题 */
  title: string

  /** 页面内边距 */
  padding: Spacing

  /** 背景图片URL */
  bgUrl: string

  /** 背景高度比例 */
  bgHeightScale?: number

  /** 背景尺寸类型 */
  bgSizeType: BackgroundSizeEnum

  /** 导航栏配置 */
  navBar: NavBar

  /** 全局公共样式模板，供组件继承 */
  template: CommonStyle

  /** 标记为页面组件 */
  isPageComponent: true
}

/**
 * 页面组件实例类型
 * @description 页面作为特殊组件的完整实例类型
 *
 * 页面组件是整个装修系统的根容器，具有以下特点：
 * 1. 继承基础组件属性（ID、名称等通用信息）
 * 2. 继承公共样式属性（边距、背景等样式）
 * 3. 包含页面特有的配置属性（如导航栏、背景等）
 *
 * 在装修系统中，页面组件与普通组件共享编辑机制，
 * 但具有特殊的处理逻辑和更高的层级。
 */
export type PageComponent = ComponentInstance<PageComponentValue>
