import { ComponentInstance } from './componentInstance'

/**
 * 视频组件值类型定义
 * 包含视频组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 视频类型、视频地址、海报地址、视频比例、第三方视频地址、视频列表
 *
 * @interface VideoComponentValue
 */
export interface VideoComponentValue {
  /**
   * 视频类型
   */
  videoType: string
  /**
   * 视频地址
   */
  videoUrl: string
  /**
   * 视频封面图片
   */
  posterUrl: string
  /**
   * 视频宽高比例
   */
  videoRatio: string
  /**
   * 第三方视频链接
   */
  thirdUrl: string
  /**
   * 上传视频数组
   */
  videoUrlList?: string[]
}

/**
 * 标题组件完整类型定义
 *
 * 将VideoComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type VideoComponent
 */
export type VideoComponent = ComponentInstance<VideoComponentValue> & {
  componentName: 'Video'
}
