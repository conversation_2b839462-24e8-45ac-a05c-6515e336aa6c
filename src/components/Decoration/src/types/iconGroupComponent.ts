import { StyleEnum } from '../enums'
import { ComponentInstance } from './componentInstance'
import { LinkSelectorItem } from '@/components/SelectLink'
/**
 * 图标组组件值类型定义
 * 包含图标组组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 标题文本、链接、样式选择
 *
 * @interface IconGroupComponentValue
 */
export interface IconGroupComponentValue {
  /**
   * 风格样式
   */
  style: StyleEnum.STYLE_1 | StyleEnum.STYLE_2
  /**
   * 风格名称
   */
  styleName?: string
  /**
   * 标题文本
   */
  titleText: string

  /**
   * 图标组列表
   */
  list: LinkSelectorItem[]
}

/**
 * 按钮组组件完整类型定义
 *
 * 将ButtonGroupComponentValue与通用组件属性结合
 * 代表页面上实际渲染的图标组组件实例完整类型
 *
 * @type IconGroupComponent
 */
export type IconGroupComponent = ComponentInstance<IconGroupComponentValue> & {
  componentName: 'IconGroup'
}
