import { CSSProperties } from 'vue'
import { ShowStatusEnum, StyleEnum } from '../enums'
import { ComponentInstance } from './componentInstance'
import { SelectLinkValue } from '@/components/SelectLink'

/**
 * 标题组件值类型定义
 * 包含标题组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 标题文本、链接、样式选择
 * - 副标题配置项
 * - 更多按钮配置项
 *
 * @interface TitleComponentValue
 */
export interface TitleComponentValue {
  /**
   * 风格样式
   */
  style: StyleEnum.STYLE_1 | StyleEnum.STYLE_2 | StyleEnum.STYLE_3 | StyleEnum.STYLE_4
  /**
   * 风格名称
   */
  styleName?: string
  /**
   * 标题文本
   */
  titleText: string
  /**
   * 标题链接
   */
  link?: SelectLinkValue
  /**
   * 字体颜色
   */
  textColor: CSSProperties['color']
  /**
   * 字体大小 (px)
   */
  fontSize: number

  /**
   * 文本对齐方式
   */
  textAlign: 'left' | 'center'

  /**
   * 是否显示左侧块
   */
  leftBlock: ShowStatusEnum

  /**
   * 副标题配置
   */
  subTitle: {
    /**
     * 副标题文本
     */
    text: string
    /**
     * 副标题颜色 (HEX)
     */
    color: string
    /**
     * 副标题字体大小 (px)
     */
    fontSize: number
    /**
     * 是否控制显示
     */
    control?: ShowStatusEnum
  }
  /**
   * 更多按钮配置
   */
  more: {
    /**
     * 更多按钮文本
     */
    text: string
    /**
     * 是否控制显示
     */
    control: ShowStatusEnum
    /**
     * 更多按钮链接
     */
    link?: SelectLinkValue
    /**
     * 更多按钮颜色 (HEX)
     */
    color: string
  }
}

/**
 * 标题组件完整类型定义
 *
 * 将TitleComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type TitleComponent
 */
export type TitleComponent = ComponentInstance<TitleComponentValue> & {
  componentName: 'Title'
}
