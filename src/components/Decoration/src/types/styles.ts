import type { Property } from 'csstype'

/**
 * 渐变样式配置
 */
export interface GradientStyle {
  /** 渐变起始颜色 (HEX/RGBA) */
  startColor: Property.Color
  /** 渐变结束颜色 (HEX/RGBA) */
  endColor: Property.Color
  /** 渐变角度 (单位: deg) @default 180 ，180度从上到下，90度从左到右 */
  angle: number
}
/**
 * 圆角半径配置 (单位: px)
 */
export interface CornerRadii {
  /** 左上角半径 */
  topLeft: number
  /** 右上角半径 */
  topRight: number
  /** 左下角半径 */
  bottomLeft: number
  /** 右下角半径 */
  bottomRight: number
}

/**
 * 间距配置 (单位: px)
 */
export interface Spacing {
  /** 上边距 */
  top: number
  /** 右边距 */
  right: number
  /** 下边距 */
  bottom: number
  /** 左边距 */
  left: number
}

/**
 * 背景配置
 */
export interface Background {
  /** 背景图片地址 (支持 URL/base64) */
  imageUrl?: string
  /** 背景透明度 (0-100) @default 100 */
  opacity?: number
  /** 背景渐变配置 (与图片共存时叠加显示) */
  gradient?: GradientStyle
}

/**
 * 公共样式配置
 * @description 定义组件的通用样式规范，用于统一管理设计系统的基础样式
 */
export interface CommonStyle {
  /**
   * 主要文字颜色 (HEX格式)
   * @default "#303133"
   */
  textColor?: Property.Color

  /**
   * 容器背景渐变配置
   * @description 应用于容器级的渐变效果，会覆盖纯色背景
   */
  containerGradient?: GradientStyle

  /** 容器内边距 */
  containerPadding?: Spacing

  /** 容器外边距 */
  containerMargin?: Spacing

  /** 组件背景配置 */
  background?: Background

  /** 组件圆角配置 */
  borderRadius?: CornerRadii

  /**
   * 子项通用样式
   * @description 子项通用样式，用于定义子项的通用样式
   */
  itemStyle?: {
    /** 背景颜色 (HEX/RGBA) @default "#ffffff" */
    backgroundColor?: Property.BackgroundColor
    /** 子项圆角配置 */
    borderRadius?: CornerRadii
  }
}

// 定义可忽略的项
export type ignoreField = keyof CommonStyle
