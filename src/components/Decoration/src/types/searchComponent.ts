import { StyleEnum } from '../enums'
import { ComponentInstance } from './componentInstance'

/**
 * 搜索组件值类型
 * @interface SearchComponentValue
 */
export interface SearchComponentValue {
  /**
   * 搜索样式
   * @default "style-1" 默认样式
   */
  style: StyleEnum.STYLE_1 | StyleEnum.STYLE_2 | StyleEnum.STYLE_3
  /**
   * 提示文字
   */
  placeholderText: string
  /**
   * logo/提示字
   */
  searchType: {
    logo: boolean
    title: boolean
    logoUrl: string
    searchTitle: string
  }
  /**
   * 显示时间
   */
  showTime: string
  /**
   * 热搜词
   */
  hotList: string[]
  /**
   * 搜索框背景颜色
   */
  boxColor: string
  /**
   * 提示文字颜色
   */
  placeholderColor: string
  /**
   * 热词颜色
   */
  hotWordColor: string
}

/**
 * 标题组件完整类型定义
 *
 * 将TitleComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type TitleComponent
 */
export type SearchComponent = ComponentInstance<SearchComponentValue> & {
  componentName: 'Search'
}
