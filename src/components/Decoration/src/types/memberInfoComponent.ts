import { StyleEnum } from '../enums'
import { ComponentInstance } from './componentInstance'

/**
 * 会员组件值类型定义
 * 包含会员组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 会员组件特有的业务属性和样式配置
 * - 副标题配置项
 * - 更多按钮配置项
 *
 * @interface MemberInfoComponentValue
 */
export interface MemberInfoComponentValue {
  /**
   * 风格样式
   */
  style: StyleEnum.STYLE_1 | StyleEnum.STYLE_2 | StyleEnum.STYLE_3 | StyleEnum.STYLE_4
  /**
   * 风格名称
   */
  styleName?: string

  /**
   * 会员标识符类型
   * user_id: 会员ID
   * phone: 手机号码
   */
  memberIdentifierType: 'user_id' | 'mobile'
  /**
   * 营销推广
   * level:等级
   * promotion:邀请码
   */
  memberMarketing: ('level' | 'promotion')[]
  /**
   * TODO: 资产信息
   * balance:余额
   * point:积分
   * coupon_count:优惠券
   * red_packet:红包
   * income:收益
   */
  memberAsset: ('balance' | 'point' | 'coupon_count' | 'red_packet' | 'income')[]
}

/**
 * 会员组件完整类型定义
 *
 * 将MemberInfoComponentValue与通用组件属性结合
 * 代表页面上实际渲染的会员组件实例完整类型
 *
 * @type MemberInfoComponent
 */
export type MemberInfoComponent = ComponentInstance<MemberInfoComponentValue> & {
  componentName: 'MemberInfo'
}
