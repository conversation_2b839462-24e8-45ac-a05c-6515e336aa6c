import { ComponentInstance } from './componentInstance'
import { LinkSelectorItem } from '@/components/SelectLink'
/**
 * 标题组件值类型定义
 * 包含标题组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 标题文本、链接、样式选择
 * - 副标题配置项
 * - 更多按钮配置项
 *
 * @interface NoticeComponentValue
 */
export interface NoticeComponentValue {
  /**
   * 公告类型
   */
  noticeType: string
  /**
   * 图片类型
   */
  imageUrl: string
  /**
   * 公告标题
   */
  noticeTitle: string
  /**
   * 滚动方式
   */
  scrollWay: string
  /**
   * 显示类型
   */
  showType: string
  /**
   * 字体颜色
   */
  textColor: string
  /**
   * 字体大小
   */
  fontSize: number
  /**
   * 字体粗细
   */
  fontWeight: string
  /**
   * 公告列表
   */
  list: LinkSelectorItem[]
}
/**
 * 公告项类型
 * @interface NoticeItem
 */
export interface NoticeItem {
  id: string
  text: string
  link: LinkSelectorItem[]
}

// 定义字体选项的类型
export interface FontOption {
  label: string
  value: 'normal' | 'italic' | 'bold'
}

// 定义组件验证结果的类型
export interface VerifyResult {
  code: boolean
  message: string
}

/**
 * 标题组件完整类型定义
 *
 * 将TitleComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type NoticeComponent
 */
export type NoticeComponent = ComponentInstance<NoticeComponentValue> & {
  componentName: 'Notice'
}
