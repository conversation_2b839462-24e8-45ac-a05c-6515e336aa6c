/**
 * 装修系统类型定义索引
 *
 * 文件组织结构：
 * - styles.ts: 样式相关类型定义
 * - componentInstance.ts: 组件实例基础类型定义
 * - componentLibrary.ts: 左侧组件库面板的组件类型定义
 * - pageComponent.ts: 页面组件值类型和实例类型定义
 * - businessTypes.ts: 业务数据相关类型定义
 * - 各组件值类型定义文件: *Component.ts
 */

// 导入组件值类型用于构建映射
// 基础组件
import { TitleComponent } from './titleComponent'
import { SearchComponent } from './searchComponent'
import { RubikCubeComponent } from './rubikCubeComponent'
import { HotAreaComponent } from './hotAreaComponent'
import { NoticeComponent } from './noticeComponent'
import { HorizontalLineComponent } from './horzLineComponent'
import { HorizontalBlankComponent } from './horzBlankComponent'
import { RichTextComponent } from './richTextComponent'
import { GraphicNavComponent } from './graphicNavComponent'
import { CarouselImageComponent } from './carouselImageComponent'
import { CeramicComponent } from './ceramicComponent'
import { WeChatComponent } from './weChatComponent'
import { VideoComponent } from './videoComponent'

// 商品组件
import { GoodsListComponent } from './goodsListComponent'
import { GoodsListTabComponent } from './goodsListTabComponent'

import { MemberInfoComponent } from './memberInfoComponent'
import { OrderInfoComponent } from './orderInfoComponent'
import { IconGroupComponent } from './iconGroupComponent'

import { GoodsInfoComponent } from './goodsInfoComponent'
import { GoodsEvaluateComponent } from './goodsEvaluateComponent'
import { GoodsDetailComponent } from './goodsDetailComponent'
import { GoodsMenuComponent } from './goodsMenuComponent'

// 样式相关类型
export * from './styles'

// 组件实例基础类型
export * from './componentInstance'

// 组件库面板类型
export * from './componentLibrary'

// 页面组件类型
export * from './pageComponent'

// 业务数据类型
export * from './businessTypes'

// 组件特定类型导出
export * from './titleComponent'
export * from './searchComponent'
export * from './rubikCubeComponent'
export * from './hotAreaComponent'
export * from './noticeComponent'
export * from './horzLineComponent'
export * from './horzBlankComponent'
export * from './goodsListComponent'
export * from './goodsListTabComponent'
export * from './goodsInfoComponent'
export * from './goodsEvaluateComponent'
export * from './goodsDetailComponent'
export * from './goodsMenuComponent'
export * from './richTextComponent'
export * from './graphicNavComponent'
export * from './carouselImageComponent'
export * from './ceramicComponent'
export * from './memberInfoComponent'
export * from './orderInfoComponent'
export * from './iconGroupComponent'
export * from './weChatComponent'
export * from './videoComponent'

/**
 * 页面组件映射
 * @description 将组件名称映射到对应的组件实例类型
 *
 * 这个映射提供了页面中所有可用组件的类型定义，主要用于以下场景：
 * 1. 类型检查：确保组件数据符合特定组件类型的结构要求
 * 2. 组件实例化：创建具有正确类型定义的组件实例
 * 3. 组件模板库：用于组件重置功能中的原始模板查找
 *
 * 每当添加新的组件类型时，需要在此映射中添加对应的类型定义
 */
export interface PageComponentMap {
  /** 标题组件 */
  Title?: TitleComponent
  /** 搜索组件 */
  Search?: SearchComponent
  /** 魔方组件 */
  RubikCube?: RubikCubeComponent
  /** 热区组件 */
  HotArea?: HotAreaComponent
  /** 公告组件 */
  Notice?: NoticeComponent
  /** 辅助线组件 */
  HorizontalLine?: HorizontalLineComponent
  /** 辅助空白组件 */
  HorizontalBlank: HorizontalBlankComponent
  /** 商品列表组件 */
  /** 选项卡组件 */
  GoodsTabList: GoodsListTabComponent
  /** 商品信息组件 */
  GoodsInfo: GoodsInfoComponent
  /** 商品评价组件 */
  GoodsEvaluate: GoodsEvaluateComponent
  /** 商品详情组件 */
  GoodsDetail: GoodsDetailComponent
  /** 底部菜单组件 */
  GoodsMenu: GoodsMenuComponent
  /** 富文本组件 */
  RichText?: RichTextComponent
  /** 导航组件 */
  GraphicNav?: GraphicNavComponent
  /** 轮播图组件 */
  CarouselImage?: CarouselImageComponent
  /** 瓷片组件 */
  Ceramic?: CeramicComponent
  /** 商品列表组件 */
  GoodsList?: GoodsListComponent
  /** 选项卡组件 */
  /** 会员组件 */
  MemberInfo: MemberInfoComponent
  /** 订单信息组件 */
  OrderInfo: OrderInfoComponent
  /** 图标组组件 */
  IconGroup: IconGroupComponent
  /** 关注公众号组件 */
  WeChat: WeChatComponent
  /** 视频组件 */
  Video: VideoComponent
}

/**
 * 页面组件列表类型
 * @description 表示页面中已添加的所有组件实例的数组
 * 用于对 componentsData 提供类型支持
 */
export type PageComponents = PageComponentMap[keyof PageComponentMap][]

/**
 * 装修组件联合类型
 *
 * 所有可用的装修组件类型的联合
 */
export type DecorationComponent = PageComponentMap[keyof PageComponentMap]
