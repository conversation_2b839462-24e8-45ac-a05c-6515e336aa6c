import { ComponentInstance } from './componentInstance'
import { SelectLinkValue } from '@/components/SelectLink'
/**
 * 关注公众号组件值类型定义
 * 包含标题组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 跳转设置
 * - 搜索内容配置项
 * - 扫描二维码图片配置项
 *
 * @interface WeChatComponentValue
 */
export interface WeChatComponentValue {
  /**
   * 公众号跳转设置
   */
  linkUrl: string
  /**
   * 搜索提示文字
   */
  tips: string
  /**
   * 图片二维码
   */
  imageUrl: string
  /**
   * 跳转链接
   */
  link?: SelectLinkValue
}
/**
 * 标题组件完整类型定义
 *
 * 将WeChatComponentValue与通用组件属性结合
 * 代表页面上实际渲染的关注公众号组件实例完整类型
 *
 * @type WeChatComponent
 */
export type WeChatComponent = ComponentInstance<WeChatComponentValue> & {
  componentName: 'WeChat'
}
