import { ComponentInstance } from './componentInstance'

/**
 * 热区链接类型
 * @interface HotAreaLink
 */
export interface HotAreaLink {
  /**
   * 链接名称
   */
  name: string
  title: string
  /**
   * 链接URL
   */
  url?: string
}

/**
 * 热区项类型
 * @interface HotAreaItem
 */
export interface HotAreaItem {
  /**
   * 单位
   */
  unit: string
  /**
   * 链接配置
   */
  link: HotAreaLink
  /**
   * 热区位置 - 左边距
   */
  left: number
  /**
   * 热区位置 - 上边距
   */
  top: number
  /**
   * 热区宽度
   */
  width: number
  /**
   * 热区高度
   */
  height: number
}

/**
 * 热区组件值类型
 * @interface HotAreaComponentValue
 */
export interface HotAreaComponentValue {
  /**
   * 图片URL
   */
  imageUrl: string
  /**
   * 图片宽度
   */
  imgWidth: string
  /**
   * 图片高度
   */
  imgHeight: string
  /**
   * 热区列表
   */
  heatMapData: HotAreaItem[]
}

/**
 * 热区组件完整类型定义
 *
 * 将TitleComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type TitleComponent
 */
export type HotAreaComponent = ComponentInstance<HotAreaComponentValue> & {
  componentName: 'HotArea'
}
