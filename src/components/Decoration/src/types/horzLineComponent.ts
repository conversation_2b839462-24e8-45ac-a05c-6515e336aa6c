import { ComponentInstance } from './componentInstance'

/**
 * 水平分割线组件值类型
 * @interface HorizontalLineValue
 */
export interface HorizontalLineValue {
  /**
   * 边框宽度
   */
  lineWidth: string
  /**
   * 边框颜色
   */
  lineColor: string
  /**
   * 边框样式
   */
  style: string
}

/**
 * 辅助线组件完整类型定义
 *
 * 将HorizontalLineValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type HorizontalLineComponent
 */
export type HorizontalLineComponent = ComponentInstance<HorizontalLineValue> & {
  componentName: 'HorizontalLine'
}
