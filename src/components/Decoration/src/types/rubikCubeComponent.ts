import { ComponentInstance } from './componentInstance'

/**
 * 魔方组件项类型
 * @interface RubikCubeItem
 */

export interface RubikCubeItem {
  /**
   * 魔方组件项起始坐标
   */
  start: {
    x: number
    y: number
  }
  /**
   * 魔方组件项结束坐标
   */
  end: {
    x: number
    y: number
  }

  /**
   * 魔方组件项原始数据
   */
  cubeData: {
    /**
     * 魔方组件项链接
     */
    link: {
      name: string
      title: string
      url: string
    }
    /**
     * 魔方组件项图片
     */
    imageUrl: string
    /**
     * 魔方组件项标题
     */
    title: string
  }
  /**
   * 魔方组件项样式
   */
  style: RubikCubeStyle
}

export interface RubikCubeStyle {
  top: string
  left: string
  width: string
  height: string
}

/**
 * 魔方组件值类型定义
 * 包含魔方组件特有的业务属性和样式配置
 *
 * 主要定义:
 * - 魔方组件风格
 * - 图片间隙
 * - 魔方高度
 * - 魔方项列表
 *
 * @interface RubikCubeComponentValue
 */
export interface RubikCubeComponentValue {
  /**
   * 风格
   */
  style: string
  /**
   * 图片间隙
   */
  imageGap: number
  /**
   * 魔方高度
   */
  cubeHeight: number
  /**
   * 魔方项列表
   */
  cubeListInfo: RubikCubeItem[]
}

/**
 * 魔方组件完整类型定义
 *
 * 将RubikCubeComponentValue与通用组件属性结合
 * 代表页面上实际渲染的魔方组件实例完整类型
 *
 * @type RubikCubeComponent
 */
export type RubikCubeComponent = ComponentInstance<RubikCubeComponentValue> & {
  componentName: 'RubikCube'
}
