import { ShowStatusEnum, StyleEnum } from '../enums'
import { ComponentInstance } from './componentInstance'
import { LinkSelectorItem, SelectLinkValue } from '@/components/SelectLink'

/**
 * 订单信息组件值类型定义
 * 包含订单信息组件特有的业务属性和样式配置
 * @interface OrderInfoComponentValue
 */
export interface OrderInfoComponentValue {
  /**
   * 风格样式
   */
  style: StyleEnum.STYLE_1 | StyleEnum.STYLE_2
  /**
   * 风格名称
   */
  styleName?: string
  /**
   * 标题文本
   */
  titleText: string
  /**
   * 标题链接
   */
  link: SelectLinkValue

  /**
   * 副标题配置
   */
  subTitle: {
    /**
     * 副标题文本
     */
    text: string
    /**
     * 副标题颜色 (HEX)
     */
    color: string
    /**
     * 是否控制显示
     */
    control?: ShowStatusEnum
  }
  /**
   * 订单按钮列表
   */
  list: LinkSelectorItem[]
}

/**
 * 订单信息组件完整类型定义
 *
 * 将OrderInfoComponentValue与通用组件属性结合
 * 代表页面上实际渲染的订单信息组件实例完整类型
 *
 * @type OrderInfoComponent
 */
export type OrderInfoComponent = ComponentInstance<OrderInfoComponentValue> & {
  componentName: 'OrderInfo'
}
