import { ComponentInstance } from './componentInstance'
/**
 * 水平空白组件值类型
 * @interface HorizontalBlankValue
 */
export interface HorizontalBlankValue {
  /**
   * 空白高度
   */
  blankHeight: string
  /**
   * 背景颜色
   */
  backgroundColor?: string
}

/**
 * 辅助空白组件完整类型定义
 *
 * 将HorizontalBlankComponentValue与通用组件属性结合
 * 代表页面上实际渲染的标题组件实例完整类型
 *
 * @type HorizontalBlankComponent
 */
export type HorizontalBlankComponent = ComponentInstance<HorizontalBlankValue> & {
  componentName: 'HorizontalBlank'
}
