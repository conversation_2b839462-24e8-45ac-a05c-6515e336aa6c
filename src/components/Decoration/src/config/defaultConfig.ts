import type { CommonStyle, PageComponent } from '../types'
import { BackgroundSizeEnum } from '../enums'

/**
 * 装修组件默认公共样式（每个组件默认继承）
 */
export const defaultCommonStyle: CommonStyle = {
  textColor: '#333333',
  containerGradient: {
    startColor: '',
    endColor: '',
    angle: 180,
  },
  containerMargin: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  containerPadding: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  background: {
    imageUrl: '',
    opacity: 100,
    gradient: {
      startColor: '',
      endColor: '',
      angle: 180,
    },
  },
  borderRadius: {
    topLeft: 4,
    topRight: 4,
    bottomLeft: 4,
    bottomRight: 4,
  },
  itemStyle: {
    backgroundColor: '',
    borderRadius: {
      topLeft: 2,
      topRight: 2,
      bottomLeft: 2,
      bottomRight: 2,
    },
  },
}

/**
 * 页面默认配置
 * @description 页面组件的默认配置与样式，作为装修系统根容器
 *
 * 页面组件结构分为三部分：
 * 1. 基础组件属性：提供组件身份识别与基本行为
 * 2. 公共样式属性：继承自defaultCommonStyle的通用样式
 * 3. 页面特有属性：页面组件独有的功能配置
 *
 * 该配置在装修初始化时被复制并用于实例化页面组件
 */
export const defaultPageConfig: PageComponent = {
  // 基础组件属性
  id: 'page',
  componentName: 'Page',
  componentTitle: '页面设置',
  path: 'edit-page',
  uses: 1,
  position: '',
  isHidden: false,
  ignore: [],

  // 公共样式属性（继承自defaultCommonStyle）
  ...defaultCommonStyle,

  // 页面特有属性
  title: '页面', // 页面标题
  padding: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  // 页面背景配置
  bgUrl: '', // 页面背景图片
  bgHeightScale: 100, // 页面背景高度比例，单位%，仅在自定义模式下使用
  bgSizeType: BackgroundSizeEnum.COVER, // 背景尺寸类型，默认为填充（覆盖）模式

  // 导航栏配置
  navBar: {
    isShow: 1, // 是否显示(1:显示,0:隐藏)
    bgColor: '#ffffff', // 导航栏背景颜色
    style: 'style-1', // 导航栏风格样式
    textColor: '#333333', // 导航栏文字颜色
    textAlign: 'center', // 导航栏文字对齐方式
    imgUrl: '', // 导航栏图片URL
  },

  // 全局样式模板
  // 该样式会被所有组件继承，用于统一页面风格
  template: defaultCommonStyle,

  // 标记为页面组件
  isPageComponent: true,
}

/**
 * 颜色选择器预定义颜色
 */
export const predefineColors = [
  '#00000000', // 透明
  '#F4391c',
  '#ff4500',
  '#ff8c00',
  '#FFD009',
  '#ffd700',
  '#19C650',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#FF407E',
  '#CFAF70',
  '#A253FF',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577',
]
