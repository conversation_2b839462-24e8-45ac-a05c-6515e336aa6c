import { ComponentGroup } from '@/components/Decoration'
import { StyleEnum } from '@/components/Decoration/src/enums'
import { LinkType } from '@/components/SelectLink'
/**
 * 组件库配置
 *
 * @description 此数据用于本地开发期间和备份使用，正式环境使用后端接口获取
 *
 * 如何导出？
 * 在引入的页面中使通过 console.log 打印后复制
 *
 * 注意：
 * 1. 请先定义相关组件的ts类型，再编写初始数据
 * 2. 尽量使用枚举，不要使用硬编码
 */
export const localComponentLibrary: ComponentGroup = {
  basic: {
    title: '基础组件',
    list: {
      Title: {
        title: '标题',
        icon: 'iconfont icon-biaoti',
        path: 'edit-title',
        uses: 0,
        position: '',
        template: {
          textColor: '#333333',
          containerGradient: {
            startColor: '',
            endColor: '',
            angle: 180,
          },
          containerMargin: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '#FFFFFF',
              endColor: '#FFFFFF',
              angle: 180,
            },
          },
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
        ignore: [],
        value: {
          style: StyleEnum.STYLE_1,
          styleName: '风格1',
          titleText: '标题栏',
          textColor: '#303133',
          fontSize: 16,
          textAlign: 'left',
          leftBlock: 1,
          subTitle: {
            text: '副标题',
            color: '#999999',
            fontSize: 14,
          },
          more: {
            text: '更多',
            control: 1,
            color: '#999999',
          },
        },
      },
      Search: {
        title: '搜索',
        icon: 'iconfont icon-sousuo',
        path: 'edit-search',
        uses: 0,
        position: '',
        template: {
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '#ffffff',
              endColor: '#ffffff',
              angle: 180,
            },
          },
        },
        value: {
          style: StyleEnum.STYLE_1,
          placeholderText: '',
          boxColor: '#f2f2f2',
          placeholderColor: '#C7C7C7',
          hotWordColor: '#333333',
          showTime: '',
          searchType: {
            logo: false,
            title: false,
            logoUrl: '/images/decoration/search/search-logo.png',
            searchTitle: '标题内容',
          },
          hotList: [],
        },
      },
      HorizontalLine: {
        title: '辅助线',
        icon: 'iconfont icon-fuzhuxian',
        path: 'edit-horizon-line',
        uses: 0,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 10,
            bottom: 0,
            left: 10,
          },
        },
        value: {
          style: 'solid',
          lineColor: '#333333',
          lineWidth: '1',
        },
      },
      HorizontalBlank: {
        title: '辅助空白',
        icon: 'iconfont icon-fuzhukongbai',
        path: 'edit-horizon-blank',
        uses: 0,
        position: '',
        value: {
          blankHeight: '20',
        },
      },
      HotArea: {
        title: '热区',
        icon: 'iconfont icon-requ',
        path: 'edit-hot-area',
        uses: 0,
        position: '',
        value: {
          imageUrl: '',
          imgWidth: '',
          imgHeight: '',
          heatMapData: [],
        },
      },
      // 轮播图片
      CarouselImage: {
        title: '轮播图片',
        icon: 'iconfont icon-lunbotupian',
        path: 'edit-carousel-image',
        uses: 0,
        position: '',
        value: {
          imageRadiusTopLeft: 10,
          imageRadiusBottomLeft: 10,
          intervalNum: 3,
          indicatorPosition: 'center',
          indicatorStyle: 'circle',
          indicatorSelectedColor: '#E93323',
          conventionColor: '#D7D7D7',
          list: [
            {
              id: '1',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              imgWidth: 0,
              imgHeight: 0,
            },
            {
              id: '2',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              imgWidth: 0,
              imgHeight: 0,
            },
          ],
        },
        template: {
          textColor: '#303133',
          containerGradient: {
            startColor: '',
            endColor: '',
            angle: 180,
          },
          containerMargin: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
      },
      GraphicNav: {
        title: '导航组',
        icon: 'iconfont icon-anniuzu',
        path: 'edit-graphic-nav',
        uses: 0,
        position: '',
        template: {
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '#FFFFFF',
              endColor: '#FFFFFF',
              angle: 180,
            },
          },
        },
        value: {
          mode: 'graphic',
          type: 'img',
          showStyle: 'fixed',
          columnCount: 4,
          rowCount: 1,
          imageShape: 'rounded',
          list: [
            {
              title: '',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
          ],
        },
      },
      RubikCube: {
        title: '魔方',
        icon: 'iconfont icon-rongqi',
        path: 'edit-rubik-cube',
        uses: 0,
        position: '',
        template: {
          containerPadding: {
            top: 12,
            right: 0,
            bottom: 12,
            left: 0,
          },
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
        },
        value: {
          style: 'style-10',
          imageGap: 12,
          cubeHeight: 750,
          cubeListInfo: [],
        },
      },
      Notice: {
        title: '公告',
        icon: 'iconfont icon-gonggao',
        path: 'edit-notice',
        position: '',
        uses: 0,
        template: {
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
        value: {
          textColor: '#303133',
          noticeType: 'img',
          imageUrl: '',
          showType: 'show',
          scrollWay: 'horizontal',
          fontSize: 14,
          fontWeight: 'normal',
          noticeTitle: '公告',
          list: [
            {
              title: '公告内容',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#F83287',
              },
            },
          ],
        },
      },
      Ceramic: {
        title: '瓷片',
        icon: 'iconfont icon-cipianqu',
        path: 'edit-ceramic',
        uses: 0,
        position: '',
        template: {
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
          containerPadding: {
            top: 12,
            right: 12,
            bottom: 12,
            left: 12,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
        value: {
          navMode: 'style-1',
          show: 'styleShow-1',
          imgType: 'square',
          imageUrl: '',
          systemUrl: '',
          showType: 'show',
          titleColor: '#333333',
          subTitleColor: '#999999',
          indicatorColor: 'black',
          imgList: [
            {
              style: 'square',
            },
            {
              style: 'square-radius',
            },
            {
              style: 'circle',
            },
          ],
          list: [
            {
              title: '按钮文字',
              buttonText: '按钮文字',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              tips: '提示文字',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#F83287',
              },
            },
            {
              title: '按钮文字',
              buttonText: '按钮文字',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              tips: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#F83287',
              },
            },
            {
              title: '',
              buttonText: '按钮文字',
              link: {
                key: '',
                title: '',
                url: '',
                type: LinkType.DECORATED_PAGE,
              },
              imageUrl: '',
              tips: '提示文字',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#F83287',
              },
            },
          ],
        },
      },
      RichText: {
        title: '富文本',
        icon: 'iconfont icon-fuwenben',
        path: 'edit-rich-text',
        position: '',
        uses: 0,
        value: {
          html: '',
        },
      },
      WeChat: {
        title: '关注公众号',
        icon: 'iconfont icon-guanzhugongzhonghao',
        path: 'edit-wechat',
        uses: 0,
        position: '',
        template: {
          borderRadius: {
            topLeft: 6,
            topRight: 6,
            bottomLeft: 6,
            bottomRight: 6,
          },
          containerMargin: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          containerPadding: {
            top: 12,
            right: 12,
            bottom: 12,
            left: 12,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
        value: {
          linkUrl: 'linkUrl',
          tips: '关注公众号，可第一时间收到最新活动和订单通知等消息推送',
          imageUrl: '',
          link: {
            key: '',
            url: '',
            title: '',
            type: LinkType.DECORATED_PAGE,
          },
        },
      },
      Video: {
        title: '视频',
        icon: 'iconfont icon-shipin',
        path: 'edit-video',
        uses: 0,
        position: '',
        value: {
          videoType: 'upFile',
          videoUrl: '',
          posterUrl: '',
          videoRatio: '16:9',
          thirdUrl: '',
        },
        template: {
          borderRadius: {
            topLeft: 0,
            topRight: 0,
            bottomLeft: 0,
            bottomRight: 0,
          },
          itemStyle: {
            backgroundColor: '',
            borderRadius: {
              topLeft: 0,
              topRight: 0,
              bottomLeft: 0,
              bottomRight: 0,
            },
          },
        },
      },
    },
  },
  goods: {
    title: '商品组件',
    list: {
      GoodsList: {
        title: '商品列表',
        icon: 'iconfont icon-shangpinliebiao',
        path: 'edit-goods-list',
        uses: 0,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          style: 0,
          checkType: 'isGoods',
          goodIds: [],
          cateIds: [],
          goodsNum: 10,
          goodsSort: 'sortAll',
          showInfo: [
            'goodsName',
            'goodsSubName',
            'goodsPrice',
            'originalPrice',
            'goodsSale',
            'userPrice',
          ],
          isShowCar: 1,
          carBtnStyle: 1,
          carBtnText: '购买',
          carBtnEffect: 1,
          subscriptStyle: 1,
          subBtnStyle: 1,
          subBtnText: '热卖',
          subBtnImg: '',
          textColor: '#333333',
          goodsNameStyle: 'bold',
          goodsNameColor: '#333333',
          imgBuildRadiusTop: 5,
          imgBuildRadiusBottom: 5,
          carBtnTextColor: '#ffffff',
          carBtnTBuildRadius: 20,
          shoppingCarStyle: {
            startColor: '#EB3534',
            endColor: '#EB3534',
            angle: 180,
          },
        },
      },
      Tab: {
        title: '选项卡',
        icon: 'iconfont icon-xuanxiangka',
        path: 'edit-goods-tab',
        uses: 0,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          tabStyle: 1,
          style: 0,
          tabList: [
            {
              title: '推荐',
              subTitle: '猜你喜欢',
              checkType: 0,
              goodIds: [],
              cateIds: [],
              goodsNum: 10,
              goodsSort: 'sortAll',
            },
          ],
          showInfo: [
            'goodsName',
            'goodsSubName',
            'goodsPrice',
            'originalPrice',
            'goodsSale',
            'userPrice',
          ],
          isShowCar: 1,
          carBtnStyle: 1,
          carBtnText: '购买',
          carBtnEffect: 1,
          subscriptStyle: 1,
          subBtnStyle: 1,
          subBtnText: '热卖',
          subBtnImg: '',
          textColor: '#333333',
          tabBackground: '#ffffff',
          tabTitleUnCheckColor: '#999999',
          tabTitleCheckColor: '#EB3534',
          isTabLine: 1,
          isTabSubTitle: 0,
          subTitleColor: '#ffffff',
          unSubTitleColor: '#999999',
          boxColor: '#EB3534',
          goodsNameStyle: 'bold',
          goodsNameColor: '#333333',
          imgBuildRadiusTop: 5,
          imgBuildRadiusBottom: 5,
          carBtnTextColor: '#ffffff',
          carBtnTBuildRadius: 20,
          shoppingCarStyle: {
            startColor: '#EB3534',
            endColor: '#EB3534',
            angle: 180,
          },
        },
      },
    },
  },
  goodsDetail: {
    title: '商品详情',
    list: {
      GoodsInfo: {
        title: '商品信息',
        icon: 'iconfont icon-shangpinliebiao',
        path: 'edit-goods-info',
        uses: 1,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          showInfo: [
            'goodsInfoPrice',
            'goodsInfoCollect',
            'goodsInfoShare',
            'goodsInfoTitle',
            'goodsInfoSubTitle',
            'goodsInfoSubSale',
            'goodsInfoVolume',
            'goodsInfoUsePrice',
            'pointsGiveaway',
            'pointsDeduction',
            'goodsInfoService',
          ],
        },
      },
      GoodsEvaluate: {
        title: '商品评价',
        icon: 'iconfont icon-xuanxiangka',
        path: 'edit-goods-evaluate',
        uses: 1,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          isShowEvaluate: 1,
          isShowNum: 1,
        },
      },
      GoodsDetail: {
        title: '商品详情',
        icon: 'iconfont icon-xuanxiangka',
        path: 'edit-goods-detail',
        uses: 1,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          detailTop: 0,
          detailBottom: 0,
          detailLeftOrRight: 0,
        },
      },
      GoodsMenu: {
        title: '底部菜单',
        icon: 'iconfont icon-xuanxiangka',
        path: 'edit-goods-menu',
        uses: 1,
        position: '',
        template: {
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
        },
        value: {
          menuInfo: ['home', 'customer', 'shoppingCar'],
          homeText: '首页',
          showBuyBtn: 1,
        },
      },
    },
  },
  member: {
    title: '会员组件',
    list: {
      MemberInfo: {
        title: '会员信息',
        icon: 'iconfont icon-huiyuanxinxi',
        path: 'edit-member-info',
        uses: 0,
        position: '',
        value: {
          style: StyleEnum.STYLE_2,
          styleName: '风格2',
          memberIdentifierType: 'user_id',
          memberMarketing: ['level', 'promotion'],
          memberAsset: ['balance', 'coupon_count', 'red_packet', 'point'],
        },
      },
      OrderInfo: {
        title: '订单信息',
        icon: 'iconfont icon-dingdanxinxi',
        path: 'edit-order-info',
        uses: 0,
        position: '',
        template: {
          textColor: '#FFFFFF',
          containerGradient: {
            startColor: '',
            endColor: '',
            angle: 180,
          },
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 10,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '#FFFFFF',
              endColor: '#FFFFFF',
              angle: 180,
            },
          },
          borderRadius: {
            topLeft: 10,
            topRight: 10,
            bottomLeft: 0,
            bottomRight: 0,
          },
        },
        ignore: [],
        value: {
          style: StyleEnum.STYLE_1,
          styleName: '风格1',
          titleText: '我的订单',
          link: {
            key: 'mall_order',
            title: '我的订单',
            type: LinkType.MALL_PAGE,
            url: '',
            pageId: '',
            appid: '',
            page: '',
            mobile: '',
          },
          subTitle: {
            text: '全部订单',
            control: 1,
            color: '#999999',
          },
          list: [
            {
              title: '待付款',
              link: {
                key: 'mall_order_payment',
                title: '待付款',
                type: LinkType.MALL_PAGE,
                url: '',
                pageId: '',
                appid: '',
                page: '',
                mobile: '',
              },
              imageUrl: '/images/decoration/member/unpaid-order.png',
              label: {
                control: true,
                text: '0',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '待发货',
              link: {
                key: 'mall_order_shipment',
                title: '待发货',
                type: LinkType.MALL_PAGE,
                url: '',
                pageId: '',
                appid: '',
                page: '',
                mobile: '',
              },
              imageUrl: '/images/decoration/member/to-ship-order.png',
              label: {
                control: true,
                text: '1',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '待收货',
              link: {
                key: 'mall_order_receipt',
                title: '待收货',
                type: LinkType.MALL_PAGE,
                url: '',
                pageId: '',
                appid: '',
                page: '',
                mobile: '',
              },
              imageUrl: '/images/decoration/member/to-receive-orders.png',
              label: {
                control: true,
                text: '0',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '待评价',
              link: {
                key: 'mall_order_review',
                title: '待评价',
                type: LinkType.MALL_PAGE,
                url: '',
                pageId: '',
                appid: '',
                page: '',
                mobile: '',
              },
              imageUrl: '/images/decoration/member/to-rate-orders.png',
              label: {
                control: true,
                text: '0',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '退换货',
              link: {
                key: 'mall_order_sales-service',
                title: '退换货',
                type: LinkType.MALL_PAGE,
                url: '',
                pageId: '',
                appid: '',
                page: '',
                mobile: '',
              },
              imageUrl: '/images/decoration/member/return-exchange.png',
              label: {
                control: true,
                text: '0',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
          ],
        },
      },
      IconGroup: {
        title: '图标组',
        icon: 'iconfont icon-tubiaozu',
        path: 'edit-icon-group',
        uses: 0,
        position: '',
        template: {
          textColor: '#FFFFFF',
          containerGradient: {
            startColor: '',
            endColor: '',
            angle: 180,
          },
          containerPadding: {
            top: 0,
            right: 0,
            bottom: 10,
            left: 0,
          },
          background: {
            imageUrl: '',
            opacity: 100,
            gradient: {
              startColor: '',
              endColor: '',
              angle: 180,
            },
          },
          borderRadius: {
            topLeft: 10,
            topRight: 10,
            bottomLeft: 10,
            bottomRight: 10,
          },
        },
        ignore: [],
        value: {
          style: StyleEnum.STYLE_1,
          styleName: '风格1',
          titleText: '常用功能',

          list: [
            {
              title: '',
              link: {
                key: '',
                type: LinkType.MALL_PAGE,
                title: '',
                url: '',
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                type: LinkType.MALL_PAGE,
                title: '',
                url: '',
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                type: LinkType.MALL_PAGE,
                title: '',
                url: '',
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
            {
              title: '',
              link: {
                key: '',
                type: LinkType.MALL_PAGE,
                title: '',
                url: '',
              },
              imageUrl: '',
              label: {
                control: false,
                text: '热门',
                textColor: '#FFFFFF',
                bgColor: '#EB3534',
              },
            },
          ],
        },
      },
    },
  },
}
