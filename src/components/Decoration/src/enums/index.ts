/**
 * 文本对齐方式枚举
 */
export enum TextAlignEnum {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
}

/**
 * 字体粗细枚举
 */
export enum FontWeightEnum {
  NORMAL = 'normal',
  BOLD = 'bold',
}

/**
 * 显示状态枚举
 */
export enum ShowStatusEnum {
  SHOW = 1,
  HIDE = 0,
}

/**
 * 风格枚举
 */
export enum StyleEnum {
  STYLE_1 = 'style-1',
  STYLE_2 = 'style-2',
  STYLE_3 = 'style-3',
  STYLE_4 = 'style-4',
  STYLE_5 = 'style-5',
}

/**
 * 商品风格枚举
 */
export enum GoodsStyleEnum {
  STYLE_1 = 0,
  STYLE_2 = 1,
  STYLE_3 = 2,
  STYLE_4 = 3,
  STYLE_5 = 4,
  STYLE_6 = 5,
}

/**
 * 选项卡风格枚举
 */
export enum TabStyleEnum {
  STYLE_1 = 1,
  STYLE_2 = 2,
  STYLE_3 = 3,
  STYLE_4 = 4,
}

/**
 * 显示评论枚举
 */
export enum showEvaluateNum {
  ONE = 1,
  TWO = 2,
  THREE = 3,
}

/**
 * 背景尺寸类型枚举
 * @description 定义页面背景图片的尺寸展示方式
 */
export enum BackgroundSizeEnum {
  /** 填充（覆盖）模式 */
  COVER = 'cover',
  /** 适应（包含）模式 */
  CONTAIN = 'contain',
  /** 自定义模式 */
  CUSTOM = 'custom',
}
