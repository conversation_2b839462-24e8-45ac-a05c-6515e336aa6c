<template>
  <!-- 渐变颜色选择器组件 -->
  <div class="gradient-color-picker">
    <!-- 渐变预览区域 -->
    <div v-if="showPreview" class="gradient-preview-container">
      <div
        class="gradient-preview"
        :style="{
          background: `linear-gradient(${angle}deg, ${startColor}, ${endColor})`,
        }"
      ></div>
    </div>
    <!-- 渐变颜色控制区域 -->
    <div class="gradient-controls">
      <!-- 颜色选择器列 -->
      <div class="color-pickers-column">
        <div class="color-block-container">
          <color-block
            :value="startColor"
            @update:value="updateColor('start', $event)"
            v-bind="$attrs"
          />
        </div>
        <div class="connector-line"></div>
        <div class="color-block-container">
          <color-block
            :value="endColor"
            @update:value="updateColor('end', $event)"
            v-bind="$attrs"
          />
        </div>
      </div>
      <!-- 颜色输入列 -->
      <div class="color-inputs-column">
        <div class="color-input-row">
          <n-input
            v-model:value="colorInputs.start"
            :placeholder="placeholder"
            :disabled="disabled"
            @blur="handleColorBlur('start')"
          />
          <n-button
            class="reset-btn"
            text
            type="primary"
            :disabled="disabled"
            @click="resetColor('start')"
          >
            {{ t('common.resetText') }}
          </n-button>
        </div>
        <div class="color-input-row">
          <n-input
            v-model:value="colorInputs.end"
            :placeholder="placeholder"
            :disabled="disabled"
            @blur="handleColorBlur('end')"
          />
          <n-button
            class="reset-btn"
            text
            type="primary"
            :disabled="disabled"
            @click="resetColor('end')"
          >
            {{ t('common.resetText') }}
          </n-button>
        </div>
      </div>
    </div>
    <!-- 角度控制区域 -->
    <div v-if="showAngleControl" class="angle-control">
      <!-- 渐变方向选择 -->
      <div v-if="controlType === 'direction'" class="direction-selector">
        <n-radio-group v-model:value="angle">
          <n-radio-button :value="180" label="上下渐变" />
          <n-radio-button :value="90" label="左右渐变" />
        </n-radio-group>
      </div>

      <!-- 自定义角度控制 -->
      <div v-if="controlType === 'angle'" class="custom-angle-control">
        <slider-input
          v-model:value="angle"
          :min="0"
          :max="359"
          :step="1"
          :default-value="defaultAngle"
        >
          <template #suffix>°</template>
        </slider-input>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, watch, reactive } from 'vue'
  import { NInput, NButton, NRadioGroup, NRadioButton } from 'naive-ui'
  import ColorBlock from './ColorBlock.vue'
  import SliderInput from '@/components/SliderInput'
  import { useI18n } from '@/hooks/web/useI18n'
  import { GradientStyle } from './type'

  const { t } = useI18n()

  /**
   * 组件属性定义
   * @property {string} startColor - 渐变起始颜色，支持十六进制格式（#RGB, #RGBA, #RRGGBB, #RRGGBBAA）
   * @property {string} endColor - 渐变结束颜色，支持十六进制格式（#RGB, #RGBA, #RRGGBB, #RRGGBBAA）
   * @property {string} defaultStartColor - 默认起始颜色，用于重置操作
   * @property {string} defaultEndColor - 默认结束颜色，用于重置操作
   * @property {number} angle - 渐变角度（0-359度）
   * @property {number} defaultAngle - 默认渐变角度，用于重置操作
   * @property {boolean} showPreview - 是否显示渐变预览
   * @property {boolean} showAngleControl - 是否显示角度控制
   * @property {boolean} disabled - 是否禁用组件
   * @property {string} placeholder - 输入框占位符
   * @property {'direction'|'angle'} controlType - 角度控制类型，direction为方向选择，angle为自定义角度
   */
  const props = withDefaults(
    defineProps<{
      startColor?: string
      endColor?: string
      defaultStartColor?: string
      defaultEndColor?: string
      angle?: number
      defaultAngle?: number
      showPreview?: boolean
      showAngleControl?: boolean
      disabled?: boolean
      placeholder?: string
      controlType?: 'direction' | 'angle'
    }>(),
    {
      startColor: '',
      endColor: '',
      defaultStartColor: '',
      defaultEndColor: '',
      angle: 180,
      defaultAngle: 180,
      showPreview: false,
      showAngleControl: false,
      disabled: false,
      placeholder: '',
      controlType: 'direction',
    },
  )

  /**
   * 组件事件
   * @event update:startColor - 更新起始颜色事件
   * @event update:endColor - 更新结束颜色事件
   * @event update:angle - 更新角度事件
   * @event change - 渐变样式变化事件
   */
  const emit = defineEmits<{
    /**
     * 更新起始颜色事件
     * @param value 新的起始颜色值
     */
    (e: 'update:startColor', value: string): void
    /**
     * 更新结束颜色事件
     * @param value 新的结束颜色值
     */
    (e: 'update:endColor', value: string): void
    /**
     * 更新角度事件
     * @param value 新的角度值
     */
    (e: 'update:angle', value: number): void
    /**
     * 渐变样式变化事件
     * @param value 渐变样式对象
     */
    (e: 'change', value: GradientStyle): void
  }>()

  // 当前颜色状态
  const startColor = ref(props.startColor)
  const endColor = ref(props.endColor)

  // 有效的颜色值缓存
  const validColors = reactive({
    start: isHexColor(props.startColor) ? props.startColor : props.defaultStartColor,
    end: isHexColor(props.endColor) ? props.endColor : props.defaultEndColor,
  })

  // 输入框显示的颜色值
  const colorInputs = reactive({
    start: props.startColor,
    end: props.endColor,
  })

  /**
   * 计算属性：渐变角度
   * 用于双向绑定角度值
   */
  const angle = computed({
    get: () => props.angle,
    set: (value) => emit('update:angle', value),
  })

  /**
   * 验证输入的颜色值是否为有效的十六进制格式
   * @param {string} color - 需要验证的颜色代码
   * @returns {boolean} 如果是有效的十六进制颜色值则返回true，否则返回false
   * @description 支持的格式: #RGB, #RGBA, #RRGGBB, #RRGGBBAA
   */
  function isHexColor(color: string): boolean {
    if (!color) return true
    return /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/.test(color)
  }

  /**
   * 更新颜色
   * @param {string} type - 颜色类型（'start'或'end'）
   * @param {string} value - 新的颜色值
   */
  function updateColor(type: 'start' | 'end', value: string) {
    if (!isHexColor(value)) return

    if (type === 'start') {
      startColor.value = value
      validColors.start = value
      colorInputs.start = value
      emit('update:startColor', value)
    } else {
      endColor.value = value
      validColors.end = value
      colorInputs.end = value
      emit('update:endColor', value)
    }

    emitChange()
  }

  /**
   * 处理颜色输入框失焦事件
   * @param {string} type - 颜色类型（'start'或'end'）
   */
  function handleColorBlur(type: 'start' | 'end') {
    const value = type === 'start' ? colorInputs.start : colorInputs.end

    if (isHexColor(value)) {
      updateColor(type, value)
    } else {
      // 恢复为有效值
      if (type === 'start') {
        colorInputs.start = validColors.start
      } else {
        colorInputs.end = validColors.end
      }
    }
  }

  /**
   * 重置颜色到默认值
   * @param {string} type - 颜色类型（'start'或'end'）
   */
  function resetColor(type: 'start' | 'end') {
    const defaultValue = type === 'start' ? props.defaultStartColor : props.defaultEndColor
    updateColor(type, defaultValue)
  }

  /**
   * 发送渐变样式变化事件
   * 将当前的起始颜色、结束颜色和角度组合成渐变样式对象发送给父组件
   */
  function emitChange() {
    emit('change', {
      startColor: startColor.value,
      endColor: endColor.value,
      angle: angle.value,
    })
  }

  // 监听输入框颜色变化
  watch(
    () => colorInputs.start,
    (val) => {
      if (isHexColor(val)) {
        updateColor('start', val)
      }
    },
  )

  watch(
    () => colorInputs.end,
    (val) => {
      if (isHexColor(val)) {
        updateColor('end', val)
      }
    },
  )

  // 监听props颜色变化
  watch(
    () => [props.startColor, props.endColor],
    ([newStartColor, newEndColor]) => {
      // 处理起始颜色变化
      if (newStartColor !== startColor.value) {
        startColor.value = newStartColor
        colorInputs.start = newStartColor
        if (isHexColor(newStartColor)) {
          validColors.start = newStartColor
        }
      }

      // 处理结束颜色变化
      if (newEndColor !== endColor.value) {
        endColor.value = newEndColor
        colorInputs.end = newEndColor
        if (isHexColor(newEndColor)) {
          validColors.end = newEndColor
        }
      }
    },
  )

  // 监听默认颜色变化
  watch(
    () => [props.defaultStartColor, props.defaultEndColor],
    ([newDefaultStart, newDefaultEnd]) => {
      // 如果当前颜色等于旧的默认颜色，则更新为新的默认颜色
      if (startColor.value === props.defaultStartColor) {
        updateColor('start', newDefaultStart)
      }

      if (endColor.value === props.defaultEndColor) {
        updateColor('end', newDefaultEnd)
      }
    },
  )

  // 监听角度变化
  watch(angle, () => emitChange())
</script>

<style lang="scss" scoped>
  /* 渐变颜色选择器容器 */
  .gradient-color-picker {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  /* 渐变预览容器 */
  .gradient-preview-container {
    width: 100%;
    height: 40px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  /* 渐变预览区域 */
  .gradient-preview {
    width: 100%;
    height: 100%;
  }

  /* 渐变控制区域 */
  .gradient-controls {
    display: flex;
    gap: 12px;
    width: 100%;
  }

  /* 颜色选择器列 */
  .color-pickers-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 颜色块容器 */
  .color-block-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2px 0;
  }

  /* 连接线 */
  .connector-line {
    width: 2px;
    height: 8px;
    background-color: #dcdfe6;
  }

  /* 颜色输入列 */
  .color-inputs-column {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
  }

  /* 颜色输入行 */
  .color-input-row {
    display: flex;
    gap: 8px;
    align-items: center;
    height: 34px;
  }

  /* 角度控制区域 */
  .angle-control {
    display: flex;
    gap: 12px;
    align-items: center;
    width: 100%;
  }

  /* 角度标签 */
  .angle-label {
    font-size: 14px;
    color: #606266;
  }

  /* 方向选择器 */
  .direction-selector {
    display: flex;
  }

  /* 自定义角度控制 */
  .custom-angle-control {
    width: 100%;
  }

  /* 重置按钮 */
  .reset-btn {
    white-space: nowrap;
  }
</style>
