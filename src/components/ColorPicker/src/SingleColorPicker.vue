<template>
  <!-- 单色选择器组件 -->
  <div class="single-color-picker">
    <div class="color-block-container">
      <!-- 颜色块组件，实现颜色可视化选择 -->
      <color-block v-model:value="colorValue" v-bind="$attrs" />
    </div>
    <div class="color-input-container">
      <!-- 颜色值输入框，支持手动输入十六进制颜色值 -->
      <n-input
        v-model:value="inputValue"
        :placeholder="placeholder"
        :disabled="disabled"
        @blur="handleBlur"
      />
      <!-- 重置按钮，将颜色恢复为默认值 -->
      <n-button class="reset-btn" text type="primary" :disabled="disabled" @click="handleReset">
        {{ t('common.resetText') }}
      </n-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue'
  import { NInput, NButton } from 'naive-ui'
  import ColorBlock from './ColorBlock.vue'
  import { useI18n } from '@/hooks/web/useI18n'

  const { t } = useI18n()

  /**
   * 组件属性定义
   * @property {string} value - 当前颜色值，支持十六进制格式（#RGB, #RGBA, #RRGGBB, #RRGGBBAA）
   * @property {string} defaultValue - 默认颜色值，用于重置操作
   * @property {string} placeholder - 输入框占位符
   * @property {boolean} disabled - 是否禁用组件
   */
  const props = withDefaults(
    defineProps<{
      value?: string
      defaultValue?: string
      placeholder?: string
      disabled?: boolean
    }>(),
    {
      value: '',
      defaultValue: '',
      placeholder: '',
      disabled: false,
    },
  )

  /**
   * 组件事件
   * @event update:value - 更新颜色值事件
   * @event change - 颜色变化事件
   */
  const emit = defineEmits<{
    /**
     * 更新颜色值事件
     * @param value 新的颜色值
     */
    (e: 'update:value', value: string): void
    /**
     * 颜色变化事件
     * @param value 新的颜色值
     */
    (e: 'change', value: string): void
  }>()

  // 输入框的颜色值
  const inputValue = ref(props.value)
  // 颜色块的颜色值
  const colorValue = ref(props.value)

  /**
   * 监听父组件传入的value属性变化
   * 当父组件的value变化时，同步更新本地的inputValue和colorValue
   */
  watch(
    () => props.value,
    (val) => {
      if (val !== inputValue.value) {
        inputValue.value = val
        colorValue.value = val
      }
    },
  )

  /**
   * 监听输入框的值变化
   * 当输入框的值是有效的十六进制颜色时，更新颜色块的值并触发事件
   */
  watch(inputValue, (val, oldVal) => {
    if (val !== oldVal) {
      if (isHexColor(val)) {
        colorValue.value = val
        emit('update:value', val)
        emit('change', val)
      }
    }
  })

  /**
   * 监听颜色块的值变化
   * 当颜色块的值变化时，更新输入框的值并触发事件
   */
  watch(colorValue, (val, oldVal) => {
    if (val !== oldVal && val !== inputValue.value) {
      inputValue.value = val
      emit('update:value', val)
      emit('change', val)
    }
  })

  /**
   * 处理输入框失焦事件
   * 当输入的值不是有效的十六进制颜色时，恢复为当前有效的颜色值
   */
  const handleBlur = () => {
    if (!isHexColor(inputValue.value)) {
      inputValue.value = colorValue.value
    }
  }

  /**
   * 重置颜色值到默认值
   * 点击重置按钮时调用，将颜色恢复为defaultValue属性指定的值
   */
  const handleReset = () => {
    if (props.defaultValue !== inputValue.value) {
      inputValue.value = props.defaultValue
      colorValue.value = props.defaultValue
    }
  }

  /**
   * 验证输入的颜色值是否为有效的十六进制格式
   * @param {string} code - 需要验证的颜色代码
   * @returns {boolean} 如果是有效的十六进制颜色值则返回true，否则返回false
   * @description 支持的格式: #RGB, #RGBA, #RRGGBB, #RRGGBBAA
   */
  function isHexColor(code: string): boolean {
    return /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/.test(code)
  }
</script>

<style lang="scss" scoped>
  /* 单色选择器容器 */
  .single-color-picker {
    display: flex;
    gap: 12px;
    align-items: center;
    width: 100%;
  }

  /* 颜色块容器样式 */
  .color-block-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 颜色输入框容器样式 */
  .color-input-container {
    display: flex;
    flex: 1;
    gap: 8px;
    align-items: center;
  }

  /* 重置按钮样式 */
  .reset-btn {
    white-space: nowrap;
  }
</style>
