<template>
  <!-- 颜色块选择器组件，基于naive-ui的颜色选择器封装 -->
  <n-color-picker
    class="color-preview"
    v-model:value="valueProxy"
    v-bind="$attrs"
    :modes="['hex']"
    :show-alpha="false"
    :swatches="predefineColors"
  >
    <!-- 转发所有插槽 -->
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}"></slot>
    </template>
  </n-color-picker>
</template>

<script lang="ts" setup>
  import { computed, PropType } from 'vue'
  import { NColorPicker } from 'naive-ui'
  import { predefineColors } from '@/components/Decoration/src/config/defaultConfig'

  /**
   * 组件属性定义
   */
  const props = defineProps({
    /**
     * 当前颜色值（十六进制格式）
     * 可以为空，代表透明色
     * @default ''
     */
    value: {
      type: String as PropType<string>,
      default: '',
      validator: (value: string) => {
        // 验证是否是有效的十六进制颜色值或为空
        return (
          value === '' ||
          /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/.test(value)
        )
      },
    },
  })

  /**
   * 组件事件
   * @event update:value - 颜色值更新事件
   */
  const emit = defineEmits<{
    /**
     * 颜色值更新事件
     * @param value 新的颜色值
     */
    (e: 'update:value', value: string): void
  }>()

  /**
   * 计算属性：颜色值代理
   * 用于双向绑定，当值变化时触发update:value事件
   */
  const valueProxy = computed({
    get: () => props.value,
    set: (value: string) => emit('update:value', value),
  })
</script>

<style lang="scss" scoped>
  /**
   * 颜色预览样式
   * 定义颜色选择器触发器的尺寸和行为
   */
  .color-preview {
    flex-shrink: 0;
    width: 32px;
    height: 32px;

    /**
     * 隐藏颜色值文本显示
     * 使用:deep选择器修改组件内部样式
     */
    :deep(.n-color-picker-trigger__value) {
      display: none;
    }
  }
</style>
