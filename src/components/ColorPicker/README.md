# 颜色选择器组件库

这是一个基于Vue 3和Naive UI的颜色选择器组件库，提供了多种颜色选择器组件，方便在项目中使用。

## 组件列表

1. **ColorBlock** - 基础颜色块选择器
2. **SingleColorPicker** - 单色选择器（带输入框和重置按钮）
3. **GradientColorPicker** - 渐变色选择器（带连接线、输入框、角度控制和重置按钮）

## 特性

1. **组件封装** - 基于Naive UI的颜色选择器组件进行封装，提供更便捷的使用方式
2. **透明度支持** - 所有组件均支持透明度选择
3. **预设颜色** - 支持自定义预设颜色列表
4. **渐变控制** - 渐变色选择器支持角度控制和预览
5. **灵活配置** - 支持显示/隐藏预览和角度控制
6. **属性透传** - 支持将Naive UI原生颜色选择器的属性直接透传到组件中，无需重复定义
7. **国际化支持** - 支持多语言配置，使用i18n实现

## 属性透传

所有组件都支持Naive UI的`n-color-picker`组件的原生属性，如`show-alpha`、`swatches`、`actions`、`modes`、`placement`、`to`等，无需在组件中重复定义这些属性，直接传递即可。

例如：

```vue
<color-block 
  v-model="color" 
  :show-alpha="true"
  :swatches="['#ff0000', '#00ff00', '#0000ff']"
  :actions="['confirm']"
  :modes="['rgb', 'hex']"
  placement="bottom-start"
  :to="false"
/>
```

## 安装

### 全局注册

在`main.ts`中全局注册组件：

```typescript
import { createApp } from 'vue'
import App from './App.vue'
import { setupColorPicker } from '@/components/ColorPicker'

const app = createApp(App)
setupColorPicker(app)
app.mount('#app')
```

### 按需引入

在需要使用的组件中引入：

```typescript
import { ColorBlock, SingleColorPicker, GradientColorPicker } from '@/components/ColorPicker'
```

## API

### ColorBlock

基础颜色块选择器组件。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| value | string | '' | 颜色值，支持v-model |

此外，组件支持所有Naive UI的n-color-picker组件的原生属性，如disabled、show-alpha、swatches等，可直接传入。

#### 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| update:value | string | 颜色值变化时触发 |

### SingleColorPicker

单色选择器组件，包含颜色块、输入框和重置按钮。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| value | string | '' | 颜色值，支持v-model |
| defaultValue | string | '' | 默认颜色值，用于重置 |
| placeholder | string | '' | 输入框占位符 |
| disabled | boolean | false | 是否禁用 |

此外，组件支持所有Naive UI的n-color-picker组件的原生属性。

#### 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| update:value | string | 颜色值变化时触发 |
| change | string | 颜色值变化时触发 |

### GradientColorPicker

渐变色选择器组件，包含两个颜色块、连接线、输入框、角度控制和重置按钮。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| startColor | string | '' | 起始颜色值，支持v-model:startColor |
| endColor | string | '' | 结束颜色值，支持v-model:endColor |
| defaultStartColor | string | '' | 默认起始颜色值，用于重置 |
| defaultEndColor | string | '' | 默认结束颜色值，用于重置 |
| angle | number | 180 | 渐变角度，支持v-model:angle |
| defaultAngle | number | 180 | 默认渐变角度，用于重置 |
| showPreview | boolean | false | 是否显示渐变预览 |
| showAngleControl | boolean | false | 是否显示角度控制器 |
| disabled | boolean | false | 是否禁用 |
| placeholder | string | '' | 输入框占位符 |
| controlType | 'direction' \| 'angle' | 'direction' | 控制方式：'direction'表示使用方向选择，'angle'表示使用自定义角度 |

此外，组件支持所有Naive UI的n-color-picker组件的原生属性。

#### 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- | 
| update:startColor | string | 起始颜色值变化时触发 |
| update:endColor | string | 结束颜色值变化时触发 |
| update:angle | number | 渐变角度变化时触发 |
| change | GradientStyle | 任何值变化时触发，返回完整的渐变样式对象 |

#### GradientStyle类型定义

```typescript
interface GradientStyle {
  /** 渐变起始颜色 (HEX/RGBA) */
  startColor: string
  /** 渐变结束颜色 (HEX/RGBA) */
  endColor: string
  /** 渐变角度 (单位: deg) @default 180 ，180度从上到下，90度从左到右 */
  angle: number
}
```

## 使用示例

### 基础颜色块选择器

```vue
<template>
  <color-block v-model:value="color" @update:value="handleColorChange" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ColorBlock } from '@/components/ColorPicker'

const color = ref('#ff0000')

const handleColorChange = (value: string) => {
  console.log('颜色变化:', value)
}
</script>
```

### 单色选择器

```vue
<template>
  <single-color-picker
    v-model:value="color"
    :defaultValue="'#333333'"
    :placeholder="'#FFFFFF'"
    :disabled="false"
    @change="handleColorChange"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { SingleColorPicker } from '@/components/ColorPicker'

const color = ref('#333333')

const handleColorChange = (value: string) => {
  console.log('颜色变化:', value)
}
</script>
```

### 渐变色选择器

```vue
<template>
  <gradient-color-picker
    v-model:startColor="gradientStyle.startColor"
    v-model:endColor="gradientStyle.endColor"
    v-model:angle="gradientStyle.angle"
    :defaultStartColor="'#ffffff'"
    :defaultEndColor="'#000000'"
    :defaultAngle="180"
    :showPreview="true"
    :showAngleControl="true"
    control-type="direction"
    :placeholder="'#FFFFFF'"
    @change="handleGradientChange"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { GradientColorPicker } from '@/components/ColorPicker'
import type { GradientStyle } from '@/components/ColorPicker'

const gradientStyle = ref<GradientStyle>({
  startColor: '#ffffff',
  endColor: '#000000',
  angle: 180
})

const handleGradientChange = (value: GradientStyle) => {
  console.log('渐变样式变化:', value)
}
</script>
``` 