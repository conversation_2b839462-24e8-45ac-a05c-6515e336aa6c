import { App } from 'vue'
import ColorBlock from './src/ColorBlock.vue'
import SingleColorPicker from './src/SingleColorPicker.vue'
import GradientColorPicker from './src/GradientColorPicker.vue'

// 注册所有组件
export function setupColorPicker(app: App) {
  app.component('ColorBlock', ColorBlock)
  app.component('SingleColorPicker', SingleColorPicker)
  app.component('GradientColorPicker', GradientColorPicker)
}

// 导出组件
export { ColorBlock, SingleColorPicker, GradientColorPicker }
export type { GradientStyle } from './src/type'

// 默认导出
export default {
  install: (app: App) => {
    setupColorPicker(app)
  },
}
