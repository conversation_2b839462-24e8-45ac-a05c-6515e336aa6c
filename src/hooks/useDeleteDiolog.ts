import { useDialog } from 'naive-ui'

/**
 * 通用删除确认弹窗 hooks
 * @param onConfirm 确认回调
 */
export function useDeleteDialog() {
  const dialog = useDialog()

  /**
   * @param content 弹窗内容
   * @param onConfirm 确认回调
   * @param title 弹窗标题
   * @param options 可选项：positiveText、negativeText、type
   */
  function showDeleteDialog(
    content: string,
    onConfirm: () => void,
    options?: {
      title?: string
      positiveText?: string
      negativeText?: string
      type?: 'error' | 'warning' | 'info' | 'success'
    },
  ) {
    dialog.warning({
      title: options?.title || '温馨提示',
      content: content,
      positiveText: options?.positiveText || '删除',
      negativeText: options?.negativeText || '取消',
      positiveButtonProps: { type: options?.type || 'error' },
      onPositiveClick: onConfirm,
    })
  }

  return {
    showDeleteDialog,
  }
}
