<template>
  <n-drawer v-model:show="active" :block-scroll="false" width="1050px" placement="right">
    <n-drawer-content title="用户协议" closable>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-[16px] ml-[4px]">用户协议</span>
          </div>
        </div>
      </template>
      <div>
        <h1 class="text-[20px]">AI CRM 后台用户协议</h1>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  const active = ref(false)
  const emit = defineEmits(['reload'])
  async function showDrawer() {
    active.value = true
  }
  // 关闭线索详情
  function closeDrawer() {
    emit('reload')
  }
  defineExpose({
    showDrawer,
    closeDrawer,
  })
</script>
<style scoped lang="scss"></style>
