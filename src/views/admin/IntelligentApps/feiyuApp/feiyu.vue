<template>
  <div>
    <n-card :title="t('intelligentApplications.common.feiyuAppSettings')">
      <n-tabs type="line" animated>
        <n-tab-pane name="basicSettings" :tab="t('intelligentApplications.common.basicSettings')">
          <n-space class="mb-4">
            {{ t('intelligentApplications.common.isOpenPlugin') }}
            <n-switch v-model:value="showPath" />
          </n-space>
          <n-form
            v-if="showPath"
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            :style="{
              maxWidth: '640px',
            }"
          >
            <n-form-item label="APP_ID" path="appId">
              <n-input v-model:value="model.appId" placeholder="Input" />
            </n-form-item>
            <n-form-item label="Secret" path="secret">
              <n-input v-model:value="model.secret" placeholder="Input" />
            </n-form-item>
            <n-form-item
              :label="t('intelligentApplications.common.callbackAddress')"
              path="callbackAddress"
            >
              <n-input v-model:value="model.callbackAddress" placeholder="Input" />
            </n-form-item>
            <n-form-item
              :label="t('intelligentApplications.common.specifyReturnLeadPool')"
              path="specifyReturnLeadPool"
            >
              <n-cascader
                v-model:value="model.specifyReturnLeadPool"
                :options="options"
                label-field="department_name"
                value-field="id"
              />
            </n-form-item>
            <div>
              <n-button round type="primary" @click="saveConfig">保存</n-button>
            </div>
          </n-form>

          <!-- <basicSettings /> -->
        </n-tab-pane>
        <n-tab-pane name="advancedSettings" :tab="t('intelligentApplications.common.requestLog')">
          <BasicForm
            @register="queryRegister"
            @submit="loadDataTable"
            :show-advanced-button="false"
          />
          <BasicTable
            ref="tableRef"
            :columns="columns"
            :request="loadDataTable"
            :row-key="(row) => row.id"
            :autoScrollX="true"
            :showTableSetting="false"
          />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { BasicForm, useForm } from '@/components/Form'
  import { BasicTable } from '@/components/Table'
  import { onMounted, ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { NCard, NInput, NSwitch, NSpace, NCascader } from 'naive-ui'
  import { getDepartments } from '@/api/admin/leads/seaLead'
  import { querySchemas } from '../config/schemas'
  import { columns } from '../config/columns'

  const { t } = useI18n()
  const showPath = ref(false)
  const model = ref({
    appId: '',
    secret: '',
    callbackAddress: '',
    specifyReturnLeadPool: '',
  })
  const rules = ref({
    appId: [{ required: true, message: '请输入' }],
    secret: [{ required: true, message: '请输入' }],
    callbackAddress: [{ required: true, message: '请输入' }],
    specifyReturnLeadPool: [{ required: true, message: '请选择' }],
  })
  const formRef = ref()
  // 保存配置
  function saveConfig() {
    formRef.value.validate().then((res) => {
      console.log(res)
    })
  }

  const [queryRegister, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas: computed(() => querySchemas()),
  })
  const loadDataTable = async (params: any) => {
    const formParams = getFieldsValue()
    console.log({ ...params, ...formParams })
  }
  // 获取部门列表
  const options = ref()
  onMounted(() => {
    getDepartments().then((data) => {
      options.value = data
    })
  })
</script>

<style scoped></style>
