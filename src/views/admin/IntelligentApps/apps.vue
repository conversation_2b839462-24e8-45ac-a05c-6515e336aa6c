<template>
  <div>
    <n-card :title="t('intelligentApplications.common.intelligentApplication')">
      <div class="flex flex-wrap gap-3">
        <div
          class="flex justify-center items-center border border-gray-200 rounded-md p-3 w-1/5 hover:border-primary-color cursor-pointer transition-all duration-300 hover:shadow-md"
          v-for="(item, index) in appList"
          :key="index"
          @click="handleClick(item.path)"
        >
          <div class="mr-4 w-14 flex items-center justify-center">
            <n-image width="100" src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg" />
          </div>
          <div class="flex-1 flex flex-col justify-center">
            <div class="text-lg font-bold">{{ item.name }}</div>
            <div class="text-sm text-gray-500 line-clamp-2 text-ellipsis">
              {{ item.description }}
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { appPath } from './config/appPath'
  import { useI18n } from '@/hooks/web/useI18n'
  import { NCard } from 'naive-ui'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const { t } = useI18n()
  const appList = [
    {
      name: 'AI 智能体',
      description: '全能营销顾问，大师级话术专家，带您进入AI营销时代',
      path: appPath.AI_AGENT,
    },
    {
      name: '飞鱼',
      description: '搭建飞鱼接口，智能承接外部流量',
      path: appPath.FEIYU,
    },
  ]
  const handleClick = (path: string) => {
    router.push(path)
  }
</script>

<style scoped></style>
