<template>
  <div>
    <n-card :title="t('intelligentApplications.common.aiAgentApp')">
      <div class="flex flex-wrap gap-3">
        <div
          class="flex justify-center items-center border border-gray-200 rounded-md p-3 w-1/5 hover:border-primary-color cursor-pointer transition-all duration-300 hover:shadow-md"
          v-for="(item, index) in appList"
          :key="index"
          @click="handleClick(item)"
        >
          <div class="mr-4 w-14 flex items-center justify-center">
            <n-image width="100" src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg" />
          </div>
          <div class="flex-1 flex flex-col justify-center">
            <div class="text-lg font-bold">{{ item.name }}</div>
            <div class="text-sm text-gray-500 line-clamp-2 text-ellipsis">
              {{ item.description }}
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { useI18n } from '@/hooks/web/useI18n'
  import { NCard } from 'naive-ui'
  const { t } = useI18n()
  const appList = [
    {
      name: '推广文案助手',
      description: '赋能销售精准推广，一键生成专业文案，精准驱动私域流量转化。',
    },
    {
      name: '企业形象助手',
      description: '提供全面的企业背书文案，助力企业传递实力与价值观，塑造专业形象。',
    },
    {
      name: '客户关怀助手',
      description: '定制个性化关怀内容，深度维护客户关系，提升满意度与忠诚度。',
    },
    {
      name: '销售案例助手',
      description: '汇聚企业成功范例，助力销售人员深度沟通客户，快速构建信任桥梁。',
    },
    {
      name: '客户邀约助手',
      description: '智能生成高效邀约话术，优化客户沟通体验，提升邀约成功率。',
    },
    {
      name: '异议解答助手',
      description: '提供专业异议解决方案，强化谈判优势，提升成交转化率。',
    },
    {
      name: '产品展示助手',
      description: '锻造精准产品话术，提示销售沟通专业度，有效降低客户疑虑。',
    },
  ]
  const handleClick = (item: any) => {
    console.log(item)
  }
</script>

<style scoped></style>
