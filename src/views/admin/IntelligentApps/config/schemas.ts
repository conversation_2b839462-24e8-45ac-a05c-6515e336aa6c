import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { dateShortcuts } from '@/utils'
const { t } = useI18n()

export function querySchemas(): FormSchema[] {
  return [
    {
      field: 'stage_id',
      component: 'NSelect',
      label: t('leads.common.sopStep'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: [],
        'label-field': 'title',
        'value-field': 'id',
      },
    },
    {
      field: 'stage_id',
      component: 'NSelect',
      label: t('leads.common.sopStep'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: [],
        'label-field': 'title',
        'value-field': 'id',
      },
    },
    {
      field: 'stage_id',
      component: 'NSelect',
      label: t('leads.common.sopStep'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: [],
        'label-field': 'title',
        'value-field': 'id',
      },
    },
    {
      field: 'created_at',
      component: 'NDatePicker',
      label: t('leads.common.addTime'),
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}
