import { BasicColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { formatToDateTime } from '@/utils'

const { t } = useI18n()

export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('leads.common.customer'),
    width: 80,
    key: 'customer_name',
  },
  {
    title: t('leads.common.rgsLocation'),
    key: 'location_province_id',
  },
  {
    title: t('leads.common.companyName'),
    key: 'company_name',
  },
  {
    title: t('leads.common.industry'),
    key: 'industry_id',
  },
  {
    title: t('leads.common.sex'),
    key: 'sex',
    render(row) {
      if (row == 1) {
        return '未知'
      } else if (row == 2) {
        return '男'
      } else {
        return '女'
      }
    },
  },
  {
    title: t('leads.common.age'),
    key: 'age',
  },
  {
    title: t('leads.common.wechat'),
    key: 'wechat',
  },
  {
    title: t('leads.common.callStatus'),
    key: 'call_status',
  },
  {
    title: t('leads.common.sopStep'),
    key: 'stage_id',
  },
  {
    title: t('leads.common.leadLabel'),
    key: 'label_ids',
  },
  {
    title: t('leads.common.entryMethod'),
    key: 'entry_methods',
  },
  {
    title: t('leads.common.source'),
    key: 'source',
  },
  {
    title: t('leads.common.followStatus'),
    key: 'follow_status',
  },
  {
    title: t('common.createTimeText'),
    key: 'created_at',
    render(row) {
      return formatToDateTime(row.created_at)
    },
  },
  {
    title: t('leads.common.otherInfo'),
    key: 'other_info',
  },
]
