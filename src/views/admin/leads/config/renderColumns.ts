import { h } from 'vue'
import { NPopover, NIcon, NGradientText, NImage } from 'naive-ui'
import { colorMap } from '@/settings/designSetting'
import { Ellipse } from '@vicons/ionicons5'
import { Invalid, ToBack, Allocated, FollowProgress, FollowedUp } from '@/utils/imagesImport'

/**
 * 渲染线索标签
 * @param row
 * @returns
 */
export function renderLabelRelationship(row: any) {
  if (!row.label_relationship || !Array.isArray(row.label_relationship)) {
    return null
  }

  return h(
    NPopover,
    {
      trigger: 'hover',
      placement: 'top',
    },
    {
      trigger: () =>
        h(
          'div',
          { style: 'display: flex; gap: 4px; flex-wrap: no-wrap;' },
          row.label_relationship.map((item: any) => {
            if (!item.name) return null

            return h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  width: 'auto',
                  textAlign: 'center',
                },
              },
              item.name,
            )
          }),
        ),
      default: () =>
        h(
          'div',
          { style: 'padding: 8px;' },
          row.label_relationship.map((item: any) =>
            h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item?.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  marginBottom: '4px',
                  textAlign: 'center',
                  display: 'inline-block',
                  marginRight: '4px',
                  width: 'auto',
                },
              },
              item?.name,
            ),
          ),
        ),
    },
  )
}

/**
 * 渲染客户信息
 * @param row
 * @returns
 */
export function renderCustomer(row: any) {
  return h(
    'div',
    {
      class: 'flex items-center cursor-pointer',
      onClick: () => {
        row._showLeadDetail(row)
      },
    },
    [
      h(
        'div',
        {
          class:
            'text-[16px] text-white size-[32px] rounded-full bg-blue-dot flex items-center justify-center flex-shrink-0 mr-[8px]',
        },
        {
          default: () => row.customer_name[0],
        },
      ),
      h('div', { class: 'text-[14px]' }, [
        h(
          'div',
          {
            class: 'text-title-deep-gray',
          },
          {
            default: () => row.customer_name,
          },
        ),
        h(
          'div',
          {
            class: 'text-text-middle-gray',
          },
          {
            default: () => row.phone,
          },
        ),
      ]),
    ],
  )
}

/**
 * 渲染通话状态
 * 通话状态（1：待联系，2：未接通，3：已接通，4：有效沟通，5：深度沟通）
 * @param row
 * @returns
 */
export function renderCallStatus(row: any) {
  let color = ''
  if (row.call_status === 1) {
    color = colorMap['orange-dot']
  } else if (row.call_status === 2) {
    color = colorMap['red-dot']
  } else if (row.call_status === 3) {
    color = colorMap['green-dot']
  } else if (row.call_status === 4) {
    color = colorMap['blue-dot']
  } else if (row.call_status === 5) {
    color = colorMap['purple-dot']
  }

  return h(
    'div',
    {
      class: 'flex items-center',
    },
    [
      h(NIcon, { color: color, size: '8px' }, { default: () => h(Ellipse) }),
      h(
        NGradientText,
        {
          class: 'text-title-deep-gray  ml-1',
        },
        { default: () => row.call_status_text },
      ),
    ],
  )
}

/**
 * 渲染跟进状态
 * 跟进状态（1：待分配，2：待跟进，3：跟进中，4：客户回退，5：无效）
 * @param row
 * @returns
 */
export function renderFollowStatus(row) {
  let renderImg = ''
  // 待分配
  if (row.follow_status === 1) {
    renderImg = Allocated
  }
  // 待跟进
  else if (row.follow_status === 2) {
    renderImg = FollowedUp
  }
  // 跟进中
  else if (row.follow_status === 3) {
    renderImg = FollowProgress
  }
  // 客户回退
  else if (row.follow_status === 4) {
    renderImg = ToBack
  }
  // 无效
  else if (row.follow_status === 5) {
    renderImg = Invalid
  }

  return h(
    'div',
    {
      class: 'flex items-center',
    },
    [
      h(NImage, { src: renderImg, width: 14, height: 14 }),
      h(
        NGradientText,
        {
          class: 'text-title-deep-gray  ml-1',
        },
        { default: () => row.follow_status_text },
      ),
    ],
  )
}
