export type LeadLabelType = {
  id: number
  name: string
}
export type SopStatusType = {
  id: number
  title: string
}
export type FollowStatusType = {
  id: number
  name: string
}
export type DepartmentType = {
  id: number
  parent_id: number
  department_name: string
  children: DepartmentType[]
}
export type IndustryType = {
  id: number
  industry_name: string
  status: number
}
export type EntryMethodType = {
  id: number
  name: string
}
export type SourceType = {
  id: number
  source_name: string
  status: number
}
export type CallStatusType = {
  id: number
  name: string
}

export type RegionType = {
  pid: number
  id: number
  name: string
  level: number
  children: RegionType[]
}

export type CheckPhoneType = string

export type ListAllType = {
  // 公海池
  seasPoolList: DepartmentType[]
  // 电话归属地
  regionList: RegionType[]
  // 所属行业
  industryList: IndustryType[]
  // 录入方式
  entryList: EntryMethodType[]
  // 渠道列表
  sourceList: SourceType[]
  // 通话状态
  callStatusList: CallStatusType[]
  // 线索标签
  leadLabelList: LeadLabelType[]
  // 跟进状态
  followStatusList: FollowStatusType[]
  // sop 阶段
  soptatusList: SopStatusType[]
}

export type RequestCreateSeaLeadsType = {
  address?: string
  /**
   * 联系人年龄
   */
  age?: string
  /**
   * 企业名称
   */
  company_name?: string
  /**
   * 联系人姓名
   */
  customer_name?: string
  /**
   * 线索归属部门（公海池）
   */
  department_id?: string
  /**
   * 所属行业（大健康 需定制 农副快消 新零售 茶叶酒水 服务行业 美业 知识付费 家具家居建材机械等 服装饰品 汽车服务）
   */
  industry_id?: string
  /**
   * 所在地（省-市-区 拼接）
   */
  location_address?: string
  /**
   * 所在地-市（例：440100-广州）
   */
  location_city_id?: string
  /**
   * 所在地-区（例：440111-白云，440104-越秀）
   */
  location_district_id?: string
  /**
   * 所在地-省（例：440000-广东）
   */
  location_province_id?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 联系电话
   */
  phone_id?: string
  /**
   * 备注不能超过100个字！
   */
  remark?: string
  /**
   * 联系人性别（1：未知，2：男，3：女）
   */
  sex?: string
  /**
   * 渠道来源（直播、微信、视频号、抖音、企微、公众号、官网、小红书、电话、短信）
   */
  source?: string
  /**
   * 渠道 id
   */
  source_id?: string
  /**
   * 渠道账号
   */
  source_account?: string
  /**
   * 联系人微信
   */
  wechat?: string
  id?: string
}

export type RequestCheckPhoneType = {
  phone?: string
  [property: string]: any
}

/**
 * 领取线索接口参数类型
 */
export type ReceiveLeadsItemType = {
  department_id: number | string
  leads_ids: (number | string)[]
}

export type RequestReceiveLeadsType = {
  ids: string[]
}

export type RequestTransferLeadsToDepartmentType = {
  department_id: number | string
  ids: (number | string)[]
}
export type RequestTransferLeadsToUserType = {
  user_id: number | string
  ids: (number | string)[]
}
export type QuerySeaLeadsType = {
  /**
   * 通话状态（1：待联系，2：未接通，3：已接通，4：有效沟通，5：深度沟通）
   */
  call_status?: string
  /**
   * 企业名称
   */
  company_name?: string
  /**
   * 添加时间
   */
  'created_at[]'?: string[]
  /**
   * 联系人姓名
   */
  customer_name?: string
  /**
   * 选择公海池的id（数组）
   */
  'department_ids[]'?: string[]
  /**
   * 录入方式（1：人工导入，2：飞鱼）
   */
  entry_methods?: string
  /**
   * 跟进状态（1：待分配，2：待跟进，3：跟进中，4：客户回退，5：无效）
   */
  follow_status?: string
  /**
   * 所属行业id（大健康 需定制 农副快消 新零售 茶叶酒水 服务行业 美业 知识付费 家具家居建材机械等 服装饰品 汽车服务 ）
   */
  industry_id?: string
  /**
   * 线索标签id
   */
  label_id?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 电话归属地-市（第二层）
   */
  phone_city_id?: string
  /**
   * 电话归属地-省（第一层）
   */
  phone_province_id?: string
  /**
   * 渠道来源id（直播 微信 视频号 抖音 企微 公众号 官网 小红书 电话 短信 ）
   */
  source?: string
  /**
   * SOP阶段id
   */
  stage_id?: string
  [property: string]: any
}

export type AssignPeopleType = {
  employee_name: string
  id: number
}

export type RequestAssignLeadsType = {
  user_id: number
  ids: (number | string)[]
}
export type RequestBatchDelLeadsType = {
  ids: (number | string)[]
  reason: string
  from: number
}
export type ExportLeadsType = {
  [key: string]: string
}
export type RequestGenerateExportTaskType = {
  /**
   * 导出字段信息
   */
  checked: string[]
  /**
   * 搜索信息
   */
  filter: { ids: string[] }
  /**
   * 导出位置标识
   */
  key: string
}

export type LeadDetailType = {
  address: string
  age: number
  assign_receive: number
  assign_receive_time: number
  assign_receive_user_id: number
  call_status: number
  company_name: string
  created_at: string
  creator_id: number
  customer_name: string
  delete_reason: string
  department_id: number
  entry_methods: number
  execute_assign_id: number
  first_assign_time: number
  follow_status: number
  has_deleted: number
  id: number
  industry_id: number
  industry_name: string
  location_address: string
  location_city_id: number
  location_district_id: number
  location_province_id: number
  other_info: string
  phone: string
  phone_address: string
  phone_city_id: number
  phone_province_id: number
  remark: string
  set_invalid: number
  sex: number
  source: number
  stage_id: number
  transfer_user_id: number
  updated_at: string
  wechat: string
  wechat_avatar: string
  wechat_name: string
  principal: string
  assign_person: string
  creator_name: string
  current_page: string
  department_name: string
  collaborator: string
  call_status_text: string
  follow_status_text: string
  sex_text: string
}
export type LabelsTagsType = {
  list: FormatLabelsType[]
  selected_list: SelectedTagsType[]
  selected_count: number
}
export type FormatLabelsType = {
  /**
   * 标签分类名称
   */
  category_name: string
  /**
   * 标签分类ID
   */
  id: number
  /**
   * 关联标签数组
   */
  label_setting: LabelSetting[]
}

export type LabelSetting = {
  /**
   * 标签分类ID
   */
  category_id: number
  /**
   * 颜色（16进制或其他）
   */
  color: string
  /**
   * 标签ID
   */
  id: number
  /**
   * 标签名称
   */
  name: string
}
export type SelectedTagsType = {
  id: number
  name: string
  color: string
  tageColor: { color?: string; borderColor?: string; textColor?: string }
}
export type RequestAddRecordUpRecordsType = {
  /**
   * 跟进内容
   */
  content: string
  /**
   * type=3时必传，传客户ID
   */
  extra_id?: number
  /**
   * 跟进时间，格式：YYYY-mm-dd hh:ii:ss
   */
  follow_up_at: string
  /**
   * 与type类型相对应，type=1时是线索ID，type=2是时客户ID，type=3是商机ID
   */
  link_id: number
  /**
   * 场景值，此处传add
   */
  scene: string
  /**
   * 类型，1：线索，2：客户，3：商机
   */
  type: number
}
export type RequestGetRecordUpRecordsType = {
  /**
   * type=3时必传，传客户ID
   */
  extra_id?: number
  /**
   * 操作时间筛选，格式：YYYY-mm-dd
   */
  'follow_up_at[]'?: string[]
  /**
   * 与type类型相对应，type=1时是线索ID，type=2是时客户ID，type=3是商机ID
   */
  link_id?: number
  /**
   * 场景值，此处传list
   */
  scene?: string
  /**
   * 类型，1：线索，2：客户，3：商机
   */
  type?: number
}
export type FollowRecord = {
  creator: string
  creator_id: number
  follow_up_at: string
  follow_up_content: string
  id: number
}
