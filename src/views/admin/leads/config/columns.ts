import { BasicColumn } from '@/components/Table'
import { h } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'

import { NIcon, NGradientText, NImage } from 'naive-ui'

import { formatToDateTime } from '@/utils'

import { Ellipse } from '@vicons/ionicons5'

import { Invalid, ToBack, Allocated, FollowProgress, FollowedUp } from '@/utils/imagesImport'

import { colorMap } from '@/settings/designSetting'
const { t } = useI18n()

/**
 * 渲染通话状态
 * 通话状态（1：待联系，2：未接通，3：已接通，4：有效沟通，5：深度沟通）
 * @param row
 * @returns
 */
function renderCallStatus(row) {
  let color = ''
  if (row.call_status === 1) {
    color = colorMap['orange-dot']
  } else if (row.call_status === 2) {
    color = colorMap['red-dot']
  } else if (row.call_status === 3) {
    color = colorMap['green-dot']
  } else if (row.call_status === 4) {
    color = colorMap['blue-dot']
  } else if (row.call_status === 5) {
    color = colorMap['purple-dot']
  }

  return h(
    'div',
    {
      class: 'flex items-center',
    },
    [
      h(NIcon, { color: color, size: '8px' }, { default: () => h(Ellipse) }),
      h(
        NGradientText,
        {
          class: 'text-title-deep-gray  ml-1',
        },
        { default: () => row.call_status_text },
      ),
    ],
  )
}

/**
 * 渲染客户信息
 * @param row
 * @returns
 */
function renderCustomer(row) {
  return h(
    'div',
    {
      class: 'flex items-center cursor-pointer',
      onClick: () => {
        row._showLeadDetail(row)
      },
    },
    [
      h(
        'div',
        {
          class:
            'text-[16px] text-white size-[32px] rounded-full bg-blue-dot flex items-center justify-center flex-shrink-0 mr-[8px]',
        },
        {
          default: () => row.customer_name[0],
        },
      ),
      h('div', { class: 'text-[14px]' }, [
        h(
          'div',
          {
            class: 'text-title-deep-gray',
          },
          {
            default: () => row.customer_name,
          },
        ),
        h(
          'div',
          {
            class: 'text-text-middle-gray',
          },
          {
            default: () => row.phone,
          },
        ),
      ]),
    ],
  )
}

/**
 * 渲染跟进状态
 * 跟进状态（1：待分配，2：待跟进，3：跟进中，4：客户回退，5：无效）
 * @param row
 * @returns
 */
export function renderFollowStatus(row) {
  let renderImg = ''
  // 待分配
  if (row.follow_status === 1) {
    renderImg = Allocated
  }
  // 待跟进
  else if (row.follow_status === 2) {
    renderImg = FollowedUp
  }
  // 跟进中
  else if (row.follow_status === 3) {
    renderImg = FollowProgress
  }
  // 客户回退
  else if (row.follow_status === 4) {
    renderImg = ToBack
  }
  // 无效
  else if (row.follow_status === 5) {
    renderImg = Invalid
  }

  return h(
    'div',
    {
      class: 'flex items-center',
    },
    [
      h(NImage, { src: renderImg, width: 14, height: 14 }),
      h(
        NGradientText,
        {
          class: 'text-title-deep-gray  ml-1',
        },
        { default: () => row.follow_status_text },
      ),
    ],
  )
}
export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('leads.common.customer'),
    width: 165,
    minWidth: 165,
    key: 'customer_name',
    resizable: true,
    render(row) {
      return renderCustomer(row)
    },
  },
  {
    title: t('leads.common.rgsLocation'),
    key: 'phone_address',
    resizable: true,
    width: 125,
    minWidth: 125,
  },
  {
    title: t('leads.common.companyName'),
    key: 'company_name',
    resizable: true,
    width: 194,
    minWidth: 194,
  },
  {
    title: t('leads.common.industry'),
    key: 'industry_name',
    resizable: true,
    width: 96,
    minWidth: 96,
  },
  {
    title: t('leads.common.sex'),
    key: 'sex_text',
    resizable: true,
    width: 55,
    minWidth: 55,
  },
  {
    title: t('leads.common.age'),
    key: 'age',
    resizable: true,
    width: 55,
    minWidth: 55,
  },
  {
    title: t('leads.common.wechat'),
    key: 'wechat',
    resizable: true,
    width: 100,
    minWidth: 100,
  },
  {
    title: t('leads.common.callStatus'),
    key: 'call_status_text',
    render(row) {
      return renderCallStatus(row)
    },
    resizable: true,
    width: 90,
    minWidth: 90,
  },
  {
    title: t('leads.common.leadLabel'),
    key: 'label_ids',
    resizable: true,
    width: 278,
    minWidth: 278,
  },
  {
    title: t('leads.common.entryMethod'),
    key: 'entry_methods',
    resizable: true,
    width: 90,
    minWidth: 90,
  },
  {
    title: t('leads.common.source'),
    key: 'source',
    resizable: true,
    width: 56,
    minWidth: 56,
  },
  {
    title: t('leads.common.followStatus'),
    key: 'follow_status_text',
    resizable: true,
    width: 88,
    minWidth: 88,
    render(row) {
      return renderFollowStatus(row)
    },
  },
  {
    title: t('common.createTimeText'),
    key: 'created_at',
    render(row) {
      return formatToDateTime(row.created_at)
    },
    resizable: true,
    width: 130,
    minWidth: 130,
  },
  {
    title: t('leads.common.otherInfo'),
    key: 'other_info',
  },
]
