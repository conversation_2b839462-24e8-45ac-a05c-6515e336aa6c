import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { dateShortcuts } from '@/utils'
import { ListAllType } from './leadTypes'
const { t } = useI18n()

export function querySchemas(listAll: ListAllType): FormSchema[] {
  return [
    {
      field: 'department_id',
      component: 'NCascader',
      label: t('leads.common.chooseLeadPool'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.seasPoolList,
        'label-field': 'department_name',
        'value-field': 'id',
      },
    },
    {
      field: 'customer_name',
      component: 'NInput',
      label: t('leads.common.customerName'),
      componentProps: {
        placeholder: t('leads.common.customerNamePlaceholder'),
      },
    },
    {
      field: 'phone',
      component: 'NInput',
      label: t('leads.common.customerPhone'),
      componentProps: {
        placeholder: t('leads.common.customerPhonePlaceholder'),
      },
    },
    {
      field: 'location_province_id',
      component: 'NCascader',
      label: t('leads.common.rgsLocation'),
      componentProps: {
        options: listAll.regionList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        showPath: true,
        placeholder: t('leads.common.selectPlaceholder'),
      },
    },
    {
      field: 'company_name',
      component: 'NInput',
      label: t('leads.common.companyName'),
      componentProps: {
        placeholder: t('leads.common.companyNamePlaceholder'),
      },
    },
    {
      field: 'industry_id',
      component: 'NSelect',
      label: t('leads.common.industry'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.industryList,
        'label-field': 'industry_name',
        'value-field': 'id',
      },
    },
    {
      field: 'entry_methods',
      component: 'NSelect',
      label: t('leads.common.entryMethod'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.entryList,
        'label-field': 'name',
        'value-field': 'id',
      },
    },
    {
      field: 'source',
      component: 'NSelect',
      label: t('leads.common.sourceOrin'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.sourceList,
        'label-field': 'source_name',
        'value-field': 'id',
      },
    },
    {
      field: 'call_status',
      component: 'NSelect',
      label: t('leads.common.callStatus'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.callStatusList,
        'label-field': 'name',
        'value-field': 'id',
      },
    },
    {
      field: 'label_ids',
      component: 'NSelect',
      label: t('leads.common.leadLabel'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        multiple: true,
        options: listAll.leadLabelList,
        'label-field': 'name',
        'value-field': 'id',
      },
    },
    {
      field: 'follow_status',
      component: 'NSelect',
      label: t('leads.common.followStatus'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.followStatusList,
        'label-field': 'name',
        'value-field': 'id',
      },
    },
    {
      field: 'stage_id',
      component: 'NSelect',
      label: t('leads.common.sopStep'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.soptatusList,
        'label-field': 'title',
        'value-field': 'id',
      },
    },
    {
      field: 'created_at',
      component: 'NDatePicker',
      label: t('leads.common.addTime'),
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}
// 跟进记录搜索栏
export function followRecordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 操作记录搜索栏
export function recordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'created_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 跟进记录表单
export function followRecordSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_content',
      label: t('customer.common.followRecord'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        maxlength: 200,
        showCount: true,
        placeholder: '请输入跟进记录',
      },
    },
    {
      field: 'follow_up_at',
      label: t('customer.common.followTime'),
      component: 'NDatePicker',
      componentProps: {
        class: 'w-full',
        type: 'datetime',
        defaultValue: new Date(),
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.pleaseInputFollowTime'),
      },
    },
  ]
}
