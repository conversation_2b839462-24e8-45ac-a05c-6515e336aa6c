<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="no-padding">
        <n-form
          ref="formRef"
          label-placement="left"
          :label-width="100"
          :model="formValue"
          :rules="rules"
        >
          <div class="flex flex-row justify-items-center justify-between w-full mb-6 mt-6">
            <div class="text-title-deep-gray">请选择导出信息</div>
            <div>
              <n-checkbox
                value="all"
                label="全选"
                :indeterminate="
                  formValue.allFields.length > 0 && formValue.allFields.length < exportFields.length
                "
                @update:checked="handleAllChecked"
              />
            </div>
          </div>
          <n-checkbox-group v-model:value="formValue.allFields" @update:value="handleUpdateValue">
            <n-grid x-gap="8" :cols="4">
              <n-gi v-for="item in exportFields" :key="item.value">
                <n-checkbox
                  :value="item.value"
                  :label="item.label"
                  :checked="formValue.allFields.includes(item.value)"
                />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form>
      </div>
    </template>
    <template #action>
      <n-button type="primary" @click="formSubmit">导出</n-button>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { getExportFields, generateExportTask } from '@/api/admin/leads/seaLead'

  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   * @property {Function} register - 组件注册时触发
   */
  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()

  const props = defineProps({
    fromType: {
      type: Number as PropType<number>,
      default: 0,
    },
  })

  const formRef = ref(null)
  const formValue = ref({
    allFields: [] as string[],
  })
  const rules = ref({
    allFields: [{ required: true, message: t('leads.common.exportInfo') }],
  })

  /**
   * 单选
   * @param value
   */
  function handleUpdateValue(value: string[]) {
    formValue.value.allFields = value
  }
  /**
   * 全选
   */
  function handleAllChecked(value: boolean) {
    formValue.value.allFields = value ? exportFields.value.map((item) => item.value) : []
  }
  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 500,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  const exportFields = ref<{ label: string; value: string }[]>([])
  const checkedIds = ref<string[]>([])
  function showModal(ids: string[]) {
    exportFields.value = []

    checkedIds.value = ids

    openModal(t('leads.common.exportInfoPlaceholder'))
    getExportFields().then((res) => {
      const { fields } = res
      // 遍历fields对象
      Object.keys(fields).forEach((key) => {
        exportFields.value.push({
          label: fields[key],
          value: key,
        })
      })
    })
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    await (formRef.value as any)?.validate()
    try {
      setSubLoading(true)

      generateExportTask(
        {
          checked: formValue.value.allFields,
          filter: { ids: checkedIds.value.map((item) => item.split('@')[0]) },
          key: 'leads',
        },
        props.fromType,
      )
        .then(() => {
          message.success(t('leads.common.exportInfoTips') + t('leads.common.downloadCenter'))
          emit('reload')
          closeModal()
        })
        .catch((err) => {
          message.error(err.message || t('common.operationFailed') || '操作失败')
        })
        .finally(() => {
          setSubLoading(false)
        })
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
