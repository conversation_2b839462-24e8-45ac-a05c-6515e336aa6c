<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div>
        <div>
          <div class="text-lg w-full mb-5">
            {{ t('leads.common.confirmBackToSea') }}
          </div>
          <div class="w-full">
            <n-input
              ref="inputRef"
              v-model:value="reason"
              type="textarea"
              :placeholder="t('leads.common.confirmBackToSeaReason')"
              show-count
              maxlength="50"
            />
          </div>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { useMessage } from 'naive-ui'
  import { onMounted, ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { returnLeads } from '@/api/admin/leads/seaLead'
  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])
  const { t } = useI18n()
  const message = useMessage()
  const inputRef = ref()
  /**
   * 组件属性
   */
  const checkedIds = ref<string[]>([])

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 700,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  async function showModal(ids: string[]) {
    checkedIds.value = ids
    openModal(t('leads.common.confirmBackToSeaTitle'))
  }
  /**
   * 提交表单
   * 验证表单并提交数据
   */
  const reason = ref('')
  function formSubmit() {
    // 提交删除线索
    // 判断deleteReason是否为空 并且长度不能超过50
    if (!reason.value || reason.value.length <= 0 || reason.value.length > 50) {
      message.error(t('leads.common.confirmBackToSeaReason'))
      setSubLoading(false)
      inputRef.value.focus()
      return
    }
    setSubLoading(true)
    returnLeads(checkedIds.value, reason.value)
      .then(() => {
        message.success(t('common.operationSuccess'))
        emit('reload')
        closeModal()
      })
      .catch((err) => {
        message.error(err.message || t('common.operationFailed') || '操作失败')
      })
      .finally(() => {
        setSubLoading(false)
      })
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
