<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <div>
          <div class="w-full mb-[6px] text-text-deep-gray">
            已选择
            <em class="text-blue-deep">{{ checkedIds.length }}</em>
            个线索，将选中线索分配给：
          </div>
          <div class="w-full">
            <n-select
              v-model:value="deptValue"
              :options="options"
              value-field="id"
              label-field="employee_name"
            />
          </div>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { getAssignPeople, assignLeads } from '@/api/admin/leads/seaLead'
  import { AssignPeopleType, RequestAssignLeadsType } from '@/views/admin/leads/config/leadTypes'
  import { useMessage } from 'naive-ui'
  const message = useMessage()
  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])
  const { t } = useI18n()

  const checkedIds = ref<number[]>([])

  const options = ref<AssignPeopleType[]>([])
  const deptValue = ref<number | null>(null)

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  async function showModal(ids: number[]) {
    checkedIds.value = ids
    openModal(t('leads.common.distributeLeads'))
    options.value = await getAssignPeople()
    deptValue.value = options.value[0].id
  }
  /**
   * 提交表单
   * 验证表单并提交数据
   */
  // 部门 id
  async function formSubmit() {
    const data: RequestAssignLeadsType = {
      user_id: deptValue.value as number,
      ids: checkedIds.value.map((item) => item.toString().split('@')[0]),
    }
    assignLeads(data)
      .then(() => {
        message.success(t('common.operationSuccess'))
        emit('reload')
        closeModal()
      })
      .catch((err) => {
        message.error(err.message || t('common.operationFailed') || '操作失败')
      })
      .finally(() => {
        setSubLoading(false)
      })
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
