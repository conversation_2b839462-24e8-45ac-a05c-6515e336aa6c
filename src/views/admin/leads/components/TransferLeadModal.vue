<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <div>
          <div class="w-full mb-[6px] text-text-deep-gray">
            已选择
            <em class="text-blue-deep">{{ checkedIds.length }}</em>
            个线索，将选中线索转移至：
          </div>
          <div class="w-full">
            <n-cascader
              v-model:value="deptValue"
              label-field="department_name"
              value-field="id"
              :options="options"
              :default-value="deptValue"
            />
          </div>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { getDepartments } from '@/api/admin/leads/seaLead'
  import type { RequestTransferLeadsToDepartmentType } from '@/views/admin/leads/config/leadTypes'
  import type { CascaderOption } from 'naive-ui'
  import { transferLeadsToDepartment } from '@/api/admin/leads/seaLead'
  import { useMessage } from 'naive-ui'
  const message = useMessage()

  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])
  const { t } = useI18n()
  const deptValue = ref<string | number | null>(null)
  const options = ref<CascaderOption[]>([])
  const checkedIds = ref<number[]>([])
  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  async function showModal(ids: number[]) {
    checkedIds.value = ids
    openModal(t('leads.common.moveLeads'))
    options.value = await getDepartments()
    deptValue.value = options.value[0].id as number
  }
  /**
   * 提交表单
   * 验证表单并提交数据
   */
  // 部门 id
  function formSubmit() {
    setSubLoading(true)
    const data: RequestTransferLeadsToDepartmentType = {
      department_id: deptValue.value as number,
      ids: checkedIds.value.map((item) => item.toString().split('@')[0]),
    }
    transferLeadsToDepartment(data)
      .then(() => {
        message.success(t('common.operationSuccess'))
        emit('reload')
        closeModal()
      })
      .catch((err) => {
        message.error(err.message || t('common.operationFailed') || '操作失败')
      })
      .finally(() => {
        setSubLoading(false)
      })
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
