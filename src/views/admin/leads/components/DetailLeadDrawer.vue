<template>
  <n-drawer
    v-model:show="active"
    :width="isExpand ? '95%' : '1050px'"
    :block-scroll="false"
    :mask-closable="false"
    :show-mask="false"
    class="transition-all duration-300"
    placement="right"
  >
    <n-drawer-content :title="t('leads.common.leadDetail')" closable>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <n-image :preview-disabled="true" width="18" :src="LeadIcon" />
            <span class="text-[16px] font-bold ml-[5px]">{{ t('leads.common.leadDetail') }}</span>
          </div>
          <div class="flex items-center" v-if="props.fromType === FROM_TYPE_RECYCLE_LEAD">
            <n-button class="mr-[8px]" @click="handleChangePublic">退公海</n-button>
            <n-button class="mr-[12px]" text @click="handleExpand">
              <template #icon>
                <n-icon>
                  <ExpandOutline />
                </n-icon>
              </template>
            </n-button>
          </div>
          <div class="flex items-center" v-else>
            <n-checkbox
              class="mr-[24px]"
              :checked="isInvalid"
              @update:checked="handleInvalidChange"
            >
              {{ t('leads.common.setInvalid') }}
            </n-checkbox>
            <n-dropdown
              key-field="value"
              label-field="label"
              :options="popSelectOptions"
              trigger="click"
              @select="popSelectChange"
            >
              <n-button class="mr-[12px]" text>
                <template #icon>
                  <n-icon>
                    <EllipsisHorizontal />
                  </n-icon>
                </template>
              </n-button>
            </n-dropdown>
            <n-dropdown
              key-field="id"
              label-field="name"
              trigger="click"
              :options="followStatusList"
              @select="handleStateChange"
            >
              <n-button class="mr-[8px]">
                {{ t('leads.common.callStatusFollow') }}
              </n-button>
            </n-dropdown>
            <n-button class="mr-[8px]" @click="handleShowRecordModal">
              {{ t('customer.common.addUpRecord') }}
            </n-button>
            <n-button class="mr-[20px]" type="primary" @click="handleTransferClient">
              {{ t('leads.common.transferClient') }}
            </n-button>
            <n-button class="mr-[12px]" text @click="handleExpand">
              <template #icon>
                <n-icon>
                  <ExpandOutline />
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </template>
      <n-spin :show="showLoading">
        <div class="lead-detail-container">
          <!-- Main Content -->
          <div class="main-content">
            <!-- Left Panel: Lead Information -->
            <div class="left-panel">
              <div class="px-[16px] py-[18px] user-info">
                <div class="flex items-center relative mb-[12px]">
                  <div
                    class="text-white mr-[8px] border-[2px] flex items-center justify-center rounded-full bg-avatar-blue w-[54px] h-[54px] text-[24px] border-white"
                  >
                    <span>{{ leadDetail?.customer_name[0] }}</span>
                  </div>
                  <div>
                    <div class="w-[110px] text-xl font-bold line-clamp-2 ellipsis overflow-hidden">
                      {{ leadDetail?.customer_name }}
                    </div>
                  </div>
                  <n-image
                    width="70"
                    :preview-disabled="true"
                    class="absolute top-[-2px] right-[-21px]"
                    :src="TagIcon"
                  />
                  <span class="text-white absolute top-[-1px] right-[-15px]">线索档案</span>
                </div>
                <div
                  class="flex items-center mb-[4px] w-[195px] line-clamp-1 ellipsis overflow-hidden"
                  @click="handleCopyPhone"
                >
                  <n-button text class="text-text-deep-gray">
                    {{ leadDetail?.phone }}({{ leadDetail?.phone_address }})
                  </n-button>
                  <n-button text class="ml-[4px]">
                    <template #icon>
                      <n-image :preview-disabled="true" width="14" :src="CopyIcon" />
                    </template>
                  </n-button>
                </div>
                <div>
                  <n-button text class="text-text-deep-gray">
                    {{ leadDetail?.company_name }}
                  </n-button>
                </div>
              </div>

              <!-- Lead Overview -->
              <div class="flex items-center justify-between mb-[4px] mt-5">
                <div class="text-[16px] font-bold">{{ t('leads.common.leadOverview') }}</div>
              </div>
              <n-descriptions
                bordered
                size="small"
                :column="1"
                label-placement="left"
                label-align="left"
                label-class="w-[98px] p-0"
              >
                <n-descriptions-item
                  :label="t(`leads.common.${item.key}`) + ':'"
                  v-for="item in leadDetailList"
                  :key="item.key"
                >
                  <template #default>
                    <div v-if="item.key === 'followStatus'" class="flex items-center">
                      <n-image
                        v-if="getFollowStatusIcon(leadDetail?.follow_status)"
                        :preview-disabled="true"
                        :src="getFollowStatusIcon(leadDetail?.follow_status)"
                        width="14"
                        height="14"
                      />
                      <span class="ml-1">{{ leadDetail?.follow_status_text || '/' }}</span>
                    </div>
                    <div v-else-if="item.key === 'callStatus'" class="flex items-center">
                      <n-icon>
                        <Ellipse :color="callStatusColor(leadDetail?.call_status)" size="8" />
                      </n-icon>
                      <span class="ml-1">{{ leadDetail?.call_status_text || '/' }}</span>
                    </div>
                    <span v-else>{{ leadDetail?.[item.value] || '/' }}</span>
                  </template>
                </n-descriptions-item>
              </n-descriptions>
              <n-divider />

              <!-- Basic Information -->
              <div class="flex items-center justify-between mb-[4px] mt-5">
                <div class="text-[16px] font-bold">{{ t('leads.common.basicInformation') }}</div>
                <div class="text-sm text-gray-500">
                  <n-button text @click="handleEditLead">
                    <template #icon>
                      <n-icon><EditOutlined /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
              <n-descriptions
                :column="1"
                bordered
                size="small"
                label-placement="left"
                label-align="left"
                label-class="w-[98px] p-0"
              >
                <n-descriptions-item
                  :label="t(`leads.common.${item.key}`) + ':'"
                  v-for="item in leadInfoList"
                  :key="item.key"
                >
                  {{ leadDetail?.[item.value] || '/' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>
            <div class="right-panel">
              <!-- Lead Tags -->
              <div class="lead-tags mb-4 p-[24px]">
                <div class="text-title-deep-gray font-bold mb-[9px]">线索标签</div>
                <div class="flex items-center">
                  <n-tag
                    class="mr-[8px]"
                    size="small"
                    v-for="(tag, index) in leadTags?.selected_list"
                    :key="index"
                    closable
                    :color="{ color: tag?.color }"
                  >
                    {{ tag?.name || '' }}
                  </n-tag>
                  <n-button text type="primary" size="small" @click="handleAddTag">+ 添加</n-button>
                </div>
              </div>
              <n-divider />

              <!-- 线索SOP -->
              <SopExecute
                ref="sopExecuteRef"
                class="p-[24px]"
                :id="leadId"
                :isExpand="isExpand"
                :type="props.businessType"
                :fromType="props.fromType"
              />
              <n-divider />

              <div class="px-[24px]">
                <n-tabs v-model:value="currentTab" type="line" size="small">
                  <n-tab-pane name="FollowRecord" tab="跟进记录" />
                  <n-tab-pane name="Record" tab="操作记录" />
                </n-tabs>
                <FollowRecord
                  v-if="currentTab === 'FollowRecord'"
                  ref="followRecordRef"
                  :fromType="props.fromType"
                  :leadId="leadId"
                />
                <Record v-if="currentTab === 'Record'" :leadId="leadId" />
              </div>
            </div>
          </div>
        </div>
      </n-spin>
    </n-drawer-content>
  </n-drawer>

  <!-- 弹窗 -->
  <LeadTagsModal ref="leadTagsModalRef" :fromType="props.fromType" @reload="getFormatLabelsData" />
  <CreateEditLeadModal
    ref="CreateEditLeadModalRef"
    :listAll="props.listAll"
    :fromType="props.fromType"
    @reload="getDetailData"
  />

  <!-- 跟进记录弹窗 -->
  <CreateFollowRecord
    ref="createFollowRecordRef"
    :lead-id="leadId"
    :fromType="props.fromType"
    @reload="onFollowRecordReload"
  />
</template>

<script setup lang="ts">
  import { ref, nextTick, unref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import {
    useMessage,
    useDialog,
    NButton,
    NIcon,
    NDescriptions,
    NDescriptionsItem,
    NTag,
  } from 'naive-ui'

  import { FROM_TYPE_PUBLIC_LEAD, FROM_TYPE_RECYCLE_LEAD } from '@/enums/fromTypeEnum'

  import { getLeadDetail, getFormatLabels, changeCallStatus } from '@/api/admin/leads/seaLead'
  import {
    type LeadDetailType,
    type LabelsTagsType,
    type FollowStatusType,
    type ListAllType,
  } from '../config/leadTypes'

  import { EditOutlined } from '@vicons/antd'
  import { EllipsisHorizontal, ExpandOutline, Ellipse } from '@vicons/ionicons5'

  import {
    LeadIcon,
    TagIcon,
    CopyIcon,
    Invalid,
    ToBack,
    Allocated,
    FollowProgress,
    FollowedUp,
  } from '@/utils/imagesImport'

  import { colorMap } from '@/settings/designSetting'

  import { SopExecute } from '@/components/SopExecute'
  import LeadTagsModal from './LeadTagsModal.vue'
  import CreateEditLeadModal from './CreateEditLeadModal.vue'
  import FollowRecord from './FollowRecord.vue'
  import CreateFollowRecord from './CreateFollowRecord.vue'
  import Record from './Record.vue'
  import { useCopyToClipboard } from '@/hooks/web/useCopyToClipboard'
  const { clipboardRef, copiedRef } = useCopyToClipboard()
  import { useDeleteLead, useLeadValidStatus, useRetreatLead, useBecomeCustomer } from '../hooks'
  // FIXME: [LEADS] 需要添加上下切换线索的快捷按钮
  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()

  const active = ref(false)
  const showLoading = ref(false)
  const leadTagsModalRef = ref()

  const currentTab = ref('FollowRecord')

  const props = defineProps({
    listAll: {
      type: Object as PropType<ListAllType>,
      default: () => ({}),
    },
    // 页面标识
    fromType: {
      type: Number as PropType<number>,
      default: 0,
    },
    // 业务模块标识
    businessType: {
      type: String as PropType<string>,
      default: '',
    },
  })
  const emit = defineEmits(['reload'])

  // 线索详情
  const leadDetailList = [
    { key: 'createTime', value: 'created_at' },
    { key: 'currentPage', value: 'current_page' },
    { key: 'department', value: 'department_name' },
    { key: 'assignTime', value: 'first_assign_time' },
    { key: 'owner', value: 'principal' },
    { key: 'collaborator', value: 'collaborator' },
    { key: 'assignedBy', value: 'assign_person' },
    { key: 'lastOperationTime', value: 'updated_at' },
    { key: 'callStatus', value: 'call_status_text' },
    { key: 'followStatus', value: 'follow_status_text' },
    { key: 'firstAssignTime', value: 'first_assign_time' },
    { key: 'creator', value: 'creator_name' },
  ]
  // 基本信息
  const leadInfoList = [
    { key: 'customerName', value: 'customer_name' },
    { key: 'customerPhone', value: 'phone' },
    { key: 'customerWechat', value: 'wechat' },
    { key: 'customerSex', value: 'sex_text' },
    { key: 'customerAge', value: 'age' },
    { key: 'companyName', value: 'company_name' },
    { key: 'industry', value: 'industry_name' },
    { key: 'location', value: 'location_address' },
    { key: 'locationDetail', value: 'address' },
    { key: 'sourceOrin', value: 'source_name' },
    { key: 'remark', value: 'remark' },
  ]

  const isExpand = ref(false)
  function handleExpand() {
    isExpand.value = !isExpand.value
  }
  // 线索ID
  const leadId = ref<string>()
  // 线索详情 + 基本信息
  const leadDetail = ref<LeadDetailType>()

  const leadTags = ref<LabelsTagsType>()

  // 跟进状态
  const followStatusList = ref<FollowStatusType[]>([
    {
      id: 1,
      name: '待联系',
    },
    {
      id: 2,
      name: '未接通',
    },
    {
      id: 3,
      name: '已接通',
    },
    {
      id: 4,
      name: '有效沟通',
    },
    {
      id: 5,
      name: '深度沟通',
    },
  ])

  const popSelectOptions = ref(
    props.fromType === FROM_TYPE_PUBLIC_LEAD
      ? [
          {
            label: '删除',
            value: 'delete',
          },
        ]
      : [
          {
            label: '退公海',
            value: 'public',
          },
          {
            label: '删除',
            value: 'delete',
          },
        ],
  )

  // 复制号码
  function handleCopyPhone() {
    clipboardRef.value = leadDetail.value?.phone || ''
    if (unref(copiedRef)) {
      message.success(t('common.copySuccess'))
    }
  }

  // 刷新跟进记录列表
  const followRecordRef = ref()
  function onFollowRecordReload() {
    // 跟进记录列表
    followRecordRef.value?.handleFollowRecord?.()
  }

  const sopExecuteRef = ref()
  // 查看线索详情
  async function showDrawer(id: string) {
    leadId.value = id
    // 获取线索详情
    await getDetailData()
    // 获取线索标签
    await getFormatLabelsData()
    // 获取线索SOP
    sopExecuteRef.value?.getSopListData()

    active.value = true
  }

  // 获取线索详情
  function getDetailData() {
    showLoading.value = true
    getLeadDetail(leadId.value as string)
      .then((res) => {
        isInvalid.value = res?.set_invalid == 1 ? false : true
        leadDetail.value = res
        showLoading.value = false
      })
      .finally(() => {
        showLoading.value = false
      })
  }

  // 获取线索标签
  function getFormatLabelsData() {
    getFormatLabels(leadId.value as string).then((res) => {
      leadTags.value = res as unknown as LabelsTagsType
    })
  }

  // 关闭线索详情
  function closeDrawer() {
    active.value = false
  }

  // 写跟进
  const createFollowRecordRef = ref()
  function handleShowRecordModal() {
    if ([FROM_TYPE_PUBLIC_LEAD].includes(props.fromType)) {
      message.warning('此线索负责人不是您，您暂无权限操作')
      return
    }
    createFollowRecordRef.value?.showModal(leadId.value)
  }

  // 删除线索
  function deleteLead() {
    const { handleDeleteLead } = useDeleteLead([leadId.value as string], 1)
    handleDeleteLead(() => {
      closeDrawer()
      emit('reload')
    })
  }

  // 修改跟进状态
  function handleStateChange(key: string, option: FollowStatusType) {
    if ([FROM_TYPE_PUBLIC_LEAD].includes(props.fromType)) {
      message.warning('此线索负责人不是您，您暂无权限操作')
      return
    }

    dialog.warning({
      title: t('common.warningText'),
      content: t('leads.common.confirmChangeStatus') + option.name,
      positiveText: t('common.okText'),
      negativeText: t('common.cancelText'),
      draggable: false,
      onPositiveClick: () => {
        changeCallStatus(leadId.value as string, Number(key)).then(() => {
          message.success(t('common.operationSuccess'))
          getDetailData()
        })
      },
    })
  }

  // 下拉框选择
  function popSelectChange(key: string) {
    console.log(key)
    // 退公海
    if (key === 'public') {
      handleChangePublic()
    }
    // 删除
    else if (key === 'delete') {
      deleteLead()
    }
  }

  // 退回公海
  function handleChangePublic() {
    const { handleRetreatLead } = useRetreatLead([leadId.value as string], props.fromType)
    handleRetreatLead(() => {
      closeDrawer()
      emit('reload')
    })
  }

  // 转客户
  function handleTransferClient() {
    if ([FROM_TYPE_PUBLIC_LEAD].includes(props.fromType)) {
      message.warning('此线索负责人不是您，您暂无权限操作')
      return
    }
    const { handleBecome } = useBecomeCustomer(
      String(leadId.value),
      props.fromType,
      sopExecuteRef.value?.checkKeyStageCompleted(),
    )
    handleBecome(() => {
      closeDrawer()
      emit('reload')
    })
    // console.log('Transfer client')
  }

  // 添加标签
  function handleAddTag() {
    leadTagsModalRef.value.showModal(leadId.value, leadTags.value)
  }

  // 设为无效
  const { isInvalid, handleInvalidChange } = useLeadValidStatus({
    leadId,
    getDetailData,
    fromType: props.fromType,
  })

  /**
   * 编辑线索
   */
  const CreateEditLeadModalRef = ref()
  function handleEditLead() {
    let formData = leadDetail.value as any
    formData.age = String(formData.age)
    formData.location_array = Number(formData.location_district_id || formData.location_city_id)
    CreateEditLeadModalRef.value.showModal('edit', unref(formData))

    // 回写数据
    nextTick(() => {
      let formData = leadDetail.value as any

      formData.location_array = Number(formData.location_district_id || formData.location_city_id)
      CreateEditLeadModalRef.value.setFieldsValue(formData)
    })
  }

  function getFollowStatusIcon(status: number | undefined) {
    switch (status) {
      case 1:
        return Allocated
      case 2:
        return FollowedUp
      case 3:
        return FollowProgress
      case 4:
        return ToBack
      case 5:
        return Invalid
      default:
        return ''
    }
  }
  function callStatusColor(status: number | undefined) {
    let color = ''
    if (status === 1) {
      color = colorMap['orange-dot']
    } else if (status === 2) {
      color = colorMap['red-dot']
    } else if (status === 3) {
      color = colorMap['green-dot']
    } else if (status === 4) {
      color = colorMap['blue-dot']
    } else if (status === 5) {
      color = colorMap['purple-dot']
    }
    return color
  }
  defineExpose({
    showDrawer,
    closeDrawer,
  })
</script>

<style lang="scss" scoped>
  :deep(.n-drawer-body-content-wrapper) {
    padding: 0 !important;
  }
</style>
<style scoped>
  .user-info {
    background: url('@/assets/images/admin/bg-info.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .lead-detail-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .main-content {
    display: flex;
    /* gap: 20px; */
    height: 100%;
    overflow: hidden;
  }

  .left-panel {
    flex: 0 0 300px;
    overflow-y: auto;
    padding: 24px;
    border-right: 1px solid #e5e5e5;
  }

  .right-panel {
    flex: 1;
    overflow-y: auto;
    /* padding: 24px; */
  }

  .client-name {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .client-name h3 {
    margin: 0;
  }
</style>
