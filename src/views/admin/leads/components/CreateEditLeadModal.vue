<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <BasicForm ref="basicFormRef" @register="register" @reset="handleReset">
          <template #sex="{ model, field, schema }: any">
            <n-radio-group v-model:value="model[field]" name="sex" :default-value="1">
              <n-space>
                <n-radio
                  v-for="item in schema.componentProps.options"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </template>
          <template #sourceWithAccount="{ model, field }">
            <div style="display: flex; gap: 12px; width: 100%">
              <n-select
                v-model:value="model.source_id"
                :options="props.listAll.sourceList"
                label-field="source_name"
                value-field="id"
                style="width: 120px"
                @update:value="(val) => field({ source_id: val })"
              />
              <n-input
                v-model:value="model.source_account"
                style="flex: 1"
                @update:value="(val) => field({ source_account: val })"
              />
            </div>
          </template>
        </BasicForm>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, computed, ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { BasicForm, useForm } from '@/components/Form'
  import { basicModal, useModal } from '@/components/Modal'
  import { getSchema } from '../config/createLeadSchemas'
  import type { ListAllType, RequestCreateSeaLeadsType } from '../config/leadTypes'
  import { findParentPathById } from '@/utils'
  import { createSeaLeads, updateSeaLeads, checkPhone } from '@/api/admin/leads/seaLead'
  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   * @property {Function} register - 组件注册时触发
   */
  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()
  /**
   * 组件属性
   */
  const props = defineProps({
    isEdit: Boolean,
    show: Boolean,
    listAll: {
      type: Object as PropType<ListAllType>,
      default: () => ({}),
    },
    fromType: {
      type: Number as PropType<number>,
      default: 0,
    },
  })

  /**
   * 表单引用
   */
  const basicFormRef = ref(null)

  /**
   * 动态计算表单配置
   */
  const formSchemas = computed(() => getSchema(props.listAll))

  /**
   * 注册表单
   */
  const [register, { submit, setFieldsValue, resetFields }] = useForm({
    labelWidth: 120,
    gridProps: { cols: 2, xGap: 32 },
    layout: 'horizontal',
    showActionButtonGroup: false,
    schemas: formSchemas,
    requireMarkPlacement: 'left',
    labelPlacement: 'top',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 724,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  let formDataClone: RequestCreateSeaLeadsType = {}
  const modalType = ref('add')
  function showModal(type: string, formData: any) {
    modalType.value = type
    if (type === 'add') {
      openModal(t('leads.common.addLeads'))
    } else {
      openModal(t('leads.common.editLeads'))
      formDataClone = formData
    }
    console.log(formDataClone)
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return

      // 新增或编辑时，手机号变更都需校验
      if (
        modalType.value === 'add' ||
        (modalType.value === 'edit' && formDataClone?.phone !== formData.phone)
      ) {
        const checkPhoneRes = await checkPhone({ phone: formData.phone })
        if (checkPhoneRes !== '1') {
          message.error(t('leads.common.customerPhoneRequired'))
          return
        }
      }
      let locInfo = {}
      if (formData.location_array) {
        const { path, pathLabels }: any = findParentPathById(
          formData.location_array,
          props.listAll.regionList,
          {
            keyField: 'id',
            childrenField: 'children',
            labelField: 'name',
          },
        )

        if (path.length === 2) {
          locInfo = {
            location_province_id: path[0],
            location_city_id: path[1],
            location_district_id: undefined,
            location_address: `${pathLabels[0]}-${pathLabels[1]}` as any,
          }
        } else {
          locInfo = {
            location_province_id: path[0],
            location_city_id: path[1],
            location_district_id: path[2],
            location_address: `${pathLabels[0]}-${pathLabels[1]}-${pathLabels[2]}` as any,
          }
        }
      }

      const submitData = {
        ...formData,
        ...locInfo,
        phone_id: formDataClone.phone_id,
      }

      if (modalType.value === 'add') {
        await createSeaLeads(submitData, props.fromType)
      } else {
        await updateSeaLeads(formDataClone.id as string, submitData, props.fromType)
      }

      message.success(t('common.operationSuccess'))
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 重置表单
   * 清空表单数据
   */
  function handleReset() {
    resetFields()
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
    setFieldsValue,
  })
</script>
