<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="tag-container no-padding">
        <!-- 循环标签分类 -->
        <div v-for="(category, index) in leadTags?.list" :key="index" class="tag-section">
          <div class="tag-title text-text-deep-gray">{{ category.category_name }}</div>
          <n-space wrap>
            <n-checkbox-group v-model:value="selectedTagIds">
              <n-space item-style="display: flex;">
                <n-checkbox
                  v-for="(tag, tagIndex) in category.label_setting"
                  :key="tagIndex"
                  :value="tag.id"
                >
                  <template #default>
                    <div class="flex items-center">
                      <div
                        class="w-[10px] h-[10px] rounded-full"
                        :style="{
                          backgroundColor: tag.color,
                          border: `1px solid ${darkerBorder(tag.color)}`,
                        }"
                      ></div>
                      <div class="ml-[8px]">
                        {{ tag.name }}
                      </div>
                    </div>
                  </template>
                </n-checkbox>
              </n-space>
            </n-checkbox-group>
          </n-space>
        </div>

        <!-- 已选标签部分 -->
        <div class="selected-tags">
          <div class="selected-header">
            <span>
              {{ t('leads.common.selectedTags') }} ({{ selectedTags.length }})
              {{ t('leads.common.tag') }}
            </span>
            <n-button text type="primary" @click="clearAllTags">
              {{ t('leads.common.clear') }}
            </n-button>
          </div>
          <n-space wrap>
            <n-tag
              v-for="tag in selectedTags"
              :key="tag.id"
              size="small"
              :color="tag?.tageColor"
              closable
              @close="removeTag(tag)"
            >
              {{ tag.name }}
            </n-tag>
          </n-space>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { LabelsTagsType, SelectedTagsType } from '@/views/admin/leads/config/leadTypes'
  import { useMessage } from 'naive-ui'
  import { addLabelRelationship } from '@/api/admin/leads/seaLead'
  import { NSpace, NButton, NTag } from 'naive-ui'
  import { cloneDeep } from 'lodash-es'
  import tinycolor from 'tinycolor2'

  const message = useMessage()
  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])
  const { t } = useI18n()

  const leadTags = ref<LabelsTagsType>()

  const leadId = ref('')

  // 已选标签
  const selectedTags = ref<SelectedTagsType[]>([])

  const selectedTagIds = ref<number[]>([])

  const props = defineProps<{
    fromType: number
  }>()

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 700,
    fullscreen: false,
  })

  /**
   * 移除标签
   */
  function removeTag(tag: { id: number; name: string }) {
    const index = selectedTags.value.findIndex((item) => item.id === tag.id)
    if (index !== -1) {
      selectedTags.value.splice(index, 1)
    }
  }

  /**
   * 清空所有已选标签
   */
  function clearAllTags() {
    selectedTags.value = []
  }

  /**
   * 显示弹窗
   */
  async function showModal(id: string, tags: LabelsTagsType) {
    leadId.value = id
    leadTags.value = cloneDeep(tags)
    selectedTags.value = leadTags.value.selected_list.map((item) => ({
      ...item,
      tageColor: { color: item.color },
    }))
    selectedTagIds.value = leadTags.value.selected_list.map((item) => item.id)
    openModal(t('leads.common.manageTags'))
  }

  /**
   * 提交表单
   */
  async function formSubmit() {
    setSubLoading(true)
    // TODO: 调用API保存标签
    addLabelRelationship(1, leadId.value, selectedTagIds.value, props.fromType)
      .then(() => {
        message.success(t('common.operationSuccess'))
        emit('reload')
        closeModal()
      })
      .catch((err: any) => {
        message.error(err.message || t('common.operationFailed'))
      })
      .finally(() => {
        setSubLoading(false)
      })
  }
  const darkerBorder = (color) => {
    return tinycolor(color).darken(20).toString() // 加深10%
  }
  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>

<style scoped>
  .tag-container {
    /* padding: 16px; */
  }

  .tag-section {
    margin-bottom: 20px;
  }

  .tag-title {
    margin-bottom: 12px;
  }

  .selected-tags {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
  }

  .selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
</style>
