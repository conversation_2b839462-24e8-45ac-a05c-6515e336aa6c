import { ref, h } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage, NText } from 'naive-ui'
import { setValid } from '@/api/admin/leads/seaLead'
import { useDialog } from '@/hooks/web/useDialog'
import { colorMap } from '@/settings/designSetting'

interface UseLeadValidStatusProps {
  leadId: any
  getDetailData: () => void
  fromType: number
}

export function useLeadValidStatus({ leadId, getDetailData, fromType }: UseLeadValidStatusProps) {
  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()
  const isInvalid = ref(false)

  function handleInvalidChange() {
    let rAry: any[] = []
    if (!isInvalid.value) {
      rAry = [
        h(
          NText,
          {
            color: colorMap['title-deep-gray'],
            style: { marginLeft: '4px' },
          },
          { default: () => t('leads.common.confirmChangeInvalid') },
        ),
        h(
          NText,
          {
            type: 'primary',
            style: { marginLeft: '4px' },
          },
          { default: () => t('leads.common.invalid') + '?' },
        ),
      ]
    } else {
      rAry = [
        h(
          NText,
          {
            color: colorMap['title-deep-gray'],
            style: { marginLeft: '4px' },
          },
          { default: () => t('leads.common.confirm') },
        ),
        h(
          NText,
          {
            type: 'primary',
            style: { marginLeft: '4px' },
          },
          { default: () => t('common.cancelText') },
        ),
        h(
          NText,
          {
            color: colorMap['title-deep-gray'],
            style: { marginLeft: '4px' },
          },
          { default: () => t('leads.common.confirmChangeInvalidTips') },
        ),
      ]
    }

    dialog.warning({
      title: t('common.tipsText'),
      content: () => {
        return rAry
      },
      positiveText: t('common.okText'),
      negativeText: t('common.cancelText'),
      draggable: false,
      positiveButtonProps: {
        type: 'primary',
      },
      onPositiveClick: () => {
        setValid(leadId.value as string, fromType).then(() => {
          message.success(t('common.operationSuccess'))
          isInvalid.value = !isInvalid.value
          getDetailData()
        })
      },
    })
  }

  return {
    isInvalid,
    handleInvalidChange,
  }
}
