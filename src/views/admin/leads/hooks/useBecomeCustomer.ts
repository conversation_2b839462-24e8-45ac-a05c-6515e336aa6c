import { h } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useDialog } from '@/hooks/web/useDialog'
import { NText } from 'naive-ui'
import { becomeCustomer } from '@/api/admin/leads/seaLead'
import { colorMap } from '@/settings/designSetting'
export function useBecomeCustomer(id: string, fromType: number, hasKeyComleted: boolean) {
  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()

  function handleBecome(callback: () => void) {
    const rAry = [
      h(
        NText,
        {
          color: colorMap['title-deep-gray'],
        },
        {
          default: () =>
            hasKeyComleted
              ? '您正在进行转客户操作，当前线索将转为客户，您确定这样操作吗？'
              : '当前操作阶段尚有前置关键阶段未完成，尚不能操作当前阶段任务，请先完成前置阶段！',
        },
      ),
    ]

    const d = dialog.warning({
      title: t('common.tipsWarningText'),
      content: () => {
        return rAry
      },
      positiveText: hasKeyComleted ? t('common.okText') : t('common.tipsKnow'),
      negativeText: t('common.cancelText'),
      draggable: false,
      loading: false, // 显式初始化 loading 状态
      positiveButtonProps: {
        type: 'primary',
      },
      onPositiveClick: () => {
        if (hasKeyComleted) {
          if (d) {
            d.loading = true
          }

          // 提交领取线索
          return becomeCustomer(id, fromType)
            .then(() => {
              message.success(t('common.operationSuccess'))
              callback()
            })
            .catch((err) => {
              message.error(err.message || t('common.operationFailed') || '操作失败')
            })
            .finally(() => {
              if (d) {
                d.loading = false
              }
              callback()
            })
        }
      },
    })
  }

  return {
    handleBecome,
  }
}
