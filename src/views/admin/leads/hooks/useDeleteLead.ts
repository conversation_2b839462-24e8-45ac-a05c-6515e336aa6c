import { h, ref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useDialog } from '@/hooks/web/useDialog'
import { NText, NForm, NInput, NFormItem } from 'naive-ui'
import { batchDelLeads } from '@/api/admin/leads/seaLead'
import { colorMap } from '@/settings/designSetting'
import type { RequestBatchDelLeadsType } from '../config/leadTypes'

// 来自哪个页面（1：公海，2：部门，3：我的,4：回收站）
export function useDeleteLead(checkedIds: string[], formType: number) {
  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()
  const deleteReason = ref('')

  function handleDeleteLead(callback: () => void) {
    const rAry = [
      h(
        NText,
        {
          color: colorMap['title-deep-gray'],
        },
        { default: () => '已选择 ' },
      ),
      h(
        NText,
        {
          type: 'primary',
        },
        { default: () => checkedIds.length },
      ),
      h(
        NText,
        {
          color: colorMap['title-deep-gray'],
        },
        { default: () => ' 个线索，删除后当前线索将流入回收站中！' },
      ),
      h(
        NForm,
        {
          class: 'mt-[24px]',
        },
        h(
          NFormItem,
          {
            label: '删除原因',
            labelPlacement: 'top',
            labelWidth: 100,
          },
          h(NInput, {
            'on-input': (value: string) => (deleteReason.value = value),
            type: 'textarea',
            placeholder: '请输入删除原因',
          }),
        ),
      ),
    ]
    const d = dialog.warning({
      title: t('leads.common.deleteLeads'),
      content: () => {
        return rAry
      },
      positiveText: t('leads.common.deleteLeads'),
      negativeText: t('common.cancelText'),
      positiveButtonProps: {
        type: 'error',
      },
      draggable: false,
      loading: false, // 显式初始化 loading 状态
      onPositiveClick: () => {
        if (deleteReason.value.length === 0) {
          message.error(t('leads.common.deleteLeadReasonRequired'))
          return new Promise((resolve) => {
            resolve(false)
          })
        }

        const data: RequestBatchDelLeadsType = {
          from: formType,
          ids: checkedIds.map((item) => item.toString().split('@')[0]),
          reason: deleteReason.value,
        }

        if (d) {
          d.loading = true
        }

        // 实际的异步操作应该是这样：
        return batchDelLeads(data, formType)
          .then(() => {
            message.success(t('common.operationSuccess'))
            callback()
          })
          .catch((err) => {
            message.error(err.message || t('common.operationFailed') || '操作失败')
          })
          .finally(() => {
            if (d) {
              d.loading = false
            }
            callback()
          })
      },
    })
  }

  return {
    handleDeleteLead,
  }
}
