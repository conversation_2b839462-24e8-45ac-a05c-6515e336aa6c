import { h } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useDialog } from '@/hooks/web/useDialog'
import { NText } from 'naive-ui'
import { receiveLeads } from '@/api/admin/leads/seaLead'
import { colorMap } from '@/settings/designSetting'
export function useReceiveLead(checkedIds: string[]) {
  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()

  function handleReceive(callback: () => void) {
    const rAry = [
      h(
        NText,
        {
          color: colorMap['title-deep-gray'],
        },
        { default: () => '您确定领取当前这 ' },
      ),
      h(
        NText,
        {
          type: 'primary',
        },
        { default: () => checkedIds.length },
      ),
      h(
        NText,
        {
          color: colorMap['title-deep-gray'],
        },
        { default: () => ' 条线索吗？' },
      ),
    ]

    const d = dialog.warning({
      title: t('leads.common.receiveLeads'),
      content: () => {
        return rAry
      },
      positiveText: t('common.okText'),
      negativeText: t('common.cancelText'),
      draggable: false,
      loading: false, // 显式初始化 loading 状态
      positiveButtonProps: {
        type: 'primary',
      },
      onPositiveClick: () => {
        if (d) {
          d.loading = true
        }

        // 提交领取线索
        return receiveLeads({ ids: checkedIds.map((item) => item.toString().split('@')[0]) })
          .then(() => {
            message.success(t('common.operationSuccess'))
            callback()
          })
          .catch((err) => {
            message.error(err.message || t('common.operationFailed') || '操作失败')
          })
          .finally(() => {
            if (d) {
              d.loading = false
            }
            callback()
          })
      },
    })
  }

  return {
    handleReceive,
  }
}
