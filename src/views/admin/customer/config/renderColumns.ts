import { h } from 'vue'
import { NPopover } from 'naive-ui'

/**
 * 渲染线索标签
 * @param row
 * @returns
 */
export function renderLabelRelationship(row: any) {
  if (!row.label_relationship || !Array.isArray(row.label_relationship)) {
    return null
  }

  return h(
    NPopover,
    {
      trigger: 'hover',
      placement: 'top',
    },
    {
      trigger: () =>
        h(
          'div',
          { style: 'display: flex; gap: 4px; flex-wrap: no-wrap;' },
          row.label_relationship.map((item: any) => {
            if (!item.label_category) return null

            return h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item.label_category.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  width: 'auto',
                  textAlign: 'center',
                },
              },
              item.label_category.name,
            )
          }),
        ),
      default: () =>
        h(
          'div',
          { style: 'padding: 8px;' },
          row.label_relationship.map((item: any) =>
            h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item.label_category?.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  marginBottom: '4px',
                  textAlign: 'center',
                  display: 'inline-block',
                  marginRight: '4px',
                  width: 'auto',
                },
              },
              item.label_category?.name,
            ),
          ),
        ),
    },
  )
}

/**
 * 渲染客户信息
 * @param row
 * @returns
 */
export function renderCustomer(row: any) {
  return h(
    'div',
    {
      class: 'flex items-center cursor-pointer',
      onClick: () => {
        row._showCustomerDetail(row)
      },
    },
    [
      h(
        'div',
        {
          class:
            'text-[16px] text-white size-[32px] rounded-full bg-blue-dot flex items-center justify-center flex-shrink-0 mr-[8px]',
        },
        {
          default: () => row.customer_name[0],
        },
      ),
      h('div', { class: 'text-[14px]' }, [
        h(
          'div',
          {
            class: 'text-title-deep-gray',
          },
          {
            default: () => row.customer_name,
          },
        ),
        h(
          'div',
          {
            class: 'text-text-middle-gray',
          },
          {
            default: () => row.phone,
          },
        ),
      ]),
    ],
  )
}
