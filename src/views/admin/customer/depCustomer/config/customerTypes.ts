import type { Director } from '@/api/admin/customer/depCustomer'
export type IndustryType = {
  id: number
  industry_name: string
  status: number
}
export type CustomerLabelType = {
  id: number
  name: string
}
export type EntryMethodType = {
  key: number
  label: string
}
export type SourceType = {
  id: number
  source_name: string
  status: number
}
export type RegionType = {
  pid: number
  id: number
  name: string
  level: number
  children: RegionType[]
}
export type TransactionStatus = {
  key: number
  label: string
}
export type ExportListType = {
  value: string
  label: string
}
export type FollowStatusType = {
  id: number
  name: string
}
export type ListAllType = {
  // 所属行业
  industryList: IndustryType[]
  //标签列表
  customerLabelList: CustomerLabelType[]
  //所在地址
  regionList: RegionType[]
  //渠道来源
  sourceList: SourceType[]
  //导入方法
  entryMethodsList: EntryMethodType[]
  //转化方式
  transactionStatusList: TransactionStatus[]
  // 跟进状态
  followStatusList?: FollowStatusType[]
  // 负责人列表
  assignPeopleList?: Director[]
}
