<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6">
        <div style="margin-bottom: 12px">确定将当前客户退回至线索公海池？</div>
        <div class="mt-6">
          <BasicForm ref="basicFormRef" @register="register" />
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { detailReturnLead } from '@/api/admin/customer/depCustomer'
  import { useMessage } from 'naive-ui'
  import { getReturnLeadSchema } from '../config/schemas'
  import { BasicForm, useForm } from '@/components/Form/index'

  const message = useMessage()
  const { t } = useI18n()

  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])

  const customerIds = ref<number[]>([])

  /**
   * 表单引用
   */
  const basicFormRef = ref(null)

  /**
   * 动态计算表单配置
   */
  const formSchemas = computed(() => getReturnLeadSchema())

  /**
   * 注册表单
   */
  const [register, { submit }] = useForm({
    gridProps: { cols: 1 },
    layout: 'horizontal',
    labelWidth: 'auto',
    showActionButtonGroup: false,
    schemas: formSchemas,
    requireMarkPlacement: 'left',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 600,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  function showModal(ids: number[]) {
    customerIds.value = ids
    openModal(t('customer.common.customerReturn'))
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return
      console.log('表单返回值：', formData)
      formData.ids = customerIds.value
      await detailReturnLead(formData)

      message.success(t('common.operationSuccess'))
      emit('reload')
      closeModal()
    } catch (error) {
      message.error('退回失败')
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
