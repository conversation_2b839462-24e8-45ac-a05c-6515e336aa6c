<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <BasicForm ref="basicFormRef" @register="register" />
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { addFollowRecord } from '@/api/admin/customer/myCustomer'
  import { useMessage } from 'naive-ui'
  import { followRecordSchemas } from '../config/schemas'
  import { BasicForm, useForm } from '@/components/Form/index'

  const message = useMessage()
  const { t } = useI18n()
  const props = defineProps({
    fromType: {
      type: Number as PropType<number>,
      default: 0,
    },
  })
  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   */
  const emit = defineEmits(['reload'])

  const custmoerId = ref<number | null>(null)

  /**
   * 表单引用
   */
  const basicFormRef = ref(null)

  /**
   * 动态计算表单配置
   */
  const formSchemas = computed(() => followRecordSchemas())

  /**
   * 注册表单
   */
  const [register, { submit }] = useForm({
    gridProps: { cols: 1 },
    layout: 'horizontal',
    labelWidth: 'auto',
    showActionButtonGroup: false,
    schemas: formSchemas,
    requireMarkPlacement: 'left',
    labelPlacement: 'top',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   */
  function showModal(id: number) {
    custmoerId.value = id
    openModal(t('customer.common.addFollowRecord'))
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return
      const params = {
        type: 2,
        link_id: custmoerId.value || 0,
        content: formData.follow_up_content,
        follow_up_at: formData.follow_up_at,
        scene: 'add',
      }
      await addFollowRecord(params, props.fromType)
      message.success(t('common.operationSuccess'))
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error?.message)
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
