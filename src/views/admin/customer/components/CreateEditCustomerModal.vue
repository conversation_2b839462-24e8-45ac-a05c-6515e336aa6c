<template>
  <basicModal
    preset="dialog"
    ref="basicModalRef"
    :maskClosable="false"
    @register="modalRegister"
    @on-ok="formSubmit"
  >
    <template #default>
      <div class="mt-6 no-padding">
        <BasicForm ref="basicFormRef" @register="register" @reset="handleReset">
          <template #sourceWithAccount="{ model, field }">
            <div style="display: flex; gap: 12px; width: 100%">
              <n-select
                v-model:value="model.source"
                :options="props.listAll.sourceList"
                label-field="source_name"
                value-field="id"
                style="width: 120px"
                @update:value="(val) => field({ source: val })"
              />
              <n-input
                v-model:value="model.source_account"
                style="flex: 1"
                @update:value="(val) => field({ source_account: val })"
              />
            </div>
          </template>
        </BasicForm>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, nextTick, computed, ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { getSchema } from '../config/schemas'
  import { BasicForm, useForm } from '@/components/Form/index'
  import { basicModal, useModal } from '@/components/Modal'
  import type { ListAllType } from '../config/customerTypes'
  import { addCustomer, updateCustomerInfo } from '@/api/admin/customer/myCustomer'
  import { findParentPathById } from '@/utils'

  /**
   * 表单数据类型定义
   */
  interface formParamsType {
    id?: number
    name: string
    status: number
    remark: string
  }

  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   * @property {Function} register - 组件注册时触发
   */
  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()
  let customerId = ref()

  /**
   * 组件属性
   */
  const props = defineProps({
    isEdit: Boolean,
    listAll: {
      type: Object as PropType<ListAllType>,
      default: () => ({}),
    },
  })

  /**
   * 表单引用
   */
  const basicFormRef = ref(null)

  /**
   * 动态计算表单配置
   */
  const formSchemas = computed(() => getSchema(props.listAll, props.isEdit))

  /**
   * 注册表单
   */
  const [register, { submit, setFieldsValue, resetFields }] = useForm({
    gridProps: { cols: 2, xGap: 32 },
    layout: 'horizontal',
    labelWidth: 'auto',
    showActionButtonGroup: false,
    schemas: formSchemas,
    requireMarkPlacement: 'left',
    labelPlacement: 'top',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 724,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   * @param record - 表单数据，用于编辑模式
   */
  function showModal(title?: string, record?: formParamsType) {
    openModal(title)
    if (record) {
      customerId.value = record.id
      getInfo(record)
    }
  }

  /**
   * 获取详情
   * 打开弹窗并设置表单值
   * @param record - 表单数据
   */
  function getInfo(record) {
    nextTick(() => {
      setFieldsValue(record)
    })
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return
      console.log('表单返回值：', formData)
      let locInfo = {}

      if (formData.location_array) {
        const { path, pathLabels }: any = findParentPathById(
          formData.location_array,
          props.listAll.regionList || [],
          {
            keyField: 'id',
            childrenField: 'children',
            labelField: 'name',
          },
        )

        if (path.length === 2) {
          locInfo = {
            location_province_id: path[0],
            location_city_id: path[1],
            location_district_id: undefined,
            location_address: `${pathLabels[0]}-${pathLabels[1]}` as any,
          }
        } else {
          locInfo = {
            location_province_id: path[0],
            location_city_id: path[1],
            location_district_id: path[2],
            location_address: `${pathLabels[0]}-${pathLabels[1]}-${pathLabels[2]}` as any,
          }
        }
      }
      const submitData = {
        ...formData,
        ...locInfo,
      }
      if (props.isEdit) {
        await updateCustomerInfo(formData.id, submitData)
      } else {
        delete formData.phone_id
        delete formData.id
        await addCustomer(submitData)
      }

      message.success(props.isEdit ? t('common.editSuccess') : t('common.addSuccess'))
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 重置表单
   * 清空表单数据
   */
  function handleReset() {
    resetFields()
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
    setFieldsValue,
  })
</script>
