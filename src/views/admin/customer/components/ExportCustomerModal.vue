<template>
  <basicModal preset="dialog" ref="basicModalRef" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="no-padding">
        <n-form
          ref="formRef"
          label-placement="left"
          :label-width="100"
          :model="formValue"
          :rules="rules"
        >
          <div class="flex flex-row justify-items-center justify-between w-full mb-6 mt-6">
            <div class="text-title-deep-gray">请选择导出信息</div>
            <div>
              <n-checkbox
                value="all"
                label="全选"
                :indeterminate="
                  formValue.allFields.length > 0 && formValue.allFields.length < exportFields.length
                "
                @update:checked="handleAllChecked"
              />
            </div>
          </div>
          <n-checkbox-group v-model:value="formValue.allFields" @update:value="handleUpdateValue">
            <n-grid x-gap="8" :cols="4">
              <n-gi v-for="item in exportFields" :key="item.value">
                <n-checkbox
                  :value="item.value"
                  :label="item.label"
                  :checked="formValue.allFields.includes(item.value)"
                />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form>
      </div>
    </template>
    <template #action>
      <n-button type="primary" :loading="isLoading" @click="formSubmit">导出</n-button>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, h } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { getExportFields, generateExportTask } from '@/api/admin/customer/myCustomer'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()
  const props = defineProps({
    exortParams: {
      type: Object,
      default: () => ({}),
    },
    fromType: {
      type: Number,
      default: 1,
    },
  })

  const isLoading = ref(false)

  const [modalRegister, { openModal, closeModal }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 600,
    fullscreen: true,
  })
  const exportFields = ref<{ label: string; value: string }[]>([])

  /**
   * 单选
   * @param value
   */
  function handleUpdateValue(value: string[]) {
    formValue.value.allFields = value
  }
  /**
   * 全选
   */
  function handleAllChecked(value: boolean) {
    formValue.value.allFields = value ? exportFields.value.map((item) => item.value) : []
    console.log(formValue.value.allFields)
  }

  const formRef = ref(null)
  const formValue = ref({
    allFields: [] as string[],
  })
  const rules = ref({
    allFields: [{ required: true, message: t('leads.common.exportInfo') }],
  })

  function showModal(title?: string) {
    exportFields.value = []
    openModal(title)
    getExportFields(props.fromType).then((res) => {
      const { fields } = res
      // 遍历fields对象
      Object.keys(fields).forEach((key) => {
        exportFields.value.push({
          label: fields[key],
          value: key,
        })
      })
    })
  }

  async function formSubmit() {
    try {
      isLoading.value = true
      await generateExportTask(
        {
          checked: formValue.value.allFields,
          key: 'customer',
          filter: props.exortParams,
        },
        props.fromType,
      )
      isLoading.value = false

      message.success(
        () =>
          h('span', null, [
            '任务已提交，请在',
            h(
              'a',
              {
                class: 'text-primary',
                style: 'text-decoration: underline; cursor: pointer;',
                onClick: () => {
                  router.push({ name: 'home_downloadCenter' })
                },
              },
              '【下载中心】',
            ),
            '中查看',
          ]),
        { duration: 3000 },
      )
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
      isLoading.value = false
    }
  }

  defineExpose({
    showModal,
    closeModal,
  })
</script>
