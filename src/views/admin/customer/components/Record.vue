<template>
  <div :bordered="false">
    <BasicForm
      :showResetButton="false"
      :showAdvancedButton="false"
      ref="basicFormRef"
      @register="register"
      @submit="handleSubmit"
    />
    <n-timeline>
      <n-timeline-item v-for="(record, index) in recordList" :key="index" :time="record.created_at">
        <div>{{ record.creator }}</div>
        <div>{{ record.operate_content }} · {{ record.creator_role }}</div>
      </n-timeline-item>
    </n-timeline>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, computed } from 'vue'
  import { NTimeline, NTimelineItem } from 'naive-ui'
  import { getCustomerRecord, CustomerRecord } from '@/api/admin/customer/myCustomer'
  import { BasicForm, useForm } from '@/components/Form'
  import { recordSearchSchemas } from '../config/schemas'
  const recordList = ref<CustomerRecord[]>([])

  /**
   * 查询表单注册
   * 初始化查询表单配置
   */
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:2 xl:2 2xl:4' },
    labelWidth: 100,
    schemas: computed(() => recordSearchSchemas()),
  })

  /**
   * 组件属性
   */
  const props = defineProps({
    customerId: String,
    fromType: Number,
  })

  function handleSubmit() {
    handleCustomerRecord()
  }

  async function handleCustomerRecord() {
    const formParams = getFieldsValue()
    const res = await getCustomerRecord(
      { ...formParams, link_id: props.customerId || 0 },
      props.fromType || 0,
    )
    recordList.value = res.list
  }

  onMounted(async () => {
    await handleCustomerRecord()
  })
</script>

<style scoped lang="scss">
  .n-timeline {
    margin: 0 0 0 10px;
  }
  .n-timeline-item-content {
    padding: 0;
  }
</style>
