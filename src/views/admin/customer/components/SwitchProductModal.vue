<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="no-padding">
        <div>
          <n-input
            class="w-[320px]"
            v-model:value="searchValue"
            placeholder="请输入产品名称"
            @keyup.enter="handleSearch"
            clearable
          >
            <template #suffix>
              <n-icon @click="handleSearch" style="cursor: pointer">
                <SearchOutlined />
              </n-icon>
            </template>
          </n-input>
        </div>
        <BasicTable
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row) => row.id"
          :showTableSetting="false"
          height="700px"
          ref="tableRef"
          :autoScrollX="true"
          @update:checked-row-keys="handleCheck"
        >
          <template #empty>
            <EmptyBlock icon="msg" emptyText="暂无产品" />
          </template>
        </BasicTable>
        <div class="mt-4" v-if="globalSelectedProducts.length > 0">
          <div class="mb-2 text-sm text-gray-600" v-if="props.showSelectCount !== false">
            已选择 {{ globalSelectedProducts.length }} 个产品
            <span v-if="props.maxSelectCount && props.maxSelectCount > 0">
              / 最多 {{ props.maxSelectCount }} 个
            </span>
            ：
          </div>
          <n-space>
            <n-tag
              v-for="item in globalSelectedProducts"
              :key="item.id"
              type="primary"
              closable
              @close="removeSelectedProduct(item.id)"
            >
              {{ item.product_name }}
            </n-tag>
          </n-space>
          <div class="mt-2">
            <n-button size="small" type="warning" @click="clearAllSelected">清空所有选择</n-button>
          </div>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { BasicColumn, BasicTable } from '@/components/Table'
  import { getProductList, type ProductItem } from '@/api/admin/product/product'
  import { SearchOutlined } from '@vicons/antd'
  const { t } = useI18n()

  /**
   * 组件属性
   */
  const props = defineProps<{
    /** 预选的产品列表，用于回显已选择的产品 */
    preSelectedProducts?: ProductItem[]
    /** 最大选择数量限制，0表示无限制 */
    maxSelectCount?: number
    /** 是否显示选择数量统计 */
    showSelectCount?: boolean
  }>()

  /**
   * 组件事件
   * @property {Function} reload - 产品选择完成后触发，传递选中的产品数据
   */
  const emit = defineEmits<{
    selectProduct: [data: { productIds: number[]; products: ProductItem[] }]
  }>()

  const custmoerId = ref<number | null>(null)
  const tableRef = ref()

  const columns: BasicColumn[] = [
    {
      type: 'selection',
    },
    {
      title: t('product.common.productName'),
      key: 'product_name',
      align: 'center',
    },
    {
      title: '产品介绍',
      key: 'product_detail',
      align: 'center',
    },
    {
      title: t('product.common.productCategory'),
      key: '[product_category.category_name]',
      align: 'center',
    },
    {
      title: t('product.common.productPrice'),
      key: 'product_price',
      align: 'center',
    },
    {
      title: t('product.common.productUnit'),
      width: 80,
      key: 'product_unit',
      align: 'center',
      render: (row) => {
        return row.product_unit == 1 ? '套' : '件'
      },
    },
  ]

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 900,
    fullscreen: true,
  })

  // 存储表格数据
  const tableData = ref<ProductItem[]>([])
  const checkedIds = ref<number[]>([]) // 当前页勾选的ID列表

  // 全局选择的产品列表（跨分页）
  const globalSelectedProducts = ref<ProductItem[]>([])

  // 计算属性：获取全局选择的产品ID列表
  const globalSelectedIds = computed(() => {
    return globalSelectedProducts.value.map((product) => product.id)
  })

  // 计算属性：当前页面应该勾选的ID（基于全局选择）
  const currentPageCheckedIds = computed(() => {
    return tableData.value
      .filter((product) => globalSelectedIds.value.includes(product.id))
      .map((product) => product.id)
  })

  /**
   * 表单提交
   */
  const formSubmit = () => {
    console.log('全局选择的产品ID:', globalSelectedIds.value)
    console.log('全局选择的产品数据:', globalSelectedProducts.value)

    // 传递全局选择的产品数据给父组件
    emit('selectProduct', {
      productIds: globalSelectedIds.value,
      products: globalSelectedProducts.value,
    })

    closeModal()
  }

  /**
   * 显示弹窗
   */
  function showModal(id: number, preSelectedProducts?: ProductItem[]) {
    custmoerId.value = id

    // 优先使用方法参数传入的预选产品，其次使用props中的预选产品
    const selectedProducts = preSelectedProducts || props.preSelectedProducts || []

    // 设置全局选择（深拷贝避免引用问题）
    globalSelectedProducts.value =
      selectedProducts.length > 0 ? JSON.parse(JSON.stringify(selectedProducts)) : []

    // 清空当前页勾选状态
    checkedIds.value = []
    openModal(t('product.common.addProduct'))
  }

  /**
   * 批量勾选处理
   */
  function handleCheck(ids: number[]) {
    checkedIds.value = ids

    // 获取当前页面的产品数据
    const currentPageProducts = tableData.value

    // 获取当前页面新选中的产品（排除已在全局选择中的）
    let newSelectedProducts = currentPageProducts.filter(
      (product) => ids.includes(product.id) && !globalSelectedIds.value.includes(product.id),
    )

    // 获取当前页面取消选中的产品ID
    const currentPageProductIds = currentPageProducts.map((p) => p.id)
    const deselectedIds = currentPageProductIds.filter(
      (id) => globalSelectedIds.value.includes(id) && !ids.includes(id),
    )

    // 检查最大选择数量限制
    if (props.maxSelectCount && props.maxSelectCount > 0) {
      const currentSelectedCount = globalSelectedProducts.value.length - deselectedIds.length
      const maxNewSelections = props.maxSelectCount - currentSelectedCount

      if (newSelectedProducts.length > maxNewSelections) {
        // 超出限制，只选择允许的数量
        newSelectedProducts = newSelectedProducts.slice(0, maxNewSelections)

        // 更新当前页勾选状态，移除超出限制的选择
        const allowedNewIds = newSelectedProducts.map((p) => p.id)
        const finalCheckedIds = [
          ...ids.filter((id) => globalSelectedIds.value.includes(id) || allowedNewIds.includes(id)),
        ]
        checkedIds.value = finalCheckedIds

        // 同步表格勾选状态
        setTimeout(() => {
          tableRef.value?.setCheckedRowKeys(finalCheckedIds)
        }, 50)

        // 提示用户
        console.warn(`最多只能选择 ${props.maxSelectCount} 个产品`)
      }
    }

    // 更新全局选择：添加新选中的，移除取消选中的
    globalSelectedProducts.value = [
      ...globalSelectedProducts.value.filter((product) => !deselectedIds.includes(product.id)),
      ...newSelectedProducts,
    ]
  }

  /**
   * 搜索处理
   */
  function handleSearch() {
    // 清空当前页勾选状态（不清空全局选择）
    checkedIds.value = []
    // 刷新表格数据
    tableRef.value?.reload()
  }

  /**
   * 移除选中的产品
   */
  function removeSelectedProduct(productId: number) {
    // 从全局选择中移除
    globalSelectedProducts.value = globalSelectedProducts.value.filter(
      (product) => product.id !== productId,
    )

    // 如果当前页面有这个产品，也要取消勾选
    if (checkedIds.value.includes(productId)) {
      checkedIds.value = checkedIds.value.filter((id) => id !== productId)
      // 更新表格勾选状态
      tableRef.value?.setCheckedRowKeys(checkedIds.value)
    }
  }

  /**
   * 清空所有选择
   */
  function clearAllSelected() {
    globalSelectedProducts.value = []
    checkedIds.value = []
    // 清空表格勾选状态
    tableRef.value?.setCheckedRowKeys([])
  }

  const formParams = ref({
    scene: 'customer_detail_page',
  })
  const searchValue = ref('')

  /**
   * 加载数据表格数据
   * @param params - 请求参数
   * @returns 返回产品列表数据
   */
  const loadDataTable = async (params: any) => {
    // 合并搜索参数
    const searchParams = searchValue.value ? { product_name: searchValue.value } : {}
    const res = await getProductList({
      ...params,
      ...formParams.value,
      ...searchParams,
    })
    // 保存表格数据用于后续获取勾选的完整数据
    tableData.value = res.list || []

    // 数据加载完成后，自动回选当前页面中已在全局选择中的产品
    setTimeout(() => {
      const shouldCheckedIds = currentPageCheckedIds.value
      if (shouldCheckedIds.length > 0) {
        checkedIds.value = shouldCheckedIds
        // 设置表格的勾选状态
        tableRef.value?.setCheckedRowKeys(shouldCheckedIds)
      }
    }, 100)

    return res
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
