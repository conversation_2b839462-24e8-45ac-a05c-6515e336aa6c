<template>
  <basicModal preset="dialog" ref="basicModalRef" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <BasicForm ref="basicFormRef" @register="register" @reset="handleReset">
          <template #is_key="{ model, field, schema }">
            <n-radio-group v-model:value="model[field]" name="status">
              <n-space>
                <n-radio
                  v-for="song in schema.componentProps.options"
                  :key="song.value"
                  :value="song.value"
                >
                  {{ song.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </template>
        </BasicForm>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { onMounted, nextTick, computed, ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { getStageSchema } from '../config/schemas'
  import { BasicForm, useForm } from '@/components/Form'
  import { basicModal, useModal } from '@/components/Modal'
  import { editStage, createStage } from '@/api/admin/sopSetting/sopSetting'

  /**
   * 组件事件
   * @property {Function} reload - 表单提交成功后触发刷新
   * @property {Function} register - 组件注册时触发
   */
  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()

  /**
   * 组件属性
   */
  const props = defineProps({
    isEdit: Boolean,
    stageId: Number,
    show: Boolean,
    type: Number,
  })

  /**
   * 表单引用
   */
  const basicFormRef = ref(null)

  /**
   * 动态计算表单配置
   */
  const formSchemas = computed(() => getStageSchema())

  /**
   * 注册表单
   */
  const [register, { submit, setFieldsValue, resetFields }] = useForm({
    gridProps: { cols: 1 },
    layout: 'horizontal',
    showActionButtonGroup: false,
    schemas: formSchemas,
    requireMarkPlacement: 'left',
    labelPlacement: 'top',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  /**
   * 显示弹窗
   * @param record - 表单数据，用于编辑模式
   */
  function showModal(title?: string, record?: any) {
    openModal(title)
    if (record) {
      getInfo(record)
    }
  }

  /**
   * 获取详情
   * 打开弹窗并设置表单值
   * @param record - 表单数据
   */
  function getInfo(record) {
    nextTick(() => {
      setFieldsValue(record)
    })
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return
      formData.type = props.type
      console.log('表单返回值：', formData)

      if (props.isEdit) {
        await editStage(formData.id, formData)
      } else {
        delete formData.id
        await createStage(formData)
      }

      message.success(props.isEdit ? t('common.editSuccess') : t('common.addSuccess'))
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  /**
   * 重置表单
   * 清空表单数据
   */
  function handleReset() {
    resetFields()
  }

  onMounted(() => {
    // 组件挂载时的初始化操作
  })

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
