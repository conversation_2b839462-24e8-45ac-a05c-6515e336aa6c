<template>
  <basicModal
    preset="dialog"
    ref="basicModalRef"
    @register="modalRegister"
    @on-ok="handleAssignRoles"
  >
    <template #default>
      <div class="mt-6 no-padding mb-[24px]">
        <div class="flex items-start">
          <img :src="userIcon" class="w-[68px] h-[68px] mr-[12px]" />
          <div>
            <div class="text-[16px] mb-[16px]">
              {{ localRoleForm.username }}
            </div>
            <div class="text-[#00000073] mb-[8px]">
              <span>员工姓名：</span>
              {{ localRoleForm.employee_name }}
            </div>
            <div class="text-[#00000073] mb-[32px]">
              <span>所属部门：</span>
              {{ localRoleForm.department_link }}
            </div>
          </div>
        </div>
        <div class="flex items-center mb-[8px]">
          <span>所属角色：</span>
          <n-cascader
            class="w-[336px] ml-[10px]"
            v-model:value="localRoleForm.role_id"
            :options="props.userRoles"
            :placeholder="t('common.chooseText')"
            :multiple="false"
            value-field="id"
            label-field="role_name"
            clearable
          />
        </div>
        <div class="ml-[80px] text-[red]" v-if="showErrorMessage">
          {{ t('permissions.common.userDepartmentRequired') }}
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useMessage } from 'naive-ui'
  import { basicModal, useModal } from '@/components/Modal'
  import { setRole } from '@/api/admin/permissions/user'
  import userIcon from '@/assets/images/admin/icon_role.png'
  import { Role } from '@/api/admin/permissions/roles'

  const emit = defineEmits(['reload', 'register'])
  const { t } = useI18n()
  const message = useMessage()
  const showErrorMessage = ref(false)
  const basicModalRef = ref()
  const localRoleForm = ref()

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })
  /**
   * 显示弹窗
   * @param record - 表单数据，用于编辑模式
   */
  function showModal(title?: string, record?: any) {
    openModal(title)
    if (record) {
      localRoleForm.value = {
        ...record,
        role_id: record.roles[0].id,
      }
    }
  }

  const props = defineProps<{
    title?: string
    roleForm?: any
    userRoles?: Role[]
  }>()

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function handleAssignRoles() {
    if (localRoleForm.value.role_id) {
      setSubLoading(true)
      const params = {
        user_id: localRoleForm.value.id,
        role_id: localRoleForm.value.role_id,
      }
      await setRole(params)
      message.success(t('common.operationSuccess'))
      emit('reload')
      closeModal()
    } else {
      setSubLoading(false)
      showErrorMessage.value = true
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
