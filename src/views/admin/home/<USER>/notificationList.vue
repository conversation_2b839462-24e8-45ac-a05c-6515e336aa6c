<template>
  <n-card :bordered="false" class="min-h-[calc(100vh-92px)]" title="站内信">
    <div class="flex justify-between">
      <div class="w-[282px] border-solid border-color-[#E1E1E1] border rounded-md h-auto">
        <n-tabs
          v-model:value="tab_type"
          type="segment"
          size="small"
          animated
          @update:value="handleChangeTab"
        >
          <n-tab-pane
            class="p-0"
            v-for="smPanel in smallPanels"
            :key="smPanel.type"
            :tab="smPanel.label"
            :name="smPanel.type"
          />
        </n-tabs>
      </div>
      <div>
        <n-button
          :disabled="btnDisabled"
          :bordered="false"
          class="mr-[16px] bg-[#FFE9E7] text-[#ED4A3F]"
          :class="btnDisabled ? 'opacity-[0.5]' : 'opacity-[1]'"
          @click="handleBatchDeleteMessage"
        >
          {{ t('home.common.batchDelete') }}
        </n-button>
        <n-button :disabled="btnDisabled" type="primary" @click="handleBatchMarkAsRead">
          {{ t('home.common.batchMarkAsRead') }}
        </n-button>
      </div>
    </div>
    <template v-if="notificationList.length > 0">
      <n-space vertical class="mt-[16px]">
        <n-checkbox-group v-model:value="checkIds">
          <div
            v-for="item in notificationList"
            :key="item.id"
            class="cursor-pointer"
            :class="[
              'hover:bg-[#fafbfc]',
              checkIds.includes(item.id) ? 'bg-[#F3F6FD]' : '',
              activeId === item.id
                ? 'border border-l-[6px] border-[#1C61F6] bg-[#F3F6FD]'
                : 'border-b border-[#e9e9e9]',
            ]"
            @click="handleView(item)"
          >
            <div
              class="flex justify-between items-center px-[12px] py-[16px]"
              :class="activeId === item.id ? 'pl-[6px] pt-[15px]' : 'pl-[12px]'"
            >
              <div class="flex items-center justify-start">
                <!-- 阻止勾选框点击冒泡 -->
                <n-checkbox :value="item.id" @click.stop />
                <img :src="IconNews" class="w-[48px] h-[48px] rounded-full mx-[12px]" />
                <div class="flex flex-col">
                  <div class="flex items-center">
                    <n-badge dot v-if="item.read_status == 1" />
                    <div>
                      <span class="text-[#000000d9] text-[16px] ml-[4px] mr-[12px]">
                        {{ item.title }}
                      </span>
                      <span class="text-[#00000073] text-[14px]">{{ item.created_at }}</span>
                    </div>
                  </div>

                  <div class="text-[#00000073] text-[14px] max-w-[770px] ellipsis-2">
                    发件人：{{ item.sender }} | 内容: {{ item.content }}
                  </div>
                </div>
              </div>
              <div class="text-[#1C61F6] cursor-pointer">
                <span class="mr-[16px]" @click.stop="handleView(item)">
                  {{ t('home.common.view') }}
                </span>
                <span @click.stop="handleDeleteMessage(item.id)">
                  {{ t('home.common.delete') }}
                </span>
              </div>
            </div>
          </div>
        </n-checkbox-group>
      </n-space>
      <div class="flex justify-end items-center my-[16px] text-[#000000d9] text-[12px]">
        共 {{ total }} 条
        <n-pagination
          v-model:page="page"
          v-model:page-size="pageSize"
          :item-count="total"
          :show-total="true"
          show-size-picker
          show-quick-jumper
          :page-sizes="[10, 20, 30, 40, 50]"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </template>
    <div v-else class="h-[calc(100vh-300px)] flex items-center justify-center">
      <EmptyBlock icon="msg" emptyText="暂无通知内容" />
    </div>

    <DetailDrawer ref="DetailDrawerRef" @reload="clearCheckId()" />
  </n-card>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import {
    getInternalNotifyList,
    markAllNotificationRead,
    batchDelInternalNotifyLogs,
    delInternalNotifyLogs,
    NotifyItem,
  } from '@/api/admin/home/<USER>'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDeleteDialog } from '@/hooks/useDeleteDiolog'
  import DetailDrawer from './DetailDrawer.vue'
  import { EmptyBlock } from '@/components/Empty'
  import IconNews from '@/assets/images/admin/icon_news_67.png'

  const { t } = useI18n()
  const DetailDrawerRef = ref()
  const { showDeleteDialog } = useDeleteDialog()
  const tab_type = ref('all')
  const notificationList = ref<NotifyItem[]>([])
  const message = useMessage()
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const activeId = ref<number | null>(null)
  const checkIds = ref<number[]>([])
  const btnDisabled = computed(() => checkIds.value.length <= 0)
  const smallPanels = ref([
    {
      label: '全部',
      type: 'all',
    },
    {
      label: '未读',
      type: 'unread',
    },
    {
      label: '已读',
      type: 'read',
    },
  ])

  // 切换tab
  async function handleChangeTab() {
    try {
      let params: any = {
        page: page.value,
        limit: pageSize.value,
      }
      if (tab_type.value !== 'all') {
        params.read_status = tab_type.value === 'unread' ? 1 : 2
      }
      const res = await getInternalNotifyList(params)
      notificationList.value = res.list
      total.value = res.row_count
    } catch (error: any) {
      message.error(error.message)
    }
  }

  //批量删除
  async function handleBatchDelete(ids: number[]) {
    try {
      await batchDelInternalNotifyLogs(ids)
      handleChangeTab()
      checkIds.value = []
    } catch (error: any) {
      message.error(error.message)
    }
  }

  //批量标记已读
  async function handleBatchMarkAsRead() {
    try {
      await markAllNotificationRead(checkIds.value)
      handleChangeTab()
      checkIds.value = []
    } catch (error: any) {
      message.error(error.message)
    }
  }

  // 删除
  async function handleDelete(id: number) {
    try {
      await delInternalNotifyLogs(id)
      handleChangeTab()
    } catch (error: any) {
      message.error(error.message)
    }
  }

  function handleDeleteMessage(id: number) {
    showDeleteDialog(`删除后站内信信息将无法恢复，您确定删除这 1 条站内信吗?`, () =>
      handleDelete(id),
    )
  }

  function handleBatchDeleteMessage() {
    showDeleteDialog(
      `删除后站内信信息将无法恢复，您确定删除这 ${checkIds.value.length} 条站内信吗?`,
      () => handleBatchDelete(checkIds.value),
    )
  }

  function handleView(item: NotifyItem) {
    activeId.value = item.id
    DetailDrawerRef.value?.showDrawer(item.id)
  }

  function clearCheckId() {
    console.log('clearCheckId')

    activeId.value = null
  }

  // 分页相关事件
  function handlePageChange(newPage: number) {
    page.value = newPage
    handleChangeTab()
  }

  // 切换条数
  function handlePageSizeChange(newSize: number) {
    pageSize.value = newSize
    page.value = 1 // 切换条数时重置为第一页
    handleChangeTab()
  }

  onMounted(() => {
    handleChangeTab()
  })
</script>

<style scoped>
  .ellipsis-2 {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
</style>
