<template>
  <n-drawer v-model:show="active" :block-scroll="false" width="1050px" placement="right">
    <n-drawer-content :title="t('home.common.detail')" closable>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <SvgIcon xlink-href="#icon-pop_news" class="h-[20px] w-[20px]" aria-hidden="true" />
            <span class="text-[16px] ml-[4px]">
              {{ t('home.common.notificationDetail') }}
            </span>
          </div>
        </div>
      </template>
      <div
        class="flex items-center justify-start pt-[8px] pb-[24px] w-full border-b border-[#e9e9e9]"
      >
        <img :src="IconNews" class="w-[67px] h-[67px] rounded-full mr-[16px]" />
        <div class="flex flex-col">
          <div class="text-[16px] text-[#000000d9] mb-[8px]">{{ notificationDetail?.title }}</div>
          <div>
            <span class="text-[14px] text-[#00000073]">
              {{ notificationDetail?.sender }} | 发件时间：{{ notificationDetail?.created_at }}
            </span>
          </div>
        </div>
      </div>
      <div class="text-[#3D3D3D] text-[14px] py-[24px]">
        {{ notificationDetail?.content }}
      </div>
    </n-drawer-content>
  </n-drawer>
</template>
<script setup lang="ts">
  import { ref, watch } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { getInternalNotifyDetail, NotifyItem } from '@/api/admin/home/<USER>'
  import IconNews from '@/assets/images/admin/icon_news_67.png'

  const { t } = useI18n()
  const active = ref(false)
  const notificationId = ref(0)
  const notificationDetail = ref<NotifyItem>()
  const emit = defineEmits(['reload'])
  async function showDrawer(id: number) {
    notificationId.value = id
    // 获取站内信详情
    notificationDetail.value = await getInternalNotifyDetail(id)
    active.value = true
  }
  // 关闭线索详情
  function closeDrawer() {
    emit('reload')
  }
  watch(active, (val) => {
    if (!val) {
      closeDrawer()
    }
  })
  defineExpose({
    showDrawer,
    closeDrawer,
  })
</script>
<style scoped lang="scss"></style>
