<template>
  <n-drawer
    v-model:show="active"
    :block-scroll="false"
    :mask-closable="false"
    :show-mask="false"
    :on-after-leave="handleAfterLeave"
    width="900"
    class="transition-all duration-300"
    placement="right"
  >
    <n-drawer-content title="产品详情" closable>
      <template #header>
        <div class="flex items-center justify-between text-[16px] text-title-deep-gray">
          <div class="flex items-center">
            <SvgIcon xlinkHref="#icon-pop_product" width="18" height="18" class="mr-[5px]" />
            <div>产品详情</div>
          </div>
          <div>
            <n-switch
              :rubber-band="false"
              :value="productInfo.is_sale === 1 ? true : false"
              :loading="isLoading"
              size="small"
              @update:value="handleUpdateValue"
            />
            <span class="ml-[6px] mr-[16px]">
              {{ productInfo.is_sale === 1 ? '上架' : '下架' }}
            </span>
            <n-button class="mr-[24px]" type="primary" @click="handleEdit">编辑</n-button>
          </div>
        </div>
      </template>
      <div class="bg-right-bg rounded-[4px] py-[24px] px-[32px] mb-[32px] flex">
        <div class="mr-[16px]">
          <n-image
            v-if="!productInfo.product_pic"
            :preview-disabled="true"
            :src="getImageUrl('admin/icon_product.png')"
            class="w-[67px]"
          />
          <n-carousel show-arrow v-else class="w-[150px] h-[150px]">
            <n-image
              v-for="item in productInfo.product_pic"
              :key="item"
              :src="item"
              object-fit="cover"
              class="w-full h-full rounded-[4px]"
            />
          </n-carousel>
        </div>
        <div>
          <div class="text-[20px] font-bold mb-[16px]">{{ productInfo.product_name }}</div>
          <div class="flex">
            <div class="w-[228px]">
              <div class="flex mb-[8px]">
                <div class="form-title">单价</div>
                <div class="text-title-deep-gray">{{ productInfo.product_price }}元</div>
              </div>
              <div class="flex mb-[8px]">
                <div class="form-title">单位</div>
                <div class="text-title-deep-gray">
                  {{ productInfo.product_unit === 1 ? '套' : '件' }}
                </div>
              </div>
              <div class="flex">
                <div class="form-title">上架状态</div>
                <div class="text-title-deep-gray">
                  {{ productInfo.is_sale === 1 ? '上架' : '下架' }}
                </div>
              </div>
            </div>
            <div>
              <div class="flex mb-[8px]">
                <div class="form-title">产品分类</div>
                <div class="text-title-deep-gray">
                  {{ productInfo?.product_category?.category_name }}
                </div>
              </div>
              <div class="flex">
                <div class="form-title">产品编码</div>
                <div class="text-title-deep-gray">{{ productInfo.product_no }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="">
        <div class="text-[16px] text-title-deep-gray font-bold mb-[16px]">其他信息</div>
        <div class="text-text-middle-gray">
          <div class="flex mb-[24px]">
            <div class="w-[98px] text-text-middle-gray">产品介绍：</div>
            <div class="text-title-deep-gray flex-1">
              {{ productInfo.product_detail }}
            </div>
          </div>
          <div class="flex mb-[24px]">
            <div class="w-[98px] text-text-middle-gray">创建者：</div>
            <div class="text-title-deep-gray flex-1">{{ productInfo.creator }}</div>
          </div>
          <div class="flex">
            <div class="w-[98px] text-text-middle-gray">创建时间：</div>
            <div class="text-title-deep-gray flex-1">{{ productInfo.created_at }}</div>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
  <!-- 新增产品 -->
  <CreateModal ref="createModalRef" :isEdit="true" @reload="reloadDetail" />
</template>

<script setup lang="ts">
  import { editProductStatus, getProductDetail } from '@/api/admin/product/product'
  import { getImageUrl } from '@/utils/assetUtils'
  import { ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { useI18n } from '@/hooks/web/useI18n'
  import SvgIcon from '@/components/SvgIcon'
  import CreateModal from './CreateModal.vue'
  const { t } = useI18n()
  const message = useMessage()
  const active = ref(false)
  const isLoading = ref(false)
  const productInfo = ref<Recordable>({})
  const emit = defineEmits(['reload'])

  /**
   * 显示抽屉
   * @param record 产品信息
   */
  const productId = ref<number>(0)
  function showDrawer(id: number) {
    productId.value = id
    reloadDetail()
  }

  /**
   * 查询产品详情
   */
  function reloadDetail() {
    getProductDetail(productId.value).then((data) => {
      productInfo.value = data
      active.value = true
    })
  }

  /**
   * 关闭抽屉
   */
  function closeDrawer() {
    active.value = false
  }

  /**
   * 监听页面关闭
   */
  function handleAfterLeave() {
    emit('reload')
  }

  /**
   * 修改产品状态
   */
  function handleUpdateValue() {
    addonProductStatus(productInfo.value.id)
  }
  /**
   * 修改产品状态
   * @param id 产品id
   */
  async function addonProductStatus(id: number) {
    isLoading.value = true
    await editProductStatus(id)
    productInfo.value.is_sale = productInfo.value.is_sale === 1 ? 2 : 1
    isLoading.value = false
    message.success(t('common.operationSuccess'))
  }

  /**
   * 编辑产品
   */
  const createModalRef = ref()
  function handleEdit() {
    createModalRef.value?.showModal('编辑产品', productInfo.value)
  }
  defineExpose({
    showDrawer,
    closeDrawer,
  })
</script>

<style lang="scss" scoped>
  /* 组合常用的 Tailwind 样式 */
  .form-title {
    @apply text-text-middle-gray w-[70px] text-align-last-justify mr-[8px];
  }
</style>
