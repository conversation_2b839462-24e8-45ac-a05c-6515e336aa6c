import { BasicColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { formatToDateTime } from '@/utils/dateUtil'
import { h } from 'vue'
import { NImage, NSwitch } from 'naive-ui'

const { t } = useI18n()

/**
 * 应用列表
 */
export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('product.common.serial'),
    key: 'serial',
    width: 70,
    render: (row, index) => {
      return index + 1
    },
  },
  {
    title: t('product.common.productNumber'),
    key: 'product_no',
    width: 80,
    minWidth: 80,
  },
  {
    title: t('product.common.productName'),
    key: 'product_name',
    width: 220,
    minWidth: 220,
  },
  {
    title: t('product.common.productPicture'),
    key: 'product_pic',
    width: 80,
    minWidth: 80,
    render(row) {
      return h(
        'div',
        {
          class: ['flex', 'align-center'].filter(Boolean).join(' '),
          style: { display: 'flex', justifyContent: 'center' },
        },
        row.product_pic?.map((pic) => {
          return [
            h(NImage, {
              width: 40,
              height: 40,
              src: pic,
              class: 'w-[40px] h-[40px] flex-shrink-0 rounded-md',
            }),
            h('div', {
              class: 'flex flex-col ml-[12px] text-left',
            }),
          ]
        }),
      )
    },
  },
  {
    title: t('product.common.productCategory'),
    key: '[product_category.category_name]',
    width: 80,
    minWidth: 80,
  },
  {
    title: t('product.common.unfinishedQuantity'),
    key: 'unfinish_opportunity_num',
    width: 120,
    minWidth: 120,
  },
  {
    title: t('product.common.productPrice'),
    key: 'product_price',
    width: 110,
    minWidth: 110,
  },
  {
    title: t('product.common.productUnit'),
    width: 70,
    minWidth: 70,
    key: 'product_unit',
    render: (row) => {
      return row.product_unit == 1 ? '套' : '件'
    },
  },
  {
    title: t('product.common.productDetail'),
    key: 'product_detail',
    width: 220,
    minWidth: 220,
  },
  {
    title: t('product.common.status'),
    key: 'is_sale',
    width: 70,
    minWidth: 70,
    render(row) {
      return h(NSwitch, {
        value: row.is_sale === 1 ? true : false,
        'onUpdate:value': (value) => {
          row.is_sale = value
          row._addonProductStatus(row.id)
        },
      })
    },
  },
  {
    title: t('product.common.creator'),
    key: 'creator',
    width: 180,
    minWidth: 180,
  },
  {
    title: t('product.common.createTime'),
    key: 'created_at',
    render(row) {
      return formatToDateTime(row.created_at)
    },
  },
]
