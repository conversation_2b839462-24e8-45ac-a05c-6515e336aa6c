import { h } from 'vue'
import { colorMap } from '@/settings/designSetting'
import { NPopover, NIcon, NGradientText } from 'naive-ui'
import { Ellipse } from '@vicons/ionicons5'

/**
 * 渲染线索标签
 * @param row
 * @returns
 */
export function renderLabelRelationship(row: any) {
  if (!row.label_relationship || !Array.isArray(row.label_relationship)) {
    return null
  }

  return h(
    NPopover,
    {
      trigger: 'hover',
      placement: 'top',
    },
    {
      trigger: () =>
        h(
          'div',
          { style: 'display: flex; gap: 4px; flex-wrap: no-wrap;' },
          row.label_relationship.map((item: any) => {
            if (!item.label_category) return null

            return h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item.label_category.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  width: 'auto',
                  textAlign: 'center',
                },
              },
              item.label_category.name,
            )
          }),
        ),
      default: () =>
        h(
          'div',
          { style: 'padding: 8px;' },
          row.label_relationship.map((item: any) =>
            h(
              'div',
              {
                class: 'text-title-deep-gray',
                style: {
                  backgroundColor: item.label_category?.color,
                  border: 'none',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  marginBottom: '4px',
                  textAlign: 'center',
                  display: 'inline-block',
                  marginRight: '4px',
                  width: 'auto',
                },
              },
              item.label_category?.name,
            ),
          ),
        ),
    },
  )
}

/**
 * 渲染客户信息
 * @param row
 * @returns
 */
export function renderCustomer(row: any) {
  return h(
    'div',
    {
      class: 'flex items-center cursor-pointer',
      onClick: () => {
        row._showCustomerDetail(row)
      },
    },
    [
      h(
        'div',
        {
          class:
            'text-[16px] text-white size-[32px] rounded-full bg-blue-dot flex items-center justify-center flex-shrink-0 mr-[8px]',
        },
        {
          default: () => row.customer_name[0],
        },
      ),
      h('div', { class: 'text-[14px]' }, [
        h(
          'div',
          {
            class: 'text-title-deep-gray',
          },
          {
            default: () => row.customer_name,
          },
        ),
        h(
          'div',
          {
            class: 'text-text-middle-gray',
          },
          {
            default: () => row.phone,
          },
        ),
      ]),
    ],
  )
}

/**
 * 渲染商机状态
 * 通话状态（1：待联系，2：未接通，3：已接通，4：有效沟通，5：深度沟通）
 * @param row
 * @returns
 */
export function renderBusStatus(row: any) {
  let color = ''
  // 跟进中
  if (row.status === 1) {
    color = colorMap['blue-deep']
  }
  // 赢单
  else if (row.status === 2) {
    color = colorMap['green-dot']
  }
  // 输单
  else if (row.status === 3) {
    color = colorMap['red-dot']
  }

  return h(
    'div',
    {
      class: 'flex items-center',
    },
    [
      h(NIcon, { color: color, size: '8px' }, { default: () => h(Ellipse) }),
      h(
        NGradientText,
        {
          class: 'text-title-deep-gray  ml-1',
        },
        { default: () => row.status_text },
      ),
    ],
  )
}
