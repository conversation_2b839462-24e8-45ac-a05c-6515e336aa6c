import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { dateShortcuts } from '@/utils'
import { ListAllType, Collaborator } from './customerTypes'
import { BasicColumn } from '@/components/Table'
import { formatToDateTime } from '@/utils'
import { h } from 'vue'
import { NInputNumber } from 'naive-ui'
const { t } = useI18n()

// 跟进记录搜索栏
export function followRecordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 操作记录搜索栏
export function recordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'created_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 跟进记录表单
export function followRecordSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_content',
      label: t('customer.common.followRecord'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        maxlength: 200,
        showCount: true,
        placeholder: '请输入跟进记录',
      },
    },
    {
      field: 'follow_up_at',
      label: t('customer.common.followTime'),
      component: 'NDatePicker',
      componentProps: {
        class: 'w-full',
        type: 'datetime',
        defaultValue: new Date(),
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.pleaseInputFollowTime'),
      },
    },
  ]
}
// 添加客户
export function getSchema(ListAll: ListAllType, isEdit: boolean): FormSchema[] {
  return [
    {
      field: 'id',
      hidden: true,
    },
    {
      field: 'phone_id',
      hidden: true,
    },
    {
      field: 'customer_name',
      label: t('customer.common.customerName'),
      component: 'NInput',
      rules: [
        { required: !isEdit, message: t('customer.common.customerNameRequired') },
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.customerNameRequired'),
        },
      ],
    },
    {
      field: 'phone',
      label: t('customer.common.customerPhone'),
      component: 'NInput',
      rules: [
        {
          required: true,
          placeholder: t('customer.common.customerPhonePlaceholder'),
          message: t('customer.common.customerPhonePlaceholder'),
        },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: t('customer.common.customerPhonePlaceholder'),
        },
      ],
      componentProps: {
        disabled: isEdit,
      },
    },
    {
      field: 'wechat',
      label: t('customer.common.customerWechat'),
      component: 'NInput',
      rules: [
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.wechatRequired'),
          placeholder: t('customer.common.customerWechatPlaceholder'),
        },
      ],
    },
    {
      field: 'sex',
      label: t('customer.common.customerSex'),
      component: 'NRadioGroup',
      componentProps: {
        options: [
          { label: '未知', value: 1 },
          { label: '男', value: 2 },
          { label: '女', value: 3 },
        ],
      },
    },
    {
      field: 'age',
      label: t('customer.common.customerAge'),
      component: 'NInput',
      rules: [
        {
          pattern: /^\d{1,2}$/,
          message: t('customer.common.customerAgeRequired'),
          placeholder: t('customer.common.customerAgePlaceholder'),
        },
      ],
    },
    {
      field: 'company_name',
      component: 'NInput',
      label: t('customer.common.companyName'),
      rules: [
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.customerAgeRequired'),
          placeholder: t('customer.common.companyNameRequired'),
        },
      ],
    },
    {
      field: 'industry_id',
      component: 'NSelect',
      label: t('customer.common.industry'),
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: ListAll.industryList,
        labelField: 'industry_name',
        valueField: 'id',
      },
    },
    {
      field: 'location_array',
      component: 'NCascader',
      label: t('customer.common.location'),
      componentProps: {
        options: ListAll.regionList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        showPath: true,
        placeholder: t('common.chooseText'),
      },
    },
    {
      field: 'address',
      label: t('customer.common.address'),
      component: 'NInput',
      rules: [
        {
          pattern: /^.{0,50}$/,
          message: t('customer.common.addressRequired'),
          placeholder: t('customer.common.addressPlaceholder'),
        },
      ],
    },
    {
      field: 'source',
      component: 'NSelect',
      label: t('customer.common.source'),
      slot: 'sourceWithAccount',
      rules: [
        {
          required: true,
          message: t('customer.common.sourceRequired'),
        },
      ],
      componentProps: {
        placeholder: t('customer.common.sourceRequired'),
        options: ListAll.sourceList,
        labelField: 'source_name',
        valueField: 'id',
      },
    },
    {
      field: 'source_account',
      hidden: true,
    },
    {
      field: 'remark',
      label: t('common.remarkText'),
      component: 'NInput',
      giProps: {
        span: 2,
      },
      rules: [
        {
          pattern: /^.{0,100}$/,
          message: t('customer.common.remarkRequired'),
        },
      ],
      componentProps: {
        type: 'textarea',
      },
    },
  ]
}

//协作员搜索栏
export function collaboratorSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'employee_name',
      component: 'NInput',
      componentProps: {
        placeholder: t('customer.common.pleaseInputEmployeeName'),
      },
    },
  ]
}

// 添加协作员表单
export function collaboratorFormSchemas(list: Collaborator[]): FormSchema[] {
  return [
    {
      field: 'type',
      hidden: true,
    },
    {
      field: 'user_ids',
      component: 'NTreeSelect',
      label: t('customer.common.selectCollaborator'),
      componentProps: {
        options: list,
        labelField: 'name',
        keyField: 'id',
        defaultExpandAll: true,
        multiple: true,
        overrideDefaultNodeClickBehavior: ({ option }) => {
          if (option.children) {
            return 'toggleExpand'
          }
          return 'default'
        },
      },
      rules: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: t('customer.common.pleaseSelectCollaborator'),
        },
      ],
    },
    {
      field: 'permission',
      component: 'NRadioGroup',
      label: t('customer.common.dataPermission'),
      componentProps: {
        options: [
          { label: '只读', value: 1 },
          { label: '读写', value: 2 },
        ],
      },
    },
  ]
}
// 新增/编辑商机表单
export function businessFormSchemas(list): FormSchema[] {
  return [
    {
      field: 'title',
      label: t('customer.common.businessName'),
      component: 'NInput',
      rules: [
        {
          required: true,
          message: t('customer.common.businessNamePlaceholder'),
          placeholder: t('customer.common.businessNamePlaceholder'),
        },
      ],
      componentProps: {
        showCount: true,
        maxlength: 30,
      },
    },
    {
      label: t('customer.common.detailCustomerName'),
      field: 'customer_id',
      slot: 'customer',
    },
    {
      field: 'director_name',
      label: '负责人',
      component: 'NInput',
      componentProps: {
        showCount: true,
        maxlength: 30,
        disabled: true,
      },
    },
    {
      field: 'user_ids',
      component: 'NSelect',
      label: t('customer.common.selectCollaborator'),
      componentProps: {
        options: list,
        labelField: 'employee_name',
        valueField: 'user_id',
        multiple: true,
      },
    },
    {
      field: 'expected_win_rate',
      label: t('customer.common.estimatedWinRate'),
      component: 'NInput',
      componentProps: {
        placeholder: '请输入1-100的整数',
        maxlength: 3,
      },
      componentSlots: {
        suffix: () => h('span', { class: 'text-text-middle-gray' }, '%'),
      },
    },
    {
      field: 'expected_transaction_amount',
      label: '预计成交金额',
      component: 'NInput',
      componentProps: {
        placeholder: '请输入1-100的整数',
      },
      componentSlots: {
        suffix: () => h('span', { class: 'text-text-middle-gray' }, '元'),
      },
    },
    {
      field: 'expected_transaction_at',
      label: t('customer.common.estimatedWinDate'),
      component: 'NDatePicker',
      componentProps: {
        type: 'date',
        class: 'w-full',
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.estimatedWinDate'),
      },
    },
    {
      field: 'remark',
      label: t('customer.common.remark'),
      giProps: {
        span: 2,
      },
      component: 'NInput',
      rules: [{ pattern: /^.{0,100}$/, message: t('customer.common.remarkRequired') }],
      componentProps: {
        type: 'textarea',
        showCount: true,
        maxlength: 100,
        placeholder: t('customer.common.remark'),
      },
    },
  ]
}

// 商机表格配置
export const opportunityColumns: BasicColumn[] = [
  {
    title: t('customer.common.businessName'),
    key: 'title',
    minWidth: 180,
    width: 180,
  },
  {
    title: t('customer.common.detailCustomerName'),
    key: 'customer_name',
    minWidth: 98,
    width: 98,
  },
  {
    title: t('customer.common.principal'),
    key: 'director_name',
    minWidth: 98,
    width: 98,
  },
  {
    title: t('customer.common.collaborator'),
    key: 'collaborators_name_text',
    minWidth: 194,
    width: 194,
    render(row) {
      return row.collaborators_name_text === '' ? '-' : row.collaborators_name_text
    },
  },
  {
    title: t('customer.common.businessStage'),
    key: 'sop_stage_title',
    minWidth: 68,
    width: 68,
  },
  {
    title: t('customer.common.intentionProduct'),
    key: 'products_name_text',
    minWidth: 68,
    width: 68,
  },
  {
    title: t('customer.common.estimatedWinRate'),
    key: 'expected_win_rate',
    minWidth: 68,
    width: 68,
    render(row) {
      return row.expected_win_rate ? `${row.expected_win_rate}%` : '-'
    },
  },
  {
    title: t('customer.common.addTime'),
    key: 'created_at',
    minWidth: 180,
    width: 180,
    render(row) {
      return formatToDateTime(row.created_at)
    },
  },
]
// // 实时计算函数
// const calculateTotal = (rowData, field, value) => {
//   rowData[field] = value

//   // 计算总价
//   const quantity = rowData.quantity || 0
//   const price = rowData.price || 0
//   rowData.total = quantity * price

//   // 触发响应式更新
//   tableData.value = [...tableData.value]
// }
export const productColumns: BasicColumn[] = [
  {
    title: '产品名称',
    key: 'product_name',
    minWidth: 110,
    width: 110,
  },
  {
    title: '产品分类',
    key: '[product_category.category_name]',
    minWidth: 80,
    width: 80,
  },
  {
    title: '单位',
    key: 'product_unit',
    minWidth: 63,
    width: 63,
  },
  {
    title: '单价（元）',
    key: 'product_price',
    minWidth: 90,
    width: 90,
  },
  {
    title: '售价（元）',
    key: 'price',
    minWidth: 120,
    width: 120,
    render(row) {
      return h(NInputNumber, {
        value: row.price,
        showButton: false,
        onUpdateValue: (value) => {
          row.price = value
          // ✅ 正确调用：只传递 row 参数
          if (row._calculateTotalPrice) {
            row._calculateTotalPrice(row)
          }
        },
      })
    },
  },
  {
    title: '数量',
    key: 'quantity',
    minWidth: 120,
    width: 120,
    render(row) {
      return h(NInputNumber, {
        value: row.quantity,
        min: 1,
        precision: 0,
        showButton: false,
        placeholder: '请输入数量',
        onUpdateValue: (value) => {
          row.quantity = value
          if (row._calculateTotalPrice) {
            row._calculateTotalPrice(row)
          }
        },
      })
    },
  },
  {
    title: '折扣（%）',
    key: 'discount',
    minWidth: 120,
    width: 120,
    render(row) {
      return h(NInputNumber, {
        value: row.discount,
        min: 0,
        max: 100,
        showButton: false,
        precision: 2,
        placeholder: '请输入折扣',
        onUpdateValue: (value) => {
          row.discount = value
          if (row._calculateTotalPrice) {
            row._calculateTotalPrice(row)
          }
        },
      })
    },
  },
  {
    title: '实价（元）',
    key: 'actual_price',
    minWidth: 90,
    width: 90,
  },
]
