<template>
  <div :bordered="false">
    <BasicForm
      :showResetButton="false"
      :showAdvancedButton="false"
      ref="basicFormRef"
      @register="register"
      @submit="handleSubmit"
    >
      <!-- <template #action>
        <n-button type="default" @click="handleExport">导出</n-button>
      </template> -->
    </BasicForm>
    <n-timeline>
      <n-timeline-item v-for="(record, index) in recordList" :key="index" :time="record.created_at">
        <div>{{ record.creator }}</div>
        <div>{{ record.operate_content }} · {{ record.creator_role }}</div>
      </n-timeline-item>
    </n-timeline>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, computed } from 'vue'
  // import { useMessage } from 'naive-ui'
  import { NTimeline, NTimelineItem } from 'naive-ui'
  import {
    getCustomerRecord,
    CustomerRecord,
    // generateLogTask,
  } from '@/api/admin/customer/depCustomer'
  import { BasicForm, useForm } from '@/components/Form'
  import { recordSearchSchemas } from '../config/schemas'
  // import { useRouter } from 'vue-router'
  // const router = useRouter()
  // const message = useMessage()
  const recordList = ref<CustomerRecord[]>([])

  /**
   * 查询表单注册
   * 初始化查询表单配置
   */
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:2 xl:2 2xl:4' },
    labelWidth: 100,
    schemas: computed(() => recordSearchSchemas()),
  })

  /**
   * 组件属性
   */
  const props = defineProps({
    customerId: Number,
  })

  function handleSubmit() {
    handleCustomerRecord()
  }

  // async function handleExport() {
  //   try {
  //     await generateLogTask({
  //       key: 'leads_log',
  //       filter: {
  //         type: 2,
  //         link_id: props.customerId || 0,
  //         created_at: new Date().toISOString(),
  //       },
  //       checked: ['date', 'time', 'creator', 'operate_content', 'creator_role'],
  //     })
  //     message.success(
  //       () =>
  //         h('span', null, [
  //           '任务已提交，请在',
  //           h(
  //             'a',
  //             {
  //               style: 'color: #18a058; text-decoration: underline; cursor: pointer;',
  //               onClick: () => {
  //                 router.push({ name: 'home_downloadCenter' })
  //               },
  //             },
  //             '【下载中心】',
  //           ),
  //           '中查看',
  //         ]),
  //       { duration: 3000 },
  //     )
  //   } catch (error) {
  //     console.error(error)
  //   }
  // }

  async function handleCustomerRecord() {
    const formParams = getFieldsValue()
    const res = await getCustomerRecord({ ...formParams, link_id: props.customerId || 0 })
    recordList.value = res.list
  }

  onMounted(async () => {
    await handleCustomerRecord()
  })
</script>

<style scoped lang="scss">
  .n-timeline {
    margin: 0 0 0 10px;
  }
  .n-timeline-item-content {
    padding: 0;
  }
</style>
