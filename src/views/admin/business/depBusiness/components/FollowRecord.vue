<template>
  <div style="padding-right: 24px" class="flex-1">
    <template v-if="searched">
      <BasicForm
        :showResetButton="false"
        :showAdvancedButton="false"
        :showActionButtonGroup="false"
        ref="basicFormRef"
        @register="register"
        @submit="handleSubmit"
      />
      <template v-if="recordList.length > 0">
        <div class="follow-record-container" v-for="record in recordList" :key="record.id">
          <img style="width: 36px; height: 36px" :src="FollowRecordIcon" />
          <div
            class="follow-record flex-1"
            @mouseenter="hoverId = record.id"
            @mouseleave="hoverId = null"
          >
            <div class="follow-record-header">
              <span class="follow-record-title">{{ record.creator }}</span>
              <span
                v-show="hoverId === record.id"
                class="delete-btn"
                @click="handleDeleteRecord(record.id)"
              >
                {{ t('common.deleteText') }}
              </span>
            </div>
            <div
              class="follow-record-content"
              :class="{ collapsed: !recordStates[record.id]?.expanded }"
              :ref="(el) => setContentRef(el, record.id)"
            >
              {{ record.follow_up_content }}
            </div>
            <div class="follow-record-footer">
              <span class="follow-record-time">{{ record.follow_up_at }}</span>
              <span
                v-if="recordStates[record.id]?.showToggle && hoverId === record.id"
                class="toggle-btn"
                @click="toggleExpand(record.id)"
              >
                {{ recordStates[record.id]?.expanded ? '收起' : '展开' }}
              </span>
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <EmptyBlock icon="msg" emptyText="暂无跟进记录" />
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
  import {
    onMounted,
    ref,
    computed,
    nextTick,
    reactive,
    watch,
    type ComponentPublicInstance,
  } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { useDeleteDialog } from '@/hooks/useDeleteDiolog'
  import { useMessage } from 'naive-ui'
  import {
    getFollowRecord,
    FollowRecord,
    deleteFollowRecord,
  } from '@/api/admin/customer/depCustomer'
  import { BasicForm, useForm } from '@/components/Form'
  import { followRecordSearchSchemas } from '../config/schemas'
  import FollowRecordIcon from '@/assets/images/record-icon.png'
  import EmptyBlock from '@/components/Empty/src/index.vue'
  const { showDeleteDialog } = useDeleteDialog()
  const { t } = useI18n()
  const message = useMessage()
  const recordList = ref<FollowRecord[]>([])
  const hoverId = ref<number | null>(null)
  const contentRefs = ref<{ [id: number]: HTMLElement | null }>({})
  const recordStates = reactive<{ [id: number]: { expanded: boolean; showToggle: boolean } }>({})
  const searched = ref(false)

  /**
   * 查询表单注册
   * 初始化查询表单配置
   */
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:2 m:2 l:2 xl:2 2xl:4' },
    labelWidth: 100,
    schemas: computed(() => followRecordSearchSchemas()),
  })

  /**
   * 组件属性
   */
  const props = defineProps({
    customerId: Number,
  })

  function handleSubmit() {
    handleFollowRecord()
  }

  async function handleFollowRecord() {
    const formParams = getFieldsValue()
    const res = await getFollowRecord({
      ...formParams,
      link_id: props.customerId || 0,
      scene: 'list',
    })
    recordList.value = res.list
    searched.value = true
  }

  function setContentRef(el: Element | ComponentPublicInstance | null, id: number) {
    // 只存储 HTMLElement 类型
    if (el instanceof HTMLElement) {
      contentRefs.value[id] = el
    }
  }

  function toggleExpand(id: number) {
    recordStates[id].expanded = !recordStates[id].expanded
  }

  function handleDelete(id: number) {
    deleteFollowRecord(id)
      .then(() => {
        handleFollowRecord()
        message.success(t('common.deleteSuccess'))
      })
      .catch((error: any) => {
        message.error(error.message)
      })
  }

  function handleDeleteRecord(id: number) {
    showDeleteDialog('删除后该跟进记录将不可恢复，您确定要删除吗？', () => handleDelete(id))
  }

  watch(recordList, () => {
    nextTick(() => {
      recordList.value &&
        recordList.value.forEach((record) => {
          const el = contentRefs.value[record.id] as HTMLElement | null
          if (el) {
            const lineHeight = parseFloat(getComputedStyle(el).lineHeight)
            const maxHeight = lineHeight * 3
            recordStates[record.id] = recordStates[record.id] || {
              expanded: false,
              showToggle: false,
            }
            recordStates[record.id].showToggle = el.scrollHeight > maxHeight + 2
          }
        })
    })
  })

  onMounted(async () => {
    await handleFollowRecord()
  })

  defineExpose({
    handleFollowRecord,
  })
</script>

<style scoped lang="scss">
  .follow-record-container {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    .follow-record {
      display: flex;
      flex-direction: column;
      align-items: center;
      align-items: flex-start;
      padding-bottom: 10px;
      margin-left: 8px;
    }
    .delete-btn {
      margin-left: 8px;
      line-height: 22px;
      color: #f56c6c;
      cursor: pointer;
      transition: color 0.2s;
    }
    .follow-record-content {
      display: -webkit-box;
      margin: 6px 0 4px 0;
      overflow: hidden;
      font-size: 14px;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.65);
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
      word-break: break-all;
      -webkit-box-orient: vertical;
      &.collapsed {
        max-height: 66px;
        -webkit-line-clamp: 3;
      }
      &:not(.collapsed) {
        max-height: none;
        overflow: visible;
        -webkit-line-clamp: unset;
      }
    }

    .follow-record-footer {
      display: flex;
      justify-content: space-between;
      min-width: 100%;
      max-width: 100%;
      .follow-record-time {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.25);
      }
    }
    .follow-record-header {
      display: flex;
      justify-content: space-between;
      min-width: 100%;
      max-width: 100%;
      .follow-record-title {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .toggle-btn {
      margin-left: 4px;
      font-size: 13px;
      line-height: 22px;
      color: #1c61f6;
      cursor: pointer;
      user-select: none;
    }
  }
</style>
