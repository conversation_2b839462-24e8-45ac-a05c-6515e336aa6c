<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="tag-container">
        <!-- 循环标签分类 -->
        <div v-for="(category, index) in customerTags?.list" :key="index" class="tag-section">
          <div class="tag-title">{{ category.category_name }}</div>
          <n-space wrap>
            <n-button
              v-for="(tag, tagIndex) in category.label_setting"
              :key="tagIndex"
              :type="isTagSelected(tag.id) ? 'primary' : 'default'"
              @click="toggleTag(tag)"
            >
              {{ tag.name }}
            </n-button>
          </n-space>
        </div>

        <!-- 已选标签部分 -->
        <div class="selected-tags">
          <div class="selected-header">
            <span>
              {{ t('customer.common.selectedTags') }} ({{ selectedTags && selectedTags?.length }})
              {{ t('customer.common.unit') }}
            </span>
            <n-button text type="primary" @click="clearAllTags">
              {{ t('customer.common.clear') }}
            </n-button>
          </div>
          <n-space wrap>
            <n-tag
              v-for="tag in selectedTags"
              :key="tag.id"
              type="primary"
              closable
              @close="removeTag(tag)"
            >
              {{ tag.name }}
            </n-tag>
          </n-space>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { useMessage } from 'naive-ui'
  import { addLabelRelationship } from '@/api/admin/customer/myCustomer'
  import { cloneDeep } from 'lodash-es'

  const message = useMessage()
  const emit = defineEmits(['reload'])
  const { t } = useI18n()

  const customerTags = ref<any>()
  const customerId = ref('')
  const selectedTags = ref<{ id: number; name: string }[]>([])

  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 600,
    fullscreen: false,
  })

  function isTagSelected(tagId: number) {
    return selectedTags.value.some((tag) => tag.id === tagId)
  }

  function toggleTag(tag: { id: number; name: string }) {
    if (isTagSelected(tag.id)) {
      removeTag(tag)
    } else {
      selectedTags.value.push({ ...tag })
    }
  }

  function removeTag(tag: { id: number; name: string }) {
    const index = selectedTags.value.findIndex((item) => item.id === tag.id)
    if (index !== -1) {
      selectedTags.value.splice(index, 1)
    }
  }

  function clearAllTags() {
    selectedTags.value = []
  }

  async function showModal(id: string, tags: any) {
    customerId.value = id
    customerTags.value = cloneDeep(tags)
    selectedTags.value = customerTags.value.selected_list
    openModal(t('customer.common.tagManagement'))
  }

  async function formSubmit() {
    const params = {
      type: 2,
      link_id: customerId.value,
      label_ids: selectedTags.value.map((tag) => tag.id),
    }
    try {
      await addLabelRelationship(params)
      message.success(t('common.addSuccess'))
      emit('reload')
      closeModal()
      setSubLoading(false)
    } catch (error) {
      console.log(error)
      setSubLoading(false)
    }
  }

  defineExpose({
    showModal,
    closeModal,
  })
</script>

<style scoped>
  .tag-container {
    padding: 16px;
  }

  .tag-section {
    margin-bottom: 20px;
  }

  .tag-title {
    margin-bottom: 10px;
    font-weight: bold;
  }

  .selected-tags {
    padding-top: 20px;
    margin-top: 30px;
    border-top: 1px solid #eee;
  }

  .selected-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
</style>
