<template>
  <div>
    <n-card :bordered="false" class="full-card" title="部门客户列表">
      <BasicForm
        @register="register"
        @submit="handleSubmit"
        @reset="handleReset"
        :enableCustomFormItemStyle="true"
      />
      <BasicTable
        ref="tableRef"
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        :actionColumn="actionColumn"
        :autoScrollX="true"
        :showTableSetting="false"
        :isShowDivider="false"
        @update:checked-row-keys="handleCheck"
      >
        <template #tableTitle>
          <n-space>
            <n-button :disabled="btnDisabled" type="info" @click="showExportCustomerModal">
              {{ t('customer.common.exportCustomer') }}
            </n-button>
          </n-space>
        </template>
      </BasicTable>
    </n-card>

    <!-- 客户详情 -->
    <DetailCustomerDrawer
      ref="DetailCustomerDrawerRef"
      :fromType="FROM_TYPE_DEPARTMENT_CUSTOMER"
      :businessType="DEPARTMENT_CUSTOMER"
      :listAll="listAll"
      @reload="reloadTable"
    />

    <!--    导出弹窗-->
    <ExportCustomerModal
      ref="ExportCustomerModalRef"
      :title="t('customer.common.exportCustomer')"
      :fromType="FROM_TYPE_DEPARTMENT_CUSTOMER"
      :exortParams="exortParams"
      :exportList="exportList"
    />

    <!-- 商机明细弹窗 -->
    <n-popover
      v-model:show="showOpportunityModal"
      trigger="manual"
      placement="bottom"
      :show-arrow="true"
      style="width: 600px"
    >
      <template #trigger>
        <div ref="opportunityTriggerRef" style="position: absolute; visibility: hidden"></div>
      </template>
      <div style="padding: 16px">
        <BasicTable
          :columns="opportunityColumns"
          :showTableSetting="false"
          :autoScrollX="true"
          :pagination="false"
        />
      </div>
    </n-popover>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, onMounted, h, onUnmounted } from 'vue'
  import { BasicForm, useForm } from '@/components/Form'
  import { useI18n } from '@/hooks/web/useI18n'
  import {
    getCustomerFilterOptions,
    getCustomerList,
    getCustomerLabels,
    getTaskFields,
    getAssignPeople,
  } from '@/api/admin/customer/depCustomer'
  import { querySchemas } from './config/schemas'
  import { columns, opportunityColumns } from './config/columns'
  import { BasicColumn, BasicTable, TableAction } from '@/components/Table'
  import DetailCustomerDrawer from '../components/DetailCustomerDrawer.vue'
  import ExportCustomerModal from '../components/ExportCustomerModal.vue'
  import { useAdminSelect } from '@/hooks'
  import { type ListAllType, type ExportListType } from '../config/customerTypes'
  import { FROM_TYPE_DEPARTMENT_CUSTOMER } from '@/enums/fromTypeEnum'
  import { DEPARTMENT_CUSTOMER } from '@/enums/sopTypeEnums'
  const { t } = useI18n()
  const tableRef = ref()
  const DetailCustomerDrawerRef = ref()
  const ExportCustomerModalRef = ref()
  const checkedIds = ref<number[]>([])
  const showOpportunityModal = ref(false)
  // const opportunityList = ref<any[]>([])
  const opportunityTriggerRef = ref()
  const listAll = reactive<ListAllType>({
    // 所属行业
    industryList: [],
    // 标签列表
    customerLabelList: [],
    //所在地址
    regionList: [],
    //渠道来源
    sourceList: [],
    //导入方法
    entryMethodsList: [],
    //转化方式
    transactionStatusList: [],
    // 负责人列表
    assignPeopleList: [],
  })
  const btnDisabled = computed(() => checkedIds.value.length <= 0)
  const exportList = ref<ExportListType[]>([]) //导出字段

  const actionColumn: BasicColumn = reactive({
    width: 70,
    title: t('common.actionText'),
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'text',
        actions: [
          {
            label: t('customer.common.detail'),
            color: '#2d8cf0',
            onClick: () => handleDetail(record),
            ifShow: () => {
              return true
            },
            auth: ['customer'],
          },
        ],
      })
    },
  })

  /**
   * 查询表单注册
   * 初始化查询表单配置
   */
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:3 l:4 xl:4 2xl:4', xGap: 10, collapsedRows: 2 },
    labelWidth: 100,
    schemas: computed(() => querySchemas(listAll)),
  })
  const exortParams = ref()

  const loadDataTable = async (params: any) => {
    const formParams = getFieldsValue()
    exortParams.value = {
      scene: 'customer_dept_page',
      from: 2, //固定传1，表示是我的客户列表
    }
    const res = await getCustomerList({
      ...exortParams.value,
      ...params,
      ...formParams,
    })

    res.list.forEach((row) => {
      row._showCustomerDetail = handleDetail
    })

    return res
  }
  /**
   * 批量勾选
   */
  function handleCheck(ids: any[]) {
    checkedIds.value = ids
  }

  /**
   * 表单操作方法
   */
  function handleSubmit(values: Recordable) {
    console.log('handleSubmit', values)
    reloadTable()
  }

  function handleReset(values: Recordable) {
    console.log('handleReset', values)
    reloadTable()
  }

  /**
   * 重新加载表格
   */
  function reloadTable() {
    checkedIds.value = []
    tableRef.value.reload()
  }

  function showExportCustomerModal() {
    ExportCustomerModalRef.value?.showModal(t('customer.common.exportCustomer'), checkedIds.value)
  }

  function handleDetail(record: Recordable) {
    DetailCustomerDrawerRef.value.showDrawer(record.id)
  }

  onMounted(async () => {
    const { selectData, fetchRegion, fetchIndustry, fetchSource, fetchFollowStatus } =
      useAdminSelect()

    // 获取需要的选择列表数据
    await Promise.all([fetchRegion(), fetchIndustry(), fetchSource(), fetchFollowStatus()])

    // 使用获取到的数据
    listAll.regionList = selectData.regionList
    listAll.industryList = selectData.industryList
    listAll.sourceList = selectData.sourceList

    // 其他API调用保持不变
    listAll.customerLabelList = await getCustomerLabels()
    const res = await getCustomerFilterOptions()
    listAll.entryMethodsList = res?.entry_methods_options
    listAll.transactionStatusList = res?.transaction_status_options
    listAll.assignPeopleList = await getAssignPeople()
    const exportRes = await getTaskFields()
    exportList.value = Object.entries(exportRes.fields).map(([k, v]) => {
      return {
        value: k,
        label: v,
      }
    })

    // 添加点击事件监听器来关闭弹窗
    document.addEventListener('click', handleDocumentClick)
  })

  onUnmounted(() => {
    // 清理事件监听器
    document.removeEventListener('click', handleDocumentClick)
  })

  // 处理文档点击事件，关闭弹窗
  function handleDocumentClick(event: Event) {
    if (showOpportunityModal.value) {
      const target = event.target as HTMLElement
      // 如果点击的不是弹窗内容，则关闭弹窗
      if (!target.closest('.n-popover') && !target.closest('a[style*="color: #18a058"]')) {
        showOpportunityModal.value = false
        // 隐藏触发元素
        if (opportunityTriggerRef.value) {
          opportunityTriggerRef.value.style.visibility = 'hidden'
        }
      }
    }
  }
</script>

<style scoped lang="scss"></style>
