import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { dateShortcuts } from '@/utils'
import { ListAllType } from '../../config/customerTypes'
import { Collaborator } from '@/api/admin/customer/myCustomer'
const { t } = useI18n()

// 我的客户搜索栏
export function querySchemas(listAll: ListAllType): FormSchema[] {
  return [
    {
      field: 'director_id',
      component: 'NTreeSelect',
      label: t('customer.common.director'),
      componentProps: {
        placeholder: t('customer.common.selectDirector'),
        options: listAll.assignPeopleList,
        labelField: 'name',
        keyField: 'id',
        childrenField: 'children',
        filterable: true,
        clearable: true,
        showPath: true,
        filter: (pattern, node) => {
          return node.name.includes(pattern)
        },
      },
    },
    {
      field: 'customer_name',
      component: 'NInput',
      label: t('customer.common.customerName'),
      componentProps: {
        placeholder: t('customer.common.customerNamePlaceholder'),
      },
    },
    {
      field: 'company_name',
      component: 'NInput',
      label: t('customer.common.companyName'),
      componentProps: {
        placeholder: t('customer.common.companyNamePlaceholder'),
      },
    },
    {
      field: 'phone',
      component: 'NInput',
      label: t('customer.common.customerPhone'),
      componentProps: {
        placeholder: t('customer.common.customerPhonePlaceholder'),
      },
    },
    {
      field: 'industry_id',
      component: 'NSelect',
      label: t('leads.common.industry'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.industryList,
        labelField: 'industry_name',
        valueField: 'id',
      },
    },
    {
      field: 'transaction_status',
      component: 'NSelect',
      label: t('customer.common.transactionStatus'),
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: listAll.transactionStatusList,
        labelField: 'label',
        valueField: 'key',
      },
    },
    {
      field: 'customerLabel',
      component: 'NSelect',
      label: t('customer.common.customerLabel'),
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: listAll.customerLabelList,
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      field: 'entry_methods',
      label: t('customer.common.transactionMethod'),
      component: 'NSelect',
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: listAll.entryMethodsList,
        labelField: 'label',
        valueField: 'key',
      },
    },
    {
      field: 'created_at',
      component: 'NDatePicker',
      label: t('customer.common.addTime'),
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 编辑客户
export function getSchema(ListAll: ListAllType, isEdit: boolean): FormSchema[] {
  return [
    {
      field: 'id',
      hidden: true,
    },
    {
      field: 'phone_id',
      hidden: true,
    },
    {
      field: 'customer_name',
      label: t('customer.common.customerName'),
      component: 'NInput',
      rules: [
        { required: !isEdit, message: t('customer.common.customerNameRequired') },
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.customerNameRequired'),
        },
      ],
    },
    {
      field: 'phone',
      label: t('customer.common.customerPhone'),
      component: 'NInput',
      rules: [
        {
          required: true,
          placeholder: t('customer.common.customerPhonePlaceholder'),
          message: t('customer.common.customerPhonePlaceholder'),
        },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: t('customer.common.customerPhonePlaceholder'),
        },
      ],
      componentProps: {
        disabled: isEdit,
      },
    },
    {
      field: 'wechat',
      label: t('customer.common.customerWechat'),
      component: 'NInput',
      rules: [
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.wechatRequired'),
          placeholder: t('customer.common.customerWechatPlaceholder'),
        },
      ],
    },
    {
      field: 'sex',
      label: t('customer.common.customerSex'),
      component: 'NRadioGroup',
      componentProps: {
        options: [
          { label: '未知', value: 1 },
          { label: '男', value: 2 },
          { label: '女', value: 3 },
        ],
      },
    },
    {
      field: 'age',
      label: t('customer.common.customerAge'),
      component: 'NInput',
      rules: [
        {
          pattern: /^\d{1,2}$/,
          message: t('customer.common.customerAgeRequired'),
          placeholder: t('customer.common.customerAgePlaceholder'),
        },
      ],
    },
    {
      field: 'company_name',
      component: 'NInput',
      label: t('customer.common.companyName'),
      rules: [
        {
          pattern: /^.{0,30}$/,
          message: t('customer.common.customerAgeRequired'),
          placeholder: t('customer.common.companyNameRequired'),
        },
      ],
    },
    {
      field: 'industry_id',
      component: 'NSelect',
      label: t('customer.common.industry'),
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: ListAll.industryList,
        labelField: 'industry_name',
        valueField: 'id',
      },
    },
    {
      field: 'location_province_id',
      component: 'NCascader',
      label: t('customer.common.location'),
      componentProps: {
        options: ListAll.regionList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        showPath: true,
        placeholder: t('common.chooseText'),
      },
    },
    {
      field: 'address',
      label: t('customer.common.address'),
      component: 'NInput',
      rules: [
        {
          pattern: /^.{0,50}$/,
          message: t('customer.common.addressRequired'),
          placeholder: t('customer.common.addressPlaceholder'),
        },
      ],
    },
    {
      field: 'source',
      component: 'NSelect',
      label: t('customer.common.source'),
      slot: 'sourceWithAccount',
      rules: [
        {
          required: true,
          message: t('customer.common.sourceRequired'),
        },
      ],
      componentProps: {
        placeholder: t('customer.common.sourceRequired'),
        options: ListAll.sourceList,
        labelField: 'source_name',
        valueField: 'id',
      },
    },
    {
      field: 'source_account',
      hidden: true,
    },
    {
      field: 'remark',
      label: t('common.remarkText'),
      component: 'NInput',
      rules: [
        {
          pattern: /^.{0,100}$/,
          message: t('customer.common.remarkRequired'),
        },
      ],
      componentProps: {
        type: 'textarea',
      },
    },
  ]
}

// 导出弹窗
export function getTaskFields(exportList: any): FormSchema[] {
  return [
    {
      field: 'filter',
      hidden: true,
    },
    {
      field: 'key',
      label: t('customer.common.exportInfo'),
      component: 'NCheckbox',
      slot: 'select_all',
    },
    {
      field: 'checked',
      label: ' ',
      component: 'NCheckbox',
      slot: 'checked_Field',
      componentProps: {
        options: exportList,
      },
    },
  ]
}

//协作员搜索栏
export function collaboratorSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'employee_name',
      component: 'NInput',
      componentProps: {
        placeholder: t('customer.common.pleaseInputEmployeeName'),
      },
    },
  ]
}

// 添加协作员表单
export function collaboratorFormSchemas(list: Collaborator[]): FormSchema[] {
  return [
    {
      field: 'type',
      hidden: true,
    },
    {
      field: 'user_ids',
      component: 'NTreeSelect',
      label: t('customer.common.selectCollaborator'),
      componentProps: {
        options: list,
        labelField: 'name',
        keyField: 'id',
        defaultExpandAll: true,
        multiple: true,
        overrideDefaultNodeClickBehavior: ({ option }) => {
          if (option.children) {
            return 'toggleExpand'
          }
          return 'default'
        },
      },
      rules: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: t('customer.common.pleaseSelectCollaborator'),
        },
      ],
    },
    {
      field: 'permission',
      component: 'NRadioGroup',
      label: t('customer.common.dataPermission'),
      componentProps: {
        options: [
          { label: '只读', value: 1 },
          { label: '读写', value: 2 },
        ],
      },
    },
  ]
}

// 回退线索表单
export function getReturnLeadSchema(): FormSchema[] {
  return [
    {
      field: 'ids',
      hidden: true,
    },
    {
      field: 'customer_back_reason',
      label: t('customer.common.returnReason'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        showCount: true,
      },
      rules: [
        {
          pattern: /^.{0,50}$/,
          message: t('customer.common.returnReasonRequired'),
        },
        {
          required: true,
          message: t('customer.common.returnReasonRequired'),
        },
      ],
    },
  ]
}

// 操作记录搜索栏
export function recordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'created_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 跟进记录搜索栏
export function followRecordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 跟进记录表单
export function followRecordSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_content',
      label: t('customer.common.followRecord'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        maxlength: 200,
        showCount: true,
        placeholder: '请输入跟进记录',
      },
    },
    {
      field: 'follow_up_at',
      label: t('customer.common.followTime'),
      component: 'NDatePicker',
      componentProps: {
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.pleaseInputFollowTime'),
      },
    },
  ]
}
// 新增/编辑商机表单
export function businessFormSchemas(): FormSchema[] {
  return [
    {
      field: 'title',
      label: t('customer.common.businessName'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.businessNameRequired') },
        { pattern: /^.{1,30}$/, message: t('customer.common.businessNameRequired') },
      ],
      componentProps: {
        showCount: true,
        maxlength: 30,
        placeholder: t('customer.common.businessNameRequired'),
      },
    },
    {
      field: 'customer_id',
      hidden: true,
    },
    {
      field: 'user_ids',
      label: t('customer.common.collaborator'),
      component: 'NTreeSelect',
      rules: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: t('customer.common.pleaseSelectCollaborator'),
        },
      ],
      componentProps: {
        multiple: true,
        placeholder: t('customer.common.pleaseSelectCollaborator'),
      },
    },
    {
      field: 'permission',
      label: t('customer.common.dataPermission'),
      component: 'NRadioGroup',
      rules: [{ required: true, message: t('customer.common.dataPermission') }],
      componentProps: {
        options: [
          { label: '只读', value: 1 },
          { label: '读写', value: 2 },
        ],
      },
    },
    {
      field: 'expected_win_rate',
      label: t('customer.common.estimatedWinRate'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.estimatedWinRate') },
        { pattern: /^([1-9][0-9]?|100)$/, message: '请输入1-100的整数' },
      ],
      componentProps: {
        placeholder: '请输入1-100的整数',
        suffix: '%',
        maxlength: 3,
      },
    },
    {
      field: 'expected_transaction_amount',
      label: t('customer.common.estimatedWinAmount'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.estimatedWinAmount') },
        { pattern: /^\d+$/, message: '请输入数字' },
      ],
      componentProps: {
        placeholder: '请输入预计成交金额',
        suffix: '元',
      },
    },
    {
      field: 'expected_transaction_at',
      label: t('customer.common.estimatedWinDate'),
      component: 'NDatePicker',
      rules: [{ required: true, message: t('customer.common.estimatedWinDate') }],
      componentProps: {
        type: 'date',
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.estimatedWinDate'),
      },
    },
    {
      field: 'remark',
      label: t('customer.common.remark'),
      component: 'NInput',
      rules: [{ pattern: /^.{0,100}$/, message: t('customer.common.remarkRequired') }],
      componentProps: {
        type: 'textarea',
        showCount: true,
        maxlength: 100,
        placeholder: t('customer.common.remark'),
      },
    },
    {
      field: 'products',
      label: t('customer.common.productInfo'),
      slot: 'products_info',
      rules: [{ required: true, message: '请添加产品信息' }],
    },
    {
      field: 'whole_order_discount',
      label: '整单折扣',
      component: 'NInput',
      rules: [{ pattern: /^\d*$/, message: '请输入数字' }],
      componentProps: {
        placeholder: '请输入整单折扣',
      },
    },
  ]
}
