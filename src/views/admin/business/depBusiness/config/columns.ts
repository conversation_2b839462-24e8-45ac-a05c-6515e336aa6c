import { BasicColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { formatToDateTime } from '@/utils'
import { renderCustomer, renderLabelRelationship } from '../../config/renderColumns'

const { t } = useI18n()

export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('customer.common.customerName'),
    key: 'customer_name',
    width: 165,
    minWidth: 165,
    render(row) {
      return renderCustomer(row)
    },
  },
  {
    title: t('customer.common.companyName'),
    key: 'company_name',
  },
  {
    title: t('customer.common.transactionStatus'),
    key: 'transaction_status_text',
  },
  {
    title: t('customer.common.businessNumber'),
    key: 'opportunity_num',
  },
  {
    title: t('customer.common.principal'),
    key: '[director.employee_name]',
  },
  {
    title: t('customer.common.customerLabel'),
    align: 'center',
    key: 'label_relationship',
    render(row) {
      return renderLabelRelationship(row)
    },
  },
  {
    title: t('customer.common.transactionMethod'),
    key: 'entry_methods_text',
  },
  {
    title: t('common.createTimeText'),
    key: 'created_at',
    render(row) {
      return formatToDateTime(row.created_at)
    },
  },
  {
    title: t('customer.common.otherInfo'),
    key: 'other_info',
  },
]

export const opportunityColumns: BasicColumn[] = [
  {
    title: t('customer.common.businessName'),
    key: 'business_name',
  },
  {
    title: t('customer.common.businessStage'),
    key: 'business_stage',
  },
  {
    title: t('customer.common.estimatedWinAmount'),
    key: 'estimated_win_amount',
  },
  {
    title: t('customer.common.estimatedWinRate'),
    key: 'estimated_win_rate',
  },
  {
    title: t('customer.common.estimatedWinDate'),
    key: 'actual_win_date',
  },
]
