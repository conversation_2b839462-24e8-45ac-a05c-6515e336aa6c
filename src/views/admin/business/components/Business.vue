<template>
  <div>
    <n-space class="flex items-center justify-between">
      <n-input-group style="width: 320px">
        <n-select
          v-model:value="formData.filter_fields"
          style="width: 100px"
          :options="filterFieldsOptions"
        />
        <n-input
          style="width: 220px"
          v-model:value="formData.filter_values"
          @keyup.enter="reloadTable"
        />
      </n-input-group>
      <n-button type="info" @click="showAddBusinessDialog(false)">
        {{ t('customer.common.addBusiness') }}
      </n-button>
    </n-space>
    <BasicTable
      ref="BusinessRef"
      :columns="opportunityColumns"
      :request="loadBusinessData"
      :row-key="(row) => row.id"
      :actionColumn="actionColumn"
      :autoScrollX="true"
      :showTableSetting="false"
      :isShowDivider="false"
      :paginate-single-page="false"
    >
      <template #empty>
        <EmptyBlock icon="msg" emptyText="暂无商机" />
      </template>
    </BasicTable>

    <CreateBusinessDrawer
      ref="createBusinessDrawerRef"
      :customerInfo="props.customerInfo"
      :directorName="props.directorName"
      :fromType="props.fromType"
      @reload="reloadTable"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, h, reactive, onMounted } from 'vue'
  import { BasicColumn, BasicTable, TableAction } from '@/components/Table'
  import { useI18n } from '@/hooks/web/useI18n'
  import { opportunityColumns } from '../config/schemas'
  import { getBusinessList } from '@/api/admin/customer/myCustomer'
  import CreateBusinessDrawer from './CreateBusinessDrawer.vue'
  import { EmptyBlock } from '@/components/Empty'
  const { t } = useI18n()
  const BusinessRef = ref()
  const props = defineProps({
    customerId: String,
    customerInfo: { type: Object, default: () => {} },
    directorName: String,
    fromType: {
      type: Number,
      default: 0,
    },
  })
  const createBusinessDrawerRef = ref()
  const actionColumn: BasicColumn = reactive({
    width: 80,
    title: t('common.actionText'),
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'text',
        actions: [
          {
            label: t('common.editText'),
            onClick: () => {
              showAddBusinessDialog(true, record.id)
            },
          },
        ],
      })
    },
  })

  /**
   * 刷新表格
   */
  function reloadTable() {
    BusinessRef.value.reload()
  }

  const formData = ref({
    filter_fields: 'title',
    filter_values: '',
  })
  const filterFieldsOptions = [
    { label: '商机名称', value: 'title' },
    { label: '客户名称', value: 'customer_name' },
    { label: '商机阶段', value: 'sop_stage_title' },
  ]
  const loadBusinessData = async (params: any) => {
    const apiParams = {
      ...params,
      ...formData.value,
      scene: 'customer_detail_page',
      customer_id: props.customerId,
    }
    const res = await getBusinessList(apiParams)
    return res
  }

  function showAddBusinessDialog(isEdit: boolean, oppId?: any) {
    createBusinessDrawerRef.value?.openDrawer(isEdit, oppId)
  }

  onMounted(() => {})
  defineExpose({
    reloadTable,
    loadBusinessData,
  })
</script>

<style scoped lang="scss"></style>
