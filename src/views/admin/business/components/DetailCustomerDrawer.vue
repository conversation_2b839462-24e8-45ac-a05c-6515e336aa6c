<template>
  <n-drawer
    v-model:show="active"
    :width="isExpand ? '95%' : '1050px'"
    :block-scroll="false"
    :mask-closable="false"
    :show-mask="false"
    class="transition-all duration-300"
    placement="right"
  >
    <n-drawer-content :title="t('leads.common.customerDetail')" closable>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <n-image
              :preview-disabled="true"
              width="18"
              :src="getImageUrl('admin/icon_customer.png')"
            />
            <span class="text-[16px] font-bold ml-[5px]">客户详情</span>
          </div>
          <div class="flex items-center">
            <n-dropdown
              key-field="id"
              label-field="name"
              trigger="click"
              :options="followStatusList"
              @select="handleStateChange"
            >
              <n-button class="mr-[8px]">
                {{ t('leads.common.callStatusFollow') }}
              </n-button>
            </n-dropdown>
            <n-button class="mr-[8px]" @click="handleShowRecordModal">
              {{ t('customer.common.addUpRecord') }}
            </n-button>
            <n-button class="mr-[8px]" @click="handleRetreat">
              {{ t('customer.common.customerReturn') }}
            </n-button>
            <n-button class="mr-[20px]" type="primary" @click="handleAddBusiness">
              {{ t('customer.common.addBusiness') }}
            </n-button>
            <n-button class="mr-[12px]" text @click="handleExpand">
              <template #icon>
                <n-icon>
                  <ExpandOutline />
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </template>
      <n-spin :show="showLoading">
        <div class="lead-detail-container">
          <!-- Main Content -->
          <div class="main-content">
            <div class="left-panel">
              <div class="px-[16px] py-[18px] user-info">
                <div class="flex items-center relative mb-[12px]">
                  <div
                    class="text-white mr-[8px] border-[2px] flex items-center justify-center rounded-full bg-avatar-blue w-[54px] h-[54px] text-[24px] border-white"
                  >
                    <span>{{ customerDetail?.customer_name[0] }}</span>
                  </div>
                  <div>
                    <div class="w-[110px] text-xl font-bold line-clamp-2 ellipsis overflow-hidden">
                      {{ customerDetail?.customer_name }}
                    </div>
                  </div>
                  <n-image
                    width="70"
                    :preview-disabled="true"
                    class="absolute top-[-2px] right-[-18px]"
                    :src="getImageUrl('admin/pop_customer_khda.png')"
                  />
                </div>
                <div
                  class="flex items-center mb-[4px] w-[195px] line-clamp-1 ellipsis overflow-hidden"
                  @click="handleCopyPhone"
                >
                  <n-button text class="text-text-deep-gray">
                    {{ customerDetail?.phone }}({{ customerDetail?.phone_address }})
                  </n-button>
                  <n-button text class="ml-[4px]">
                    <template #icon>
                      <n-image :preview-disabled="true" width="14" :src="CopyIcon" />
                    </template>
                  </n-button>
                </div>
                <div>
                  <n-button text class="text-text-deep-gray">
                    {{ customerDetail?.company_name }}
                  </n-button>
                </div>
              </div>

              <div class="flex items-center justify-between mb-[4px] mt-5">
                <div class="text-[16px] font-bold">客户概况</div>
              </div>
              <n-descriptions
                bordered
                size="small"
                :column="1"
                label-placement="left"
                label-align="left"
                label-class="w-[98px] p-0"
              >
                <n-descriptions-item
                  :label="t(`leads.common.${item.key}`) + ':'"
                  v-for="item in customerDetailList"
                  :key="item.key"
                >
                  <template #default>
                    <div v-if="item.key === 'followStatus'" class="flex items-center">
                      <n-image
                        v-if="getFollowStatusIcon(customerDetail?.follow_status)"
                        :preview-disabled="true"
                        :src="getFollowStatusIcon(customerDetail?.follow_status)"
                        width="14"
                        height="14"
                      />
                      <span class="ml-1">{{ customerDetail?.follow_status_text || '/' }}</span>
                    </div>
                    <div v-else-if="item.key === 'callStatus'" class="flex items-center">
                      <n-icon>
                        <Ellipse :color="callStatusColor(customerDetail?.call_status)" size="8" />
                      </n-icon>
                      <span class="ml-1">{{ customerDetail?.call_status_text || '/' }}</span>
                    </div>
                    <span v-else>{{ customerDetail?.[item.value] || '/' }}</span>
                  </template>
                </n-descriptions-item>
              </n-descriptions>
              <n-divider />

              <!-- Basic Information -->
              <div class="flex items-center justify-between mb-[4px] mt-5">
                <div class="text-[16px] font-bold">{{ t('leads.common.basicInformation') }}</div>
                <div class="text-sm text-gray-500">
                  <n-button text @click="handleEditCustmoer">
                    <template #icon>
                      <n-icon><EditOutlined /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
              <n-descriptions
                :column="1"
                bordered
                size="small"
                label-placement="left"
                label-align="left"
                label-class="w-[98px] p-0"
              >
                <n-descriptions-item
                  :label="t(`leads.common.${item.key}`) + ':'"
                  v-for="item in leadInfoList"
                  :key="item.key"
                >
                  {{ customerDetail?.[item.value] || '/' }}
                </n-descriptions-item>
              </n-descriptions>
            </div>
            <div class="right-panel">
              <div class="lead-tags mb-4 p-[24px]">
                <div class="text-title-deep-gray font-bold mb-[9px]">客户标签</div>
                <div class="flex items-center">
                  <n-tag
                    class="mr-[8px]"
                    size="small"
                    v-for="(tag, index) in leadTags?.selected_list"
                    :key="index"
                    closable
                    :color="{ color: tag?.color }"
                  >
                    {{ tag?.name || '' }}
                  </n-tag>
                  <n-button text type="primary" size="small" @click="handleAddTag">+ 添加</n-button>
                </div>
              </div>
              <n-divider />

              <!-- 线索SOP -->
              <SopExecute
                ref="sopExecuteRef"
                class="p-[24px]"
                :id="customerId"
                :isExpand="isExpand"
                :type="props.businessType"
                :fromType="props.fromType"
              />
              <n-divider />

              <div class="px-[24px]">
                <n-tabs type="line" v-model:value="currentTab" size="small">
                  <n-tab-pane name="Business" tab="商机" />
                  <n-tab-pane name="Collaborator" tab="协作员" />
                  <n-tab-pane name="FollowRecord" tab="跟进记录" />
                  <n-tab-pane name="Record" tab="操作记录" />
                </n-tabs>
                <Business
                  v-if="currentTab === 'Business'"
                  class="mt-[16px]"
                  ref="businessRef"
                  :fromType="props.fromType"
                  :customerId="customerId"
                  :customerInfo="customerInfo"
                  :directorName="directorName"
                  @reload="getDetailData"
                />
                <Collaborator
                  v-if="currentTab === 'Collaborator'"
                  class="mt-[16px]"
                  ref="CollaboratorRef"
                  :fromType="props.fromType"
                  :customerId="customerId"
                  @reload="getDetailData"
                />
                <FollowRecord
                  v-if="currentTab === 'FollowRecord'"
                  class="mt-[16px]"
                  ref="followRecordRef"
                  :fromType="props.fromType"
                  :customerId="customerId"
                />
                <Record
                  v-if="currentTab === 'Record'"
                  class="mt-[16px]"
                  :customerId="customerId"
                  :fromType="props.fromType"
                />
              </div>
            </div>
          </div>
        </div>
      </n-spin>
    </n-drawer-content>
  </n-drawer>

  <!-- 弹窗 -->
  <CustmoerTagsModal
    ref="leadTagsModalRef"
    :fromType="props.fromType"
    @reload="getFormatLabelsData"
  />
  <CreateEditCustomerModal
    ref="CreateEditCustomerModalRef"
    :isEdit="isEdit"
    :listAll="props.listAll"
    :fromType="props.fromType"
    @reload="getDetailData"
  />

  <!-- 跟进记录弹窗 -->
  <CreateFollowRecord
    ref="createFollowRecordRef"
    :fromType="props.fromType"
    @reload="onFollowRecordReload"
  />
</template>

<script setup lang="ts">
  import { ref, unref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import {
    useMessage,
    useDialog,
    NButton,
    NIcon,
    NDescriptions,
    NDescriptionsItem,
    NTag,
  } from 'naive-ui'
  import { type ListAllType } from '../config/customerTypes'
  import {
    getFormatLabels,
    FormatLabels,
    getCustomerDetail,
    CustomerDetail,
    changeCallStatus,
  } from '@/api/admin/customer/myCustomer'
  import { type FollowStatusType } from '../config/customerTypes'

  import { EditOutlined } from '@vicons/antd'
  import { ExpandOutline, Ellipse } from '@vicons/ionicons5'

  import {
    CopyIcon,
    Invalid,
    ToBack,
    Allocated,
    FollowProgress,
    FollowedUp,
  } from '@/utils/imagesImport'
  import { getImageUrl } from '@/utils/assetUtils'

  import { colorMap } from '@/settings/designSetting'

  import { SopExecute } from '@/components/SopExecute'
  import CustmoerTagsModal from './CustmoerTagsModal.vue'
  import CreateEditCustomerModal from './CreateEditCustomerModal.vue'
  import FollowRecord from './FollowRecord.vue'
  import CreateFollowRecord from './CreateFollowRecord.vue'
  import Record from './Record.vue'
  import { useCopyToClipboard } from '@/hooks/web/useCopyToClipboard'
  import { useRetreatLead } from '../hooks'
  import Collaborator from './Collaborator.vue'
  import Business from './Business.vue'
  const { clipboardRef, copiedRef } = useCopyToClipboard()

  const message = useMessage()
  const dialog = useDialog()
  const { t } = useI18n()

  const active = ref(false)
  const showLoading = ref(false)
  const leadTagsModalRef = ref()

  const props = defineProps({
    listAll: {
      type: Object as PropType<ListAllType>,
      default: () => ({}),
    },
    // 页面标识
    fromType: {
      type: Number as PropType<number>,
      default: 0,
    },
    // 业务模块标识
    businessType: {
      type: String as PropType<string>,
      default: '',
    },
  })
  const emit = defineEmits(['reload'])

  // 线索详情
  const customerDetailList = [
    { key: 'createTime', value: 'created_at' },
    { key: 'currentPage', value: 'current_position' },
    { key: 'department', value: 'department_name' },
    { key: 'assignTime', value: 'first_assign_time' },
    { key: 'owner', value: 'director_name' },
    { key: 'collaborator', value: 'collaborator_text' },
    { key: 'assignedBy', value: 'assigner_name' },
    { key: 'lastOperationTime', value: 'updated_at' },
    { key: 'callStatus', value: 'call_status_text' },
    { key: 'followStatus', value: 'follow_status_text' },
    { key: 'firstAssignTime', value: 'first_assign_time' },
    { key: 'creator', value: 'creator' },
  ]
  // 基本信息
  const leadInfoList = [
    { key: 'customerName', value: 'customer_name' },
    { key: 'customerPhone', value: 'phone' },
    { key: 'customerWechat', value: 'wechat' },
    { key: 'customerSex', value: 'sex_text' },
    { key: 'customerAge', value: 'age' },
    { key: 'companyName', value: 'company_name' },
    { key: 'industry', value: 'industry_name' },
    { key: 'location', value: 'location_address' },
    { key: 'locationDetail', value: 'address' },
    { key: 'demandSource', value: 'source_name' },
    { key: 'remark', value: 'remark' },
  ]

  const isExpand = ref(false)
  function handleExpand() {
    isExpand.value = !isExpand.value
  }
  // 客户ID
  const customerId = ref<string>()
  // 客户信息
  const customerInfo = ref()
  // 责任人
  const directorName = ref<string>()
  // 客户详情 + 基本信息
  const customerDetail = ref<CustomerDetail>()

  const leadTags = ref<FormatLabels>()

  // 跟进状态
  const followStatusList = ref<FollowStatusType[]>([
    {
      id: 1,
      name: '待联系',
    },
    {
      id: 2,
      name: '未接通',
    },
    {
      id: 3,
      name: '已接通',
    },
    {
      id: 4,
      name: '有效沟通',
    },
    {
      id: 5,
      name: '深度沟通',
    },
  ])

  // 复制号码
  function handleCopyPhone() {
    clipboardRef.value = customerDetail.value?.phone || ''
    if (unref(copiedRef)) {
      message.success(t('common.copySuccess'))
    }
  }

  // 刷新跟进记录列表
  const followRecordRef = ref()
  function onFollowRecordReload() {
    // 跟进记录列表
    followRecordRef.value?.handleFollowRecord?.()
  }
  const currentTab = ref('Business')
  const CollaboratorRef = ref()

  const sopExecuteRef = ref()
  const businessRef = ref()
  // 查看线索详情
  async function showDrawer(id: string) {
    currentTab.value = ''
    customerId.value = id
    // 获取线索详情
    await getDetailData()
    // 获取线索标签
    await getFormatLabelsData()
    // 重载商机列表
    currentTab.value = 'Business'
    // 获取线索SOP
    sopExecuteRef.value?.getSopListData()
    active.value = true
  }

  // 获取线索详情
  function getDetailData() {
    showLoading.value = true
    getCustomerDetail(customerId.value as string)
      .then((res) => {
        customerDetail.value = res
        customerInfo.value = {
          customerName: res.customer_name,
          phone: res.phone,
          customerId: customerId.value,
        }
        directorName.value = res.director_name
        showLoading.value = false
      })
      .finally(() => {
        showLoading.value = false
      })
  }

  // 获取线索标签
  function getFormatLabelsData() {
    getFormatLabels(Number(customerId.value)).then((res) => {
      leadTags.value = res as unknown as FormatLabels
    })
  }

  // 关闭线索详情
  function closeDrawer() {
    active.value = false
  }

  // 写跟进
  const createFollowRecordRef = ref()
  function handleShowRecordModal() {
    createFollowRecordRef.value?.showModal(customerId.value)
  }
  // 退回公海
  function handleRetreat() {
    const { handleRetreatLead } = useRetreatLead([String(customerId.value)], props.fromType, true)

    handleRetreatLead(() => {
      getDetailData()
      closeDrawer()
      emit('reload')
    })
  }

  // 修改跟进状态
  function handleStateChange(key: string, option: FollowStatusType) {
    dialog.warning({
      title: t('common.warningText'),
      content: t('leads.common.confirmChangeStatus') + option.name,
      positiveText: t('common.okText'),
      negativeText: t('common.cancelText'),
      draggable: false,
      onPositiveClick: () => {
        changeCallStatus(Number(customerId.value), Number(key), props.fromType).then(() => {
          message.success(t('common.operationSuccess'))
          getDetailData()
        })
      },
    })
  }

  // 添加商机
  function handleAddBusiness() {
    console.log('Add business')
  }

  // 添加标签
  function handleAddTag() {
    leadTagsModalRef.value.showModal(customerId.value, leadTags.value)
  }

  /**
   * 编辑线索
   */
  const CreateEditCustomerModalRef = ref()
  const isEdit = ref(false)
  function handleEditCustmoer() {
    isEdit.value = true
    CreateEditCustomerModalRef.value?.showModal(
      t('customer.common.editCustomer'),
      unref({
        ...customerDetail.value,
        location_array: Number(
          customerDetail.value?.location_district_id || customerDetail.value?.location_city_id,
        ),
      }),
    )
  }

  function getFollowStatusIcon(status: number | undefined) {
    switch (status) {
      case 1:
        return Allocated
      case 2:
        return FollowedUp
      case 3:
        return FollowProgress
      case 4:
        return ToBack
      case 5:
        return Invalid
      default:
        return ''
    }
  }
  function callStatusColor(status: number | undefined) {
    let color = ''
    if (status === 1) {
      color = colorMap['orange-dot']
    } else if (status === 2) {
      color = colorMap['red-dot']
    } else if (status === 3) {
      color = colorMap['green-dot']
    } else if (status === 4) {
      color = colorMap['blue-dot']
    } else if (status === 5) {
      color = colorMap['purple-dot']
    }
    return color
  }

  defineExpose({
    showDrawer,
    closeDrawer,
  })
</script>

<style lang="scss" scoped>
  :deep(.n-drawer-body-content-wrapper) {
    padding: 0 !important;
  }
</style>
<style scoped>
  .user-info {
    background: url('@/assets/images/admin/pop_customer_bg.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .lead-detail-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .main-content {
    display: flex;
    /* gap: 20px; */
    height: 100%;
    overflow: hidden;
  }

  .left-panel {
    flex: 0 0 300px;
    padding: 24px;
    overflow-y: auto;
    border-right: 1px solid #e5e5e5;
  }

  .right-panel {
    flex: 1;
    overflow-y: auto;
    /* padding: 24px; */
  }

  .client-name {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
  }

  .client-name h3 {
    margin: 0;
  }
</style>
