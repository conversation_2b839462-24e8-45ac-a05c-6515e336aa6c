<template>
  <div>
    <n-space class="flex items-center justify-between">
      <n-input
        class="w-[320px]"
        v-model="employee_name"
        @keyup.enter="reloadTable"
        placeholder="请输入协作员名称"
      />
      <n-button type="info" @click="showAddCollaboratorDialog">
        {{ t('customer.common.addCollaborator') }}
      </n-button>
    </n-space>
    <BasicTable
      ref="CollaboratorRef"
      :columns="columns"
      :request="loadCollaboratorData"
      :row-key="(row) => row.id"
      :actionColumn="actionColumn"
      :autoScrollX="true"
      :showTableSetting="false"
      :isShowDivider="false"
      :paginate-single-page="false"
    >
      <template #empty>
        <EmptyBlock icon="msg" emptyText="暂无数据" />
      </template>
    </BasicTable>
    <basicModal preset="dialog" ref="basicModalRef" @register="modalRegister" @on-ok="formSubmit">
      <template #default>
        <div class="mt-6 no-padding">
          <BasicForm ref="basicFormRef" @register="CollaboratorRegister" />
        </div>
      </template>
    </basicModal>
  </div>
</template>

<script setup lang="ts">
  import { ref, h, computed, reactive, defineProps, onMounted } from 'vue'
  import { getSelectCollaboratorTree } from '@/api/admin/customer/myCustomer'
  import {
    getCollaborator,
    transferPermission,
    deleteCollaborator,
    addCollaborator,
    Collaborator,
  } from '@/api/admin/customer/myCustomer'
  import { BasicForm, useForm } from '@/components/Form'
  import { BasicColumn, BasicTable, TableAction } from '@/components/Table'
  import { useI18n } from '@/hooks/web/useI18n'
  import { NSwitch, useMessage } from 'naive-ui'
  import { formatToDateTime } from '@/utils'
  import { collaboratorFormSchemas } from '../config/schemas'
  import { useModal } from '@/components/Modal'
  import { EmptyBlock } from '@/components/Empty'

  const { t } = useI18n()
  const message = useMessage()
  const CollaboratorRef = ref()
  const collaboratorTree = ref<Collaborator[]>([])
  const props = defineProps({
    customerId: {
      type: String,
      default: '',
    },
    fromType: {
      type: Number,
      default: 0,
    },
  })
  const emit = defineEmits(['reload'])
  /**
   * 注册添加协作员表单
   */
  const [CollaboratorRegister, { submit }] = useForm({
    gridProps: { cols: 1 },
    layout: 'horizontal',
    labelWidth: 'auto',
    showActionButtonGroup: false,
    schemas: computed(() => collaboratorFormSchemas(collaboratorTree.value)),
    requireMarkPlacement: 'left',
    labelPlacement: 'top',
  })

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  const actionColumn: BasicColumn = reactive({
    width: 80,
    title: t('common.actionText'),
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'text',
        actions: [
          {
            label: t('common.deleteText'),
            onPositiveClick: handleDelete.bind(null, record),
            onNegativeClick: cancelDelete.bind(null, record),
            isConfirm: true,
            confirmContent: t('common.deleteConfirmContent'),
            positiveText: t('common.confirmText'),
          },
        ],
      })
    },
  })

  const columns: BasicColumn[] = [
    {
      title: t('customer.common.collaboratorName'),
      key: 'employee_name',
    },
    {
      title: t('customer.common.collaboratorAccount'),
      key: 'username',
    },
    {
      title: t('customer.common.collaboratorDepartment'),
      key: 'department_link',
    },
    {
      title: t('customer.common.collaboratorRole'),
      key: 'roles_link',
    },
    {
      title: t('customer.common.collaboratorPermission'),
      key: 'permission',
      align: 'center',
      render(row) {
        return h(NSwitch, {
          value: row.permission === 2 ? true : false,
          'onUpdate:value': (value) => {
            row.status = value
            addonPermissionStatus(row.id)
          },
        })
      },
    },
    {
      title: t('common.createTimeText'),
      key: 'created_at',
      render(row) {
        return formatToDateTime(row.created_at)
      },
    },
  ]

  let addonPermissionStatus = async (id: number) => {
    await transferPermission(id, props.fromType)
    message.success(t('common.operationSuccess'))
    reloadTable()
  }

  const employee_name = ref('')
  const loadCollaboratorData = async (params: any) => {
    const res = await getCollaborator(
      { ...params, employee_name: employee_name.value, type: 2, relationship_id: props.customerId },
      props.fromType,
    )
    return res
  }

  /**
   * 处理删除确认
   * @param record - 要删除的行数据
   * @desc 执行删除操作并刷新表格
   */
  async function handleDelete(record: Recordable) {
    try {
      await deleteCollaborator(record.id, props.fromType)
      message.success(t('common.deleteSuccess'))
      reloadTable()
    } catch (error) {
      console.error('删除失败:', error)
      message.error(t('common.deleteError'))
    }
  }

  /**
   * 处理删除取消
   * @param record - 取消删除的行数据
   */
  function cancelDelete(record: Recordable) {
    console.log('取消删除操作:', record)
  }

  /**
   * 刷新表格
   */
  function reloadTable() {
    CollaboratorRef.value.reload()
  }

  /**
   * 显示添加协作员弹窗
   */
  function showAddCollaboratorDialog() {
    openModal(t('customer.common.addCollaborator'))
  }

  /**
   * 提交表单
   * 验证表单并提交数据
   */
  async function formSubmit() {
    try {
      setSubLoading(true)
      const formData = await submit()
      if (!formData) return
      console.log('表单返回值：', formData)

      formData.type = 2
      formData.relationship_id = props.customerId
      await addCollaborator(formData, props.fromType)
      reloadTable()
      message.success(t('common.addSuccess'))
      emit('reload')
      closeModal()
    } catch (error: any) {
      message.error(error.message || t('common.operationFailed') || '操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  onMounted(async () => {
    collaboratorTree.value = await getSelectCollaboratorTree(props.fromType)

    // 处理协作员树数据，为最末级且is_employee为2的节点添加disabled属性
    collaboratorTree.value = collaboratorTree.value.map((item) => {
      const processNode = (node: any): any => {
        const isLeaf = !node.children || node.children.length === 0

        if (isLeaf && node.is_employee === 2) {
          return {
            ...node,
            disabled: true,
          }
        }

        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: node.children.map(processNode),
          }
        }

        return node
      }

      return processNode(item)
    })
  })

  defineExpose({
    reloadTable,
  })
</script>

<style scoped lang="scss"></style>
