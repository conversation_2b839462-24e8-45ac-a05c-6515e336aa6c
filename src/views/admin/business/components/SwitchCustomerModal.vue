<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="no-padding">
        <div>
          <n-input
            class="w-[320px]"
            v-model:value="searchValue"
            placeholder="请输入客户名称或手机号"
            @keyup.enter="handleSearch"
            clearable
          >
            <template #suffix>
              <n-icon @click="handleSearch" style="cursor: pointer">
                <SearchOutlined />
              </n-icon>
            </template>
          </n-input>
        </div>
        <BasicTable
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row) => row.id"
          :showTableSetting="false"
          height="700px"
          ref="tableRef"
          :autoScrollX="true"
          @checked-row-change="handleCheck"
        >
          <template #empty>
            <EmptyBlock icon="msg" emptyText="暂无客户" />
          </template>
        </BasicTable>
        <div class="mt-4" v-if="globalSelectedCustomers.length > 0">
          <div class="mb-2 text-sm text-gray-600">已选择客户：</div>
          <n-tag type="primary" closable @close="clearAllSelected">
            {{ globalSelectedCustomers[0].customer_name }}
          </n-tag>
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { basicModal, useModal } from '@/components/Modal'
  import { BasicColumn, BasicTable } from '@/components/Table'
  import { getCustomerList } from '@/api/admin/business/myBusiness'
  import type { CustomerList } from '@/api/admin/business/type'
  import { renderCustomer } from '../config/renderColumns'
  import { SearchOutlined } from '@vicons/antd'
  import { EmptyBlock } from '@/components/Empty'
  const { t } = useI18n()

  /**
   * 组件属性
   */
  const props = defineProps<{
    /** 预选的客户列表，用于回显已选择的客户 */
    preSelectedCustomers?: CustomerList[]
    /** 最大选择数量限制，0表示无限制 */
    maxSelectCount?: number
    /** 是否显示选择数量统计 */
    showSelectCount?: boolean
  }>()

  /**
   * 组件事件
   * @property {Function} reload - 客户选择完成后触发，传递选中的客户数据
   */
  const emit = defineEmits<{
    selectCustomer: [data: { customerIds: number[]; customers: CustomerList[] }]
  }>()

  const custmoerId = ref<number | null>(null)
  const tableRef = ref()

  // 标志位，用于避免程序化设置选中状态时触发处理逻辑
  const isSettingCheckedKeys = ref(false)

  const columns: BasicColumn[] = [
    {
      type: 'selection',
      multiple: false, // 设置为单选模式
    },
    {
      title: t('customer.common.customerName'),
      key: 'customer_name',
      width: 165,
      minWidth: 165,
      render(row) {
        return renderCustomer(row)
      },
    },
    {
      title: t('customer.common.companyName'),
      key: 'company_name',
    },
    {
      title: '成交状态',
      key: 'transaction_status_text',
    },
    {
      title: 'SOP 阶段',
      key: 'sop_stage_title',
    },
  ]

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 900,
    fullscreen: true,
  })

  // 存储表格数据
  const tableData = ref<CustomerList[]>([])
  const checkedIds = ref<number[]>([]) // 当前页勾选的ID列表

  // 全局选择的客户列表（单选模式）
  const globalSelectedCustomers = ref<CustomerList[]>([])

  // 计算属性：获取全局选择的客户ID列表
  const globalSelectedIds = computed(() => {
    return globalSelectedCustomers.value.map((customer) => customer.id)
  })

  /**
   * 表单提交
   */
  const formSubmit = () => {
    console.log('全局选择的客户ID:', globalSelectedIds.value)
    console.log('全局选择的客户数据:', globalSelectedCustomers.value)

    // 传递全局选择的客户数据给父组件
    emit('selectCustomer', {
      customerIds: globalSelectedIds.value,
      customers: globalSelectedCustomers.value,
    })

    closeModal()
  }

  /**
   * 显示弹窗
   */
  function showModal(id: number, preSelectedCustomers?: CustomerList[]) {
    custmoerId.value = id

    // 优先使用方法参数传入的预选客户，其次使用props中的预选客户
    const selectedCustomers = preSelectedCustomers || props.preSelectedCustomers || []

    // 设置全局选择（深拷贝避免引用问题）
    globalSelectedCustomers.value =
      selectedCustomers.length > 0 ? JSON.parse(JSON.stringify(selectedCustomers)) : []

    // 清空当前页勾选状态
    checkedIds.value = []
    openModal('选择客户')
  }

  /**
   * 单选处理
   */
  function handleCheck(ids: number[]) {
    // 如果是程序化设置选中状态，则不处理
    if (isSettingCheckedKeys.value) {
      return
    }

    // 在单选模式下，只保留最新的选择
    const latestId = ids.length > 0 ? [ids[ids.length - 1]] : []
    checkedIds.value = latestId

    // 获取当前页面的客户数据
    const currentPageCustomers = tableData.value
    console.log(currentPageCustomers)

    // 单选模式下，直接设置选中的客户
    if (latestId.length > 0) {
      const selectedCustomer = currentPageCustomers.find((customer) => customer.id === latestId[0])
      console.log(selectedCustomer)

      if (selectedCustomer) {
        globalSelectedCustomers.value = [selectedCustomer]
      }
    } else {
      // 取消选择
      globalSelectedCustomers.value = []
    }
  }

  /**
   * 搜索处理
   */
  function handleSearch() {
    // 清空当前页勾选状态（不清空全局选择）
    checkedIds.value = []
    // 刷新表格数据
    tableRef.value?.reload()
  }

  /**
   * 清空选择
   */
  function clearAllSelected() {
    globalSelectedCustomers.value = []
    checkedIds.value = []
    // 设置标志位，避免触发 handleCheck
    isSettingCheckedKeys.value = true
    // 清空表格勾选状态
    tableRef.value?.setCheckedRowKeys([])
    // 重置标志位
    setTimeout(() => {
      isSettingCheckedKeys.value = false
    }, 50)
  }

  const formParams = ref({
    scene: 'opportunity_select_customer',
  })
  const searchValue = ref('')

  /**
   * 加载数据表格数据
   * @param params - 请求参数
   * @returns 返回客户列表数据
   */
  const loadDataTable = async (params: any) => {
    try {
      // 合并搜索参数
      const searchParams = searchValue.value ? { mixNameOrPhone: searchValue.value } : {}
      const res = await getCustomerList({
        ...params,
        ...formParams.value,
        ...searchParams,
      })
      console.log(res)

      // 保存表格数据用于后续获取勾选的完整数据
      tableData.value = res.list

      // 数据加载完成后，自动回选当前页面中已在全局选择中的客户
      setTimeout(() => {
        const shouldCheckedIds = globalSelectedIds.value.filter((id) =>
          tableData.value.some((customer) => customer.id === id),
        )
        if (shouldCheckedIds.length > 0) {
          checkedIds.value = shouldCheckedIds
          // 设置标志位，避免触发 handleCheck
          isSettingCheckedKeys.value = true
          // 设置表格的勾选状态
          tableRef.value?.setCheckedRowKeys(shouldCheckedIds)
          // 重置标志位
          setTimeout(() => {
            isSettingCheckedKeys.value = false
          }, 50)
        }
      }, 100)

      // 返回表格组件期望的分页格式
      return res
    } catch (error) {
      console.error('加载客户列表失败:', error)
      // 发生错误时返回空数据
      tableData.value = []
      return {
        list: [],
        page_count: 0,
        row_count: 0,
      }
    }
  }

  /**
   * 对外暴露的方法
   */
  defineExpose({
    showModal,
    closeModal,
  })
</script>
