import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { dateShortcuts } from '@/utils'
import { ListAllType } from '../../config/customerTypes'
import { Collaborator } from '@/api/admin/customer/myCustomer'
const { t } = useI18n()

// 我的商机搜索栏
export function querySchemas(listAll: ListAllType): FormSchema[] {
  return [
    {
      field: 'title',
      component: 'NInput',
      label: t('customer.common.businessName'),
      componentProps: {
        placeholder: t('customer.common.businessNamePlaceholder'),
      },
    },
    {
      field: 'status',
      component: 'NSelect',
      label: '商机状态',
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: [
          { label: '跟进中', value: 1 },
          { label: '赢单', value: 2 },
          { label: '输单', value: 3 },
        ],
      },
    },
    {
      field: 'customer_name',
      component: 'NInput',
      label: t('customer.common.customerName'),
      componentProps: {
        placeholder: t('customer.common.customerNamePlaceholder'),
      },
    },
    {
      field: 'phone',
      component: 'NInput',
      label: t('customer.common.customerPhone'),
      componentProps: {
        placeholder: t('customer.common.customerPhonePlaceholder'),
      },
    },
    {
      field: 'company_name',
      component: 'NInput',
      label: t('customer.common.companyName'),
      componentProps: {
        placeholder: t('customer.common.companyNamePlaceholder'),
      },
    },

    {
      field: 'label_id',
      component: 'NSelect',
      label: '客户标签',
      componentProps: {
        placeholder: t('customer.common.selectPlaceholder'),
        options: listAll.customerLabelList,
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      field: 'created_at',
      component: 'NDatePicker',
      label: '创建时间',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 导出弹窗
export function getTaskFields(exportList: any): FormSchema[] {
  return [
    {
      field: 'filter',
      hidden: true,
    },
    {
      field: 'key',
      label: t('customer.common.exportInfo'),
      component: 'NCheckbox',
      slot: 'select_all',
    },
    {
      field: 'checked',
      label: ' ',
      component: 'NCheckbox',
      slot: 'checked_Field',
      componentProps: {
        options: exportList,
      },
    },
  ]
}

//协作员搜索栏
export function collaboratorSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'employee_name',
      component: 'NInput',
      componentProps: {
        placeholder: t('customer.common.pleaseInputEmployeeName'),
      },
    },
  ]
}

// 添加协作员表单
export function collaboratorFormSchemas(list: Collaborator[]): FormSchema[] {
  return [
    {
      field: 'type',
      hidden: true,
    },
    {
      field: 'user_ids',
      component: 'NTreeSelect',
      label: t('customer.common.selectCollaborator'),
      componentProps: {
        options: list,
        labelField: 'name',
        keyField: 'id',
        defaultExpandAll: true,
        multiple: true,
        overrideDefaultNodeClickBehavior: ({ option }) => {
          if (option.children) {
            return 'toggleExpand'
          }
          return 'default'
        },
      },
      rules: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: t('customer.common.pleaseSelectCollaborator'),
        },
      ],
    },
    {
      field: 'permission',
      component: 'NRadioGroup',
      label: t('customer.common.dataPermission'),
      componentProps: {
        options: [
          { label: '只读', value: 1 },
          { label: '读写', value: 2 },
        ],
      },
    },
  ]
}

// 回退线索表单
export function getReturnLeadSchema(): FormSchema[] {
  return [
    {
      field: 'ids',
      hidden: true,
    },
    {
      field: 'customer_back_reason',
      label: t('customer.common.returnReason'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        showCount: true,
      },
      rules: [
        {
          pattern: /^.{0,50}$/,
          message: t('customer.common.returnReasonRequired'),
        },
        {
          required: true,
          message: t('customer.common.returnReasonRequired'),
        },
      ],
    },
  ]
}

// 操作记录搜索栏
export function recordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'created_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

export function getExportRecordSchema(): FormSchema[] {
  return [
    {
      field: 'file_name',
      label: t('customer.common.pleaseInputFileName'),
      rules: [
        {
          required: true,
          message: t('customer.common.pleaseInputFileName'),
          'show-require-mark': false,
        },
      ],
    },
  ]
}

// 跟进记录搜索栏
export function followRecordSearchSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_at',
      component: 'NDatePicker',
      componentProps: {
        type: 'datetimerange',
        valueFormat: 'yyyy-MM-dd',
        shortcuts: dateShortcuts,
      },
    },
  ]
}

// 跟进记录表单
export function followRecordSchemas(): FormSchema[] {
  return [
    {
      field: 'follow_up_content',
      label: t('customer.common.followRecord'),
      component: 'NInput',
      componentProps: {
        type: 'textarea',
        maxlength: 200,
        showCount: true,
        placeholder: '请输入跟进记录',
      },
    },
    {
      field: 'follow_up_at',
      label: t('customer.common.followTime'),
      component: 'NDatePicker',
      componentProps: {
        type: 'datetime',
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.pleaseInputFollowTime'),
      },
    },
  ]
}

// 商机搜索栏
export function businessSearchSchemas(searchField = 'title'): FormSchema[] {
  // 定义所有可选字段的 schema
  const fieldMap = {
    title: {
      field: 'filter_values',
      component: 'NInput',
      componentProps: { placeholder: '请输入商机名称' },
    },
    customer_name: {
      field: 'filter_values',
      component: 'NInput',
      componentProps: { placeholder: '请输入客户名称' },
    },
    sop_stage_title: {
      field: 'filter_values',
      component: 'NInput',
      componentProps: { placeholder: '请输入商机阶段' },
    },
  }
  return [
    // 选择字段
    {
      field: 'filter_fields',
      component: 'NSelect',
      componentProps: {
        options: [
          { label: '商机名称', value: 'title' },
          { label: '客户名称', value: 'customer_name' },
          { label: '商机阶段', value: 'sop_stage_title' },
        ],
        placeholder: '选择信息',
      },
      defaultValue: 'title',
    },
    // 动态输入框
    fieldMap[searchField],
  ]
}

// 新增/编辑商机表单
export function businessFormSchemas(): FormSchema[] {
  return [
    {
      field: 'title',
      label: t('customer.common.businessName'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.businessNameRequired') },
        { pattern: /^.{1,30}$/, message: t('customer.common.businessNameRequired') },
      ],
      componentProps: {
        showCount: true,
        maxlength: 30,
        placeholder: t('customer.common.businessNameRequired'),
      },
    },
    {
      field: 'customer_id',
      hidden: true,
    },
    {
      field: 'user_ids',
      label: t('customer.common.collaborator'),
      component: 'NTreeSelect',
      rules: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: t('customer.common.pleaseSelectCollaborator'),
        },
      ],
      componentProps: {
        multiple: true,
        placeholder: t('customer.common.pleaseSelectCollaborator'),
      },
    },
    {
      field: 'permission',
      label: t('customer.common.dataPermission'),
      component: 'NRadioGroup',
      rules: [{ required: true, message: t('customer.common.dataPermission') }],
      componentProps: {
        options: [
          { label: '只读', value: 1 },
          { label: '读写', value: 2 },
        ],
      },
    },
    {
      field: 'expected_win_rate',
      label: t('customer.common.estimatedWinRate'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.estimatedWinRate') },
        { pattern: /^([1-9][0-9]?|100)$/, message: '请输入1-100的整数' },
      ],
      componentProps: {
        placeholder: '请输入1-100的整数',
        suffix: '%',
        maxlength: 3,
      },
    },
    {
      field: 'expected_transaction_amount',
      label: t('customer.common.estimatedWinAmount'),
      component: 'NInput',
      rules: [
        { required: true, message: t('customer.common.estimatedWinAmount') },
        { pattern: /^\d+$/, message: '请输入数字' },
      ],
      componentProps: {
        placeholder: '请输入预计成交金额',
        suffix: '元',
      },
    },
    {
      field: 'expected_transaction_at',
      label: t('customer.common.estimatedWinDate'),
      component: 'NDatePicker',
      rules: [{ required: true, message: t('customer.common.estimatedWinDate') }],
      componentProps: {
        type: 'date',
        valueFormat: 'yyyy-MM-dd',
        placeholder: t('customer.common.estimatedWinDate'),
      },
    },
    {
      field: 'remark',
      label: t('customer.common.remark'),
      component: 'NInput',
      rules: [{ pattern: /^.{0,100}$/, message: t('customer.common.remarkRequired') }],
      componentProps: {
        type: 'textarea',
        showCount: true,
        maxlength: 100,
        placeholder: t('customer.common.remark'),
      },
    },
    {
      field: 'products',
      label: t('customer.common.productInfo'),
      slot: 'products_info',
      rules: [{ required: true, message: '请添加产品信息' }],
    },
    {
      field: 'whole_order_discount',
      label: '整单折扣',
      component: 'NInput',
      rules: [{ pattern: /^\d*$/, message: '请输入数字' }],
      componentProps: {
        placeholder: '请输入整单折扣',
      },
    },
  ]
}
