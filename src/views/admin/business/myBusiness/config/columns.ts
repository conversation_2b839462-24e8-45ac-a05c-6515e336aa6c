import { BasicColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { formatToDateTime } from '@/utils'
import {
  renderCustomer,
  renderLabelRelationship,
  renderBusStatus,
} from '../../config/renderColumns'

const { t } = useI18n()

export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('customer.common.customerName'),
    key: 'customer_name',
    width: 165,
    minWidth: 165,
    render(row) {
      return renderCustomer(row)
    },
  },
  {
    title: '商机名称',
    key: 'title',
  },
  {
    title: '商机状态',
    key: 'status_text',
    render(row) {
      return renderBusStatus(row)
    },
  },
  {
    title: 'SOP 阶段',
    key: 'sop_stage_title',
  },
  {
    title: '最新跟进记录',
    key: 'follow_up_content',
  },
  {
    title: t('customer.common.companyName'),
    key: 'company_name',
  },
  {
    title: '所属行业',
    key: 'industry_name',
  },
  {
    title: '客户标签',
    key: 'label_relationship',
    render(row) {
      return renderLabelRelationship(row)
    },
  },
]
