<template>
  <div>
    <n-card title="我的商机" :bordered="false" class="full-card">
      <BasicForm
        @register="register"
        @submit="handleSubmit"
        @reset="handleReset"
        :show-reset-button="false"
        :show-advanced-button="false"
        :enableCustomFormItemStyle="true"
      />
      <n-tabs v-model:value="tab_type" type="line" size="medium" @update:value="handleChangeTab">
        <n-tab-pane
          v-for="panel in panels"
          :key="panel.tab_type"
          :tab="panel.label"
          :name="panel.tab_type"
        />
      </n-tabs>
      <BasicTable
        ref="tableRef"
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row) => row.id"
        :actionColumn="actionColumn"
        :autoScrollX="true"
        :showTableSetting="false"
        :isShowDivider="false"
        @update:checked-row-keys="handleCheck"
      >
        <template #tableTitle v-if="tab_type !== 2">
          <n-space>
            <n-button :disabled="btnDisabled" type="info" @click="showExportModal">
              {{ t('customer.common.exportCustomer') }}
            </n-button>
          </n-space>
        </template>
        <template #toolbar>
          <n-button type="primary" @click="showCreateDialog">
            <template #icon>
              <n-icon>
                <PlusOutlined />
              </n-icon>
            </template>
            新增商机
          </n-button>
        </template>
      </BasicTable>
    </n-card>
    <CreateBusinessDrawer ref="createBusinessDrawerRef" />
    <!-- <CreateEditCustomerModal
      ref="CreateEditCustomerModalRef"
      :listAll="listAll"
      :title="t('customer.common.addCustomer')"
      :is-edit="isEdit"
      @reload="reloadTable"
    />

    <DetailCustomerDrawer
      ref="DetailCustomerDrawerRef"
      :fromType="FROM_TYPE_MY_CUSTOMER"
      :businessType="MY_CUSTOMER"
      :listAll="listAll"
      @reload="reloadTable"
    />

    <ExportCustomerModal
      ref="ExportCustomerModalRef"
      :title="t('customer.common.exportCustomer')"
      :fromType="FROM_TYPE_MY_CUSTOMER"
      :exortParams="exortParams"
      @reload="reloadTable"
    />

    <ImportModal ref="ImportModalRef" @reload="reloadTable" />

    <UploadFiles
      ref="UploadFilesRef"
      action="/api/admin/customer/importCustomer"
      @download-template="downloadTemplate"
      @reload="reloadTable"
    />
    <TransferCustomerModal ref="TransferCustomerModalRef" @reload="reloadTable" /> -->
  </div>
</template>

<script setup lang="ts">
  import { PlusOutlined } from '@vicons/antd'
  import { ref, computed, reactive, onMounted, h } from 'vue'
  import { BasicForm, useForm } from '@/components/Form'
  import { useI18n } from '@/hooks/web/useI18n'

  import { getOpportunityList } from '@/api/admin/business/myBusiness'

  import {
    getCustomerFilterOptions,
    getCustomerLabels,
    getTabData,
    getImportTemplate,
  } from '@/api/admin/customer/myCustomer'
  import { querySchemas } from './config/schemas'
  import { columns } from './config/columns'
  import { BasicColumn, BasicTable, TableAction } from '@/components/Table'
  import CreateBusinessDrawer from '../components/CreateBusinessDrawer.vue'

  import CreateEditCustomerModal from '../components/CreateEditCustomerModal.vue'
  import ExportCustomerModal from '../components/ExportCustomerModal.vue'
  import ImportModal from './components/ImportModal.vue'
  import TransferCustomerModal from './components/TransferCustomerModal.vue'
  import { type ListAllType } from '../config/customerTypes'
  import { useAdminSelect } from '@/hooks'
  import { FROM_TYPE_MY_CUSTOMER, FROM_TYPE_MY_BUSINESS } from '@/enums/fromTypeEnum'
  import { MY_CUSTOMER } from '@/enums/sopTypeEnums'
  import { UploadFiles } from '@/components/UploadFiles'

  import DetailCustomerDrawer from '../components/DetailCustomerDrawer.vue'
  import { useRetreatLead } from '../hooks'
  const { t } = useI18n()
  const tableRef = ref()
  const CreateEditCustomerModalRef = ref()
  const ExportCustomerModalRef = ref()
  const ImportModalRef = ref()
  const TransferCustomerModalRef = ref()
  const checkedIds = ref<number[]>([])
  const listAll = reactive<ListAllType>({
    // 所属行业
    industryList: [],
    // 标签列表
    customerLabelList: [],
    //所在地址
    regionList: [],
    //渠道来源
    sourceList: [],
    //导入方法
    entryMethodsList: [],
    //转化方式
    transactionStatusList: [],
    // 跟进状态
    followStatusList: [],
  })
  let tab_type = ref(0)
  const tabData = ref({
    tab_all_count: 0,
    tab_director_count: 0,
    tab_collaborator_count: 0,
    sub_tab_director_all_count: 0,
    sub_tab_collaborator_all_count: 0,
    sub_tab_director_transacted_count: 0,
    sub_tab_director_not_transacted_count: 0,
    sub_tab_collaborator_transacted_count: 0,
    sub_tab_collaborator_not_transacted_count: 0,
  })
  const panels = computed(() => [
    {
      tab_type: 0,
      label: `全部(${tabData.value.tab_all_count})`,
    },
    {
      tab_type: 1,
      label: `我负责的(${tabData.value.tab_director_count})`,
    },
    {
      tab_type: 2,
      label: `我协作的(${tabData.value.tab_collaborator_count})`,
    },
  ])

  const btnDisabled = computed(() => checkedIds.value.length <= 0)

  const actionColumn: BasicColumn = reactive({
    width: 70,
    title: t('common.actionText'),
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'text',
        actions: [
          {
            label: t('customer.common.detail'),
            color: '#2d8cf0',
            onClick: () => handleDetail(record),
            ifShow: () => {
              return true
            },
            auth: ['customer'],
          },
        ],
      })
    },
  })

  /**
   * 查询表单注册
   * 初始化查询表单配置
   */
  const [register, { getFieldsValue }] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:4 xl:4 2xl:4', xGap: 10, collapsedRows: 2 },
    labelWidth: 100,
    schemas: computed(() => querySchemas(listAll)),
  })
  const exortParams = ref()
  const loadDataTable = async (params: any) => {
    const formParams = getFieldsValue()
    exortParams.value = {
      tab_type: tab_type.value,
      scene: 'opportunity_my_page',
    }
    const res = await getOpportunityList({
      ...exortParams.value,
      ...params,
      ...formParams,
    })

    tabData.value = await getTabData()
    res.list.forEach((row) => {
      row._showCustomerDetail = handleDetail
    })
    return res
  }

  /**
   * 导入客户
   */
  const UploadFilesRef = ref()
  function handleImportModal() {
    UploadFilesRef.value.showModal(t('customer.common.importCustomer'))
  }
  /**
   * 下载模板
   */
  function downloadTemplate() {
    getImportTemplate().then((res) => {
      const { url } = res as any
      // 下载 url 文件
      const a = document.createElement('a')
      a.href = url
      a.download = 'template.xlsx'
      a.click()
      // 删除 a 元素
      document.body.removeChild(a)
      // window.open(url, '_blank')
    })
  }

  /**
   * 转移客户弹窗
   */
  function showTransferCustomerModal() {
    TransferCustomerModalRef.value?.showModal(checkedIds.value)
  }

  /**
   * 客户回退弹窗
   */
  function showReturnLeadModal() {
    const { handleRetreatLead } = useRetreatLead(
      checkedIds.value.map((id) => String(id)),
      FROM_TYPE_MY_CUSTOMER,
      false,
    )
    handleRetreatLead(() => {
      reloadTable()
    })
  }

  /**
   * 批量勾选
   */
  function handleCheck(ids: any[]) {
    checkedIds.value = ids
  }

  /**
   * 表单操作方法
   */
  function handleSubmit(values: Recordable) {
    console.log('handleSubmit', values)
    reloadTable()
  }

  function handleReset(values: Recordable) {
    console.log('handleReset', values)
    reloadTable()
  }

  /**
   * 重新加载表格
   */
  function reloadTable() {
    checkedIds.value = []
    tableRef.value.reload()
  }

  /**
   * 打开新建弹窗
   * @desc 初始化新建表单并显示对话框
   */
  const createBusinessDrawerRef = ref()
  function showCreateDialog() {
    createBusinessDrawerRef.value?.openDrawer()
  }

  /**
   * 导出
   */

  function showExportModal() {
    ExportCustomerModalRef.value?.showModal(t('customer.common.exportCustomer'), checkedIds.value)
  }

  async function handleChangeTab() {
    reloadTable()
  }

  /**
   * 查看客户详情
   */
  const DetailCustomerDrawerRef = ref()
  function handleDetail(record: Recordable) {
    DetailCustomerDrawerRef.value.showDrawer(record.id)
  }

  onMounted(async () => {
    const { selectData, fetchRegion, fetchIndustry, fetchSource, fetchFollowStatus } =
      useAdminSelect()

    // 获取需要的选择列表数据
    await Promise.all([fetchRegion(), fetchIndustry(), fetchSource(), fetchFollowStatus()])

    // 使用获取到的数据
    listAll.regionList = selectData.regionList
    listAll.industryList = selectData.industryList
    listAll.sourceList = selectData.sourceList
    listAll.followStatusList = selectData.followStatusList

    // 其他API调用保持不变
    listAll.customerLabelList = await getCustomerLabels()
    const res = await getCustomerFilterOptions()
    listAll.entryMethodsList = res?.entry_methods_options
    listAll.transactionStatusList = res?.transaction_status_options
  })
</script>

<style scoped lang="scss"></style>
