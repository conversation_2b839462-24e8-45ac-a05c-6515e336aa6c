<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    :title="t('customer.common.importCustomer')"
    style="width: 600px"
    :closable="true"
    :loading="subLoading"
  >
    <n-steps :current="step" size="medium" class="mb-4">
      <n-step title="选择文件" />
      <n-step title="导入文件" />
      <n-step title="导入完成" />
    </n-steps>

    <!-- 步骤1：选择文件 -->
    <template v-if="step === 1">
      <div>
        <n-upload :max="1" :default-upload="true" class="w-full" @change="onFileChange">
          <template #default>
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <CloudUploadOutline />
                </n-icon>
              </div>
              <div>
                将文件拖到此处，或
                <span class="upload-link" @click.stop="triggerUpload">点击添加</span>
              </div>
              <div class="upload-tip">支持xlsx、xls、csv格式的文件</div>
              <n-button type="primary" text @click="downloadTemplate" class="mt-2">
                下载导入模板
              </n-button>
            </n-upload-dragger>
          </template>
        </n-upload>
      </div>
    </template>

    <!-- 步骤2：导入文件 -->
    <template v-else-if="step === 2">
      <div
        class="import-upload-area"
        style="display: flex; flex-direction: column; align-items: center; justify-content: center"
      >
        <div>
          <n-icon size="40" class="mb-2" color="#52c41a">
            <DocumentTextOutline />
          </n-icon>
          <div>
            将文件拖到此处，或
            <span class="upload-link" @click="triggerUpload">重新选择</span>
          </div>
          <div class="upload-tip">支持xlsx、xls、csv格式的文件</div>
          <n-space vertical>
            <n-button type="primary" text @click="downloadTemplate" class="mt-2">
              下载导入模板
            </n-button>
            <n-button type="primary" class="mt-2" @click="handleImport" style="width: 120px">
              开始导入
            </n-button>
          </n-space>
        </div>
      </div>

      <!-- 隐藏的上传组件用于步骤2的重新选择 -->
      <n-upload
        ref="uploadRef2"
        :default-upload="false"
        style="position: absolute; left: -9999px; pointer-events: none; opacity: 0"
        @change="onFileChange"
      >
        <template #default>
          <div></div>
        </template>
      </n-upload>
    </template>

    <!-- 步骤3：导入完成 -->
    <template v-else>
      <div class="import-upload-area">
        <n-icon size="48" color="#52c41a" class="mb-2">
          <CheckmarkCircleOutline />
        </n-icon>
        <div style="margin: 12px 0">
          <span style="color: #f5222d">导入失败：{{ failCount }}个</span>
          <span style="margin-left: 16px; color: #52c41a">导入成功：{{ successCount }}个</span>
        </div>
        <n-button type="primary" style="width: 120px" @click="closeModal">完成</n-button>
      </div>
    </template>

    <!-- 注意事项 -->
    <div class="import-notice">
      <span class="notice-title">注：</span>
      <div>1. 一次最多导入3000行记录,超出部分请分批导入</div>
      <div>2. 导入未完成之前，请勿关闭页面，否则会导致导入失败</div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useI18n } from '@/hooks/web/useI18n'
  import { NIcon, useMessage } from 'naive-ui'
  import {
    CloudUploadOutline,
    DocumentTextOutline,
    CheckmarkCircleOutline,
  } from '@vicons/ionicons5'
  import { getImportTemplate, importCustomer } from '@/api/admin/customer/myCustomer'

  const { t } = useI18n()
  const message = useMessage()
  const visible = ref(false)
  const step = ref(1)
  const fileList = ref<{ file: File; raw: File }[]>([])
  const successCount = ref(0)
  const failCount = ref(0)
  const templateUrl = ref('')
  const uploadRef2 = ref()
  const subLoading = ref(false)
  const emit = defineEmits(['reload'])

  function showModal() {
    visible.value = true
    fileList.value = []
    getImportTemplate().then((res) => {
      templateUrl.value = res.url
    })
  }

  function triggerUpload() {
    const uploadInput = document.querySelector('.n-upload input[type=file]') as HTMLInputElement
    uploadInput?.click()
  }

  // 下载模板
  function downloadTemplate() {
    if (!templateUrl.value) return
    window.open(templateUrl.value, '_blank')
  }

  // 检查是有文件上传
  function onFileChange(options: any) {
    // 更新 fileList
    fileList.value = options.fileList || []

    if (fileList.value.length > 0) {
      step.value = 2
    } else {
      step.value = 1
    }
  }
  async function handleImport() {
    if (!fileList.value.length) return
    try {
      subLoading.value = true
      const rawFile =
        fileList.value[fileList.value.length - 1]?.file ||
        fileList.value[fileList.value.length - 1]?.raw ||
        fileList.value[fileList.value.length - 1]
      const formData = new FormData()

      formData.append('file', rawFile)
      console.log(fileList.value)
      const res = await importCustomer(formData)
      successCount.value = res.success_count
      failCount.value = res.failure_count
      step.value = 3
    } catch (error: any) {
      message.error(error.message)
    } finally {
      subLoading.value = false
    }
  }

  function closeModal() {
    visible.value = false
    step.value = 1
    fileList.value = []
    emit('reload')
  }

  defineExpose({ showModal })
</script>

<style lang="scss" scoped>
  .import-upload-area {
    padding: 32px 0;
    margin-bottom: 16px;
    text-align: center;
    background: #fafbfc;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }
  .import-upload-area-step2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 0;
    margin-bottom: 16px;
    text-align: center;
  }
  .upload-link {
    color: #2080f0;
    text-decoration: underline;
    cursor: pointer;
  }
  .upload-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #888;
  }
  .import-notice {
    padding: 8px 12px;
    margin-top: 16px;
    font-size: 13px;
    color: #333;
    background: #f4f8ff;
    border-radius: 4px;
  }
  .notice-title {
    margin-bottom: 2px;
    font-weight: bold;
    color: #2080f0;
  }
</style>
