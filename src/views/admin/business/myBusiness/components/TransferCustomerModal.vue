<template>
  <basicModal ref="basicModalRef" preset="dialog" @register="modalRegister" @on-ok="formSubmit">
    <template #default>
      <div class="mt-6 no-padding">
        <div class="w-full text-text-deep-gray">
          已选择
          <span class="text-blue-deep">{{ customerIds.length }}</span>
          个客户，将选中客户转移至：
        </div>
        <div class="mt-[6px]">
          <n-tree-select
            v-model:value="selectedUserId"
            :options="userOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择"
            class="w-full"
            :override-default-node-click-behavior="override"
          />
        </div>
        <div v-if="reasonError" style="margin-top: 4px; font-size: 13px; color: #e34d59">
          {{ reasonError }}
        </div>
      </div>
    </template>
  </basicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { basicModal, useModal } from '@/components/Modal'
  import { useI18n } from '@/hooks/web/useI18n'
  import { getAssignPeople, transferCustomers } from '@/api/admin/customer/myCustomer'
  import { useMessage } from 'naive-ui'

  const emit = defineEmits(['reload'])
  const message = useMessage()
  const { t } = useI18n()

  const customerIds = ref<number[]>([])
  const userOptions = ref<any[]>([])
  const selectedUserId = ref<number>(0)
  const showError = ref(false)
  const reasonError = ref('')
  const override = ({ option }) => {
    if (option.children) {
      return 'toggleExpand'
    }
    return 'default'
  }

  /**
   * 注册弹窗
   */
  const [modalRegister, { openModal, closeModal, setSubLoading }] = useModal({
    subBtuText: t('common.confirmText'),
    width: 480,
    fullscreen: true,
  })

  async function showModal(ids: number[]) {
    customerIds.value = ids
    const res = await getAssignPeople() // 获取负责人列表

    // 处理负责人树数据，为最末级且is_employee为2的节点添加disabled属性
    userOptions.value = res.map((item) => {
      const processNode = (node: any): any => {
        const isLeaf = !node.children || node.children.length === 0

        if (isLeaf && node.is_employee === 2) {
          return {
            ...node,
            disabled: true,
          }
        }

        if (node.children && node.children.length > 0) {
          return {
            ...node,
            children: node.children.map(processNode),
          }
        }

        return node
      }

      return processNode(item)
    })

    selectedUserId.value = 0
    showError.value = false
    openModal(t('customer.common.customerTransfer'))
  }

  async function formSubmit() {
    if (!selectedUserId.value) {
      showError.value = true
      return
    }
    showError.value = false
    setSubLoading(true)
    try {
      await transferCustomers({
        ids: customerIds.value,
        user_id: selectedUserId.value,
      })
      message.success('操作成功')
      emit('reload')
      closeModal()
    } catch (e) {
      message.error('操作失败')
    } finally {
      setSubLoading(false)
    }
  }

  defineExpose({ showModal, closeModal })
</script>
