import { Alova } from '@/utils/http/alova/index'
import { NavItem, NavDetail } from './type'
import { TabbarComponentValue } from '@/views/decoration/tabbar/type'
/**
 * 获取导航列表
 * @param params
 * @returns
 */
export const getNavList = (params: any) => {
  return Alova.Get<IPageResult<NavItem>>('/mall/diy-bottom', { params })
}

/**
 * 获取导航详情
 * @param id
 * @returns
 */
export const getNavDetail = (id: string) => {
  return Alova.Get<NavDetail>(`/mall/diy-bottom/${id}`)
}

/**
 * 更新导航
 * @param param
 * @returns
 */
export const updateNav = (id: string, param: TabbarComponentValue) => {
  return Alova.Put(`/mall/diy-bottom/${id}`, {
    value: param,
  })
}
