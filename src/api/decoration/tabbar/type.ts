import { TabbarComponentValue } from '@/views/decoration/tabbar/type'
/**
 * 导航项
 */
export interface NavItem {
  /**
   * 导航ID
   */
  id: number
  /**
   * 导航名称
   */
  name: string
  /**
   * 导航类型
   */
  type: number

  [property: string]: any
}

/**
 * 导航详情
 */
export interface NavDetail {
  id: number
  /**
   * 导航标识
   */
  key: string
  /**
   * 商城ID
   */
  mall_id: number
  /**
   * 导航名称
   */
  name: string
  /**
   * 模板ID
   */
  template_id: number
  /**
   * 模板名称
   */
  template_name: string
  /**
   * 导航配置
   */
  value: TabbarComponentValue
  [property: string]: any
}
