// import { demoData } from '@/views/decoration/demo'
import { Alova } from '@/utils/http/alova/index'
import {
  type PageTemplate,
  type PageTemplateDetail,
  type DecorationPageParams,
} from '@/components/Decoration'
import { PageLinkEnum } from '@/enums/common'

/**
 * 获取装修页面列表
 * @param key 页面链接标识，用于数据筛选
 * @returns
 */
export const getDecorationPageList = (
  params: {
    key?: PageLinkEnum | ''
    keys?: PageLinkEnum[]
  } & IPageRequest,
) => {
  return Alova.Get<IPageResult<PageTemplate>>('/mall/diy-page', {
    params,
  })
}

/**
 * 添加装修页面
 * @param params 页面数据
 * @returns
 */
export const addDecorationPage = (params: DecorationPageParams) => {
  return Alova.Post<{ id: number }>('/mall/diy-page', params)
}

/**
 * 编辑装修页面
 * @param id 页面ID
 * @param params 页面数据
 * @returns
 */
export const editDecorationPage = (id: number, params: DecorationPageParams) => {
  return Alova.Put<{ id: number }>(`/mall/diy-page/${id}`, params)
}

/**
 * 删除装修页面
 * @param id 页面ID
 * @returns
 */
export const deleteDecorationPage = (id: string) => {
  return Alova.Delete(`/mall/diy-page/${id}`)
}

/**
 * 初始化装修页面
 * @param params 页面数据
 * @returns
 */
export const initPage = (params: { id: number; key: string; init: number }) => {
  return Alova.Get<PageTemplateDetail>('/mall/diy-page/info', { params })
}

/**
 * 复制装修页面
 * @param id 页面ID
 * @returns
 */
export const copyDecorationPage = (id: number) => {
  return Alova.Get(`/mall/diy-page/copy`, { params: { id } })
}

/**
 * 设置默认页面
 * @param id 页面ID
 * @returns
 */
export const setDefaultDecorationPage = (id: number) => {
  return Alova.Get(`/mall/diy-page/default`, { params: { id } })
}
