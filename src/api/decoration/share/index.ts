import { Alova } from '@/utils/http/alova/index'
import { PosterInfo } from './type'
/**
 * 获取海报信息
 * @param params
 * @returns
 */
export const getPosterInfo = (type: string) => {
  return Alova.Get<PosterInfo>('/mall/poster/info', {
    params: {
      type,
    },
  })
}

/**
 * 保存海报信息
 * @param params
 * @returns
 */
export const savePosterInfo = (params: PosterInfo) => {
  return Alova.Post('/mall/poster', params)
}
