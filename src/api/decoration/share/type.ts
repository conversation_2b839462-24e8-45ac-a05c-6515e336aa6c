/**
 * 海报信息
 */
export interface PosterInfo {
  setting: MemberSetting | GoodsSetting
  /**
   * 类型：store_goods：商品分享；user：会员分享
   */
  type: string
  [property: string]: any
}

/**
 * 会员海报配置
 */
export interface MemberSetting {
  /**
   * 海报背景
   */
  background: string
  /**
   * H5分享标题
   */
  h5_share_title: string
  /**
   * 绑定店铺信息：1:展示：0：不展示
   */
  is_store_info: number
  /**
   * 绑定店铺logo:1:展示在二维码中：0：不展示
   */
  is_store_logo: number
  /**
   * 小程序分享标题
   */
  mini_program_share_title: string
  /**
   * 海报模版：1：样式1；2：样式2
   */
  template: number
  /**
   * 编辑文字
   */
  text: string
  /**
   * 文字颜色
   */
  text_color: string
  [property: string]: any
}

/**
 * 商品海报配置
 */
export interface GoodsSetting {
  /**
   * h5
   */
  h5: H5
  /**
   * 小程序
   */
  mini_program: MiniProgram
  /**
   * 海报分享人：1：展示；0：不展示
   */
  is_share_user: 1 | 0
  /**
   * 店铺信息：1：展示；0：不展示
   */
  is_store_info: 1 | 0
  /**
   * 商品海报样式:1:样式1；2：样式2 3：样式3
   */
  template: 1 | 2 | 3
  [property: string]: any
}

/**
 * h5
 */
export interface H5 {
  /**
   * H5商品分享标题：1：仅展示商品名称；2：展示自定义前缀营销文案
   */
  share_title: number
  /**
   * 自定义文案
   */
  share_title_value: string
  [property: string]: any
}

/**
 * 小程序
 */
export interface MiniProgram {
  /**
   * 小程序分享标题：1：仅展示商品名称；2：展示自定义前缀营销文案
   */
  share_title: number
  /**
   * 自定义文案
   */
  share_title_value: string
  [property: string]: any
}
