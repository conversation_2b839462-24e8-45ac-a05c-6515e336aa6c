import { Alova } from '@/utils/http/alova/index'

// 图片上传
export function upload(data) {
  return Alova.Post('/upload', data)
}

// 状态列表
export function stateList(params?) {
  return Alova.Get('/state_list', {
    params,
  })
}

export interface SiteInfo {
  /**
   * 登录页背景
   */
  login_bg?: string
  /**
   * 登录页LOGO
   */
  login_logo?: string
  /**
   * 站点LOGO
   */
  logo?: string
  /**
   * 站点名称
   */
  name?: string
  [property: string]: any
}

/**
 * 获取配置信息
 * @param scene platform：平台，mall：商城，store：商家
 * @returns
 */
export function getSite(scene: string) {
  return Alova.Get<SiteInfo>('/admin/auth/site', {
    params: {
      scene,
    },
    meta: {
      isVisitor: true,
    },
  })
}
