import { Alova } from '@/utils/http/alova/index'
import { BaseLinkItem } from '@/components/SelectLink'

/**
 * 获取商城 - 基础页面链接
 * @param params 搜索参数，包含keyword
 */
export const getShopBaseLink = (params?: { keyword?: string }) => {
  return Alova.Get<BaseLinkItem[]>('/common/link/get-shop-base-link', { params })
}

/**
 * 获取商城 - 应用插件页面链接
 */
export const getShopAddonsLink = (params?: { keyword?: string }) => {
  return Alova.Get<BaseLinkItem[]>('/common/link/get-addons-link', { params })
}
