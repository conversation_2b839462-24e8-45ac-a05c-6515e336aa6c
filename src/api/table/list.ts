import { Alova } from '@/utils/http/alova/index'

export interface TableItem {
  id: number
  name: string
  avatar: string
  sex: string
  status: string
  createDate: string
  email: string
  city: string
}

export const sexMap = {
  male: '男',
  female: '女',
  unknown: '未知',
}

export const statusMap = {
  close: '已取消',
  refuse: '已拒绝',
  pass: '已通过',
}

// 获取table
export function getTableList(params: any) {
  return Alova.Get('/table/list', { params })
}

//获取table select
export function getTableSelectList(params: any) {
  return Alova.Get('/table/select', { params })
}

// 空的编辑 (此为空接口，只用于示例)
export function editKong(id: number, data: any) {
  return Promise.resolve({ code: 0, msg: '编辑成功', data: { id, ...data } })
}

// 空的添加 (此为空接口，只用于示例)
export function createKong(data: any) {
  return Promise.resolve({ code: 0, msg: '添加成功', data })
}
