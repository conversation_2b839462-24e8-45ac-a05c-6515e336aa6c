import { Alova } from '@/utils/http/alova/index'

// 上传类型
export type UploadType = 'files' | 'images' | 'videos' | 'voices' | 'pdf' // 可以根据需要添加更多类型

// 文件类型
export type FileType = '1' | '2' // 1:图片；2：图标

// 文件列表项类型
export interface FileItem {
  /**
   * 创建时间
   */
  created_at: number
  /**
   * 存储驱动
   */
  drive: string
  /**
   * id
   */
  id: number
  /**
   * 名称
   */
  name: string
  /**
   * 原始文件名
   */
  old_name: string
  /**
   * 文件大小
   */
  size: number
  /**
   * 缩略图地址
   */
  thumb_url: string
  /**
   * 上传类型
   */
  upload_type: string
  /**
   * 文件地址
   */
  url: string
  /**
   * 其他可能的属性
   */
  [property: string]: any
}

// 上传响应类型
export interface UploadFileResponse {
  domain_name: string
  name: string
  old_name: string
  width: number
  height: number
  size: number
  extension: string
  url: string
  merge: boolean
  guid: string
  type: string
  thumb_url: string[]
  duration: string
  path_dir: string
  id: number
  formatter_size: string
  upload_type: string
}

/**
 * 文件上传
 */
export function uploadFile(formData: FormData) {
  return Alova.Post<UploadFileResponse>('/common/file', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 获取文件列表
 */
export function getFileList(params: {
  keyword?: string
  upload_type?: string
  type?: string
  page?: number
  limit?: number
  group_id?: string
}) {
  return Alova.Get<IPageResult<FileItem>>('/common/file', {
    params,
  })
}

/**
 * 删除文件
 */
export function deleteFiles(ids: number[]) {
  return Alova.Delete<null>('/common/file/delete', { ids })
}

/**
 * 移动文件
 */
export function moveFiles(data: { ids: number[]; attachment_group_id: number }) {
  return Alova.Post<null>('/common/file/move', data)
}
