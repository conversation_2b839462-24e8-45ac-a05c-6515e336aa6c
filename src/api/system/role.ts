import { Alova } from '@/utils/http/alova/index'
import { MenuItem } from './menu'
import { getAppType } from '@/utils/env'

export interface RoleInfo {
  id: number
  name: string
  status: number
  remark: string
  menu: MenuItem[]
}

/**
 * @description: 角色列表
 */
export function getRoleList(params: IPageRequest) {
  return Alova.Get<IPageResult<RoleInfo>>(`/${getAppType()}/role`, { params })
}

/**
 * @description: 创建角色
 */
export function createRole(params) {
  return Alova.Post(`/${getAppType()}/role`, params)
}

/**
 * @description: 更新角色
 */
export function updateRole(id: number, params: any) {
  return Alova.Put(`/${getAppType()}/role/${id}`, params)
}

/**
 * @description: 删除角色
 */
export function deleteRole(id: number) {
  return Alova.Delete(`/${getAppType()}/role/${id}`)
}

/**
 * @description: 获取角色信息
 */
export function getRoleInfo(id: number) {
  return Alova.Get<RoleInfo>(`/${getAppType()}/role/${id}`)
}

/**
 * @description: 获取菜单列表（用于设置角色权限）
 */
export function getRoleMenus() {
  return Alova.Get<MenuItem[]>(`/${getAppType()}/role/menus`)
}

/**
 * @description: 设置角色权限
 */
export function setRolePermission(id: number, menu_ids: number[]) {
  return Alova.Post(`/${getAppType()}/role/set-menus?id=${id}`, { menu_ids })
}
