import { Alova } from '@/utils/http/alova/index'
import { getAppType } from '@/utils/env'

export interface BasicPageParams {
  pageNumber: number
  pageSize: number
  total: number
}
export interface UserInfo {
  id: string
  avatar: string
  creator_id: number
  deleted_at: string
  created_at: string
  department_id: number
  email: string
  login_at: string
  login_ip: string
  status: number
  updated_at: string
  permissions: any[]
}

interface LoginParams {
  username: string
  password: string
  type?: string
  remember?: boolean
}

/**
 * @description: 获取用户信息
 */
export function getUserInfo() {
  try {
    return Alova.Get<UserInfo>(`/user/online`)
  } catch (error) {
    throw error
  }
}

/**
 * @description: 用户登录
 */
export const login = (data: LoginParams) => {
  // 代理端使用商城端登录接口, 其他端使用自己的登录接口
  // const platform = getAppType() === PlatformEnum.AGENT ? PlatformEnum.MALL : getAppType()
  const url = `/login`
  return Alova.Post<IResponseModel<{ user: string; token: string; expired: number }>>(
    url,
    {
      email: data.username,
      password: data.password,
      remember: false,
    },
    {
      meta: {
        authRole: 'login',
        isTransformResponse: false,
      },
    },
  )
}

/**
 * @description: 用户修改密码
 */
export function changePassword(data, uid) {
  const platform = getAppType()
  return Alova.Post(`/${platform}/user/u${uid}/changepw`, data)
}

/**
 * @description: 用户登出
 */
export function userLogout(data?) {
  const platform = getAppType()
  return Alova.Post(`/${platform}/logout`, data, {
    meta: {
      authRole: 'logout',
    },
  })
}

/**
 * @description: 获取用户列表
 */
export function getUserList(params) {
  return Alova.Get('/admin/user_list', { params })
}

/**
 * @description: 刷新 token
 */
export const refreshToken = () => {
  // 每个端使用自己的刷新token接口
  const url = `/reload-token`
  return Alova.Get<IResponseModel<{ user: string; token: string; expired: number }>>(url, {
    meta: {
      authRole: 'refreshToken',
      isTransformResponse: false,
    },
  })
}
