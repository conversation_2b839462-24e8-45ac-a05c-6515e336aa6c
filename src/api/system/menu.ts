import { Alova } from '@/utils/http/alova/index'
import { MenuTypeEnum } from '@/enums/common'
import { TargetContext } from '/#/index'
import { PlatformEnum } from '@/enums/appEnums'

export interface MenuItem {
  /**
   * 主键
   */
  id: number
  /**
   * 父ID
   */
  parent_id: number
  /**
   * 组件
   */
  component: string
  /**
   * 是否隐藏
   */
  hidden: boolean
  /**
   * ICON
   */
  icon?: string
  /**
   * 菜单类型
   */
  menu_type: MenuTypeEnum
  /**
   * 名称
   */
  name: string
  /**
   * 路径
   */
  path: string
  /**
   * 所属平台
   */
  platform: IPlatformEnum
  /**
   * 重定向
   */
  redirect: string
  /**
   * 排序
   */
  sort: string
  /**
   * 打开方式
   */
  target: TargetContext
  /**
   * 标题
   */
  title: string
  [property: string]: any
}

/**
 * @description 获取当前平台的菜单数据
 * @returns {Promise} 返回菜单数据的Promise对象
 * @example
 * // 获取当前平台菜单
 * const menus = await getMenus()
 */
export function getMenus() {
  // 返回请求菜单的接口
  return Alova.Get(`/user/menu`)
}

/**
 * @description: 获取管理端菜单
 */
export function adminMenus() {
  return Alova.Get('/admin/auth/menu')
}

/**
 * @description: 获取商城端菜单
 */
export function mallMenus() {
  return Alova.Get('/mall/menu/list')
}

/**
 * @description: 获取商户端菜单
 */
export function storeMenus() {
  return Alova.Get('/store/menu/list')
}

/**
 * 获取菜单列表
 */
export interface MenuListResult {
  [PlatformEnum.ADMIN]: MenuItem[]
  [PlatformEnum.MALL]: MenuItem[]
}

export function getMenuList() {
  return Alova.Get<MenuListResult>('/admin/menu/list')
}

/**
 * 创建菜单
 */
export function createMenu(params: MenuItem) {
  return Alova.Post('/admin/menu', params)
}

/**
 * 更新菜单
 */
export function updateMenu(id: number, params: MenuItem) {
  return Alova.Put(`/admin/menu/${id}`, params)
}

/**
 * 删除菜单
 */
export function deleteMenu(id: number) {
  return Alova.Delete(`/admin/menu/${id}`)
}
