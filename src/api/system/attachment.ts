import { Alova } from '@/utils/http/alova/index'
interface AttachmentItem {
  created_at: number
  drive: string
  /**
   * id
   */
  id: number
  /**
   * 名称
   */
  name: string
  old_name: string
  size: number
  thumb_url: string
  upload_type: string
  /**
   * 地址
   */
  url: string
  [property: string]: any
}
/**
 * @description: 分组列表
 */
export function getAttachmentGroupList(params?) {
  return Alova.Get('/common/attachment-group', {
    params,
  })
}

/**
 * @description: 新增分组
 * @param {Object} params
 * @returns
 */
export function addAttachmentGroup(params: { name: string; type: number }) {
  return Alova.Post('/common/attachment-group', params)
}

/**
 * @description: 编辑分组
 * @param {Object} params
 * @returns
 */
export function editAttachmentGroup(params: { id: number; name: string }) {
  return Alova.Put(`/common/attachment-group/${params.id}`, {
    name: params.name,
  })
}

/**
 * @description: 删除分组
 * @param {Object} id
 * @returns
 */
export function deleteAttachmentGroup(id: number) {
  return Alova.Delete(`/common/attachment-group/${id}`)
}

/**
 * @description: 上传文件
 * @param {Object} params
 * @returns
 */
export function uploadFile(params) {
  return Alova.Post('/common/file', {
    params,
  })
}

/**
 * @description: 文件列表
 * @param {Object} params
 * @returns
 */
export function getAttachmentList(params) {
  return Alova.Get<IPageResult<AttachmentItem>>('/common/file', {
    params,
  })
}

/**
 * @description: 删除文件
 * @param {Object} ids
 * @returns
 */
export function deleteFile(ids: number[]) {
  return Alova.Delete(`/common/file/delete`, {
    ids,
  })
}

/**
 * @description: 移动文件
 * @param {Object} params
 * @returns
 */
export function moveFile(params) {
  return Alova.Put('/common/file/move', params)
}
