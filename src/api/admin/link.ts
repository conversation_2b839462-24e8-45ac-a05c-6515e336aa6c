import { Alova } from '@/utils/http/alova/index'

/**
 * 链接分类参数接口
 * @description 用于创建/更新链接分类的参数
 */
export interface LinkCategoryParams {
  /** 分类ID,更新时必填 */
  id?: number
  /** 唯一标识 */
  key: string
  /** 分类名称 */
  title: string
}

/**
 * 链接参数接口
 * @description 用于创建/更新链接的参数
 */
export interface LinkParams {
  /** 链接ID,更新时必填 */
  id?: number
  /** 分类ID,类型为商城系统时必填 */
  cate_id?: number
  /** 链接唯一标识 */
  key: string
  /** 链接名称 */
  title: string
  /** 链接状态: 0-禁用, 1-启用 */
  status: number
  /** 跳转链接地址 */
  url: string
  /** 链接类型: 1-商城系统, 2-应用插件 */
  type: number
  /** 应用名称(英文标识),类型为应用插件时必填 */
  addons_name?: string
}

/**
 * 链接分类列表项接口
 * @description 获取链接分类列表的返回项类型
 */
export interface LinkCategoryItem {
  /** 分类ID */
  id: number
  /** 唯一标识 */
  key: string
  /** 分类名称 */
  title: string
  /** 父级分类ID */
  parent_id: number
  /** 创建时间戳 */
  created_at: number
  /** 更新时间戳 */
  updated_at: number
  /** 删除时间戳,未删除为null */
  deleted_at: null | number
}

/**
 * 链接列表项接口
 * @description 获取链接列表的返回项类型
 */
export interface LinkItem {
  /** 链接ID */
  id: number
  /** 分类ID */
  cate_id: number
  /** 分类名称 */
  cate_title: string
  /** 链接名称 */
  title: string
  /** 链接唯一标识 */
  key: string
  /** 跳转链接地址 */
  url: string
  /** 应用名称(英文标识) */
  addons_name: string
  /** 链接类型: 1-商城系统, 2-应用插件 */
  type: number
  /** 是否可分享: 0-不允许, 1-允许 */
  is_share: number
  /** 链接状态: 0-禁用, 1-启用 */
  status: number
  /** 创建时间戳 */
  created_at: number
  /** 更新时间戳 */
  updated_at: number
  /** 删除时间戳,未删除为null */
  deleted_at: null | number
}

/**
 * 获取链接分类列表
 * @param params 查询参数
 * @returns 分页结果
 */
export function getLinkCategoryList(params?: any) {
  return Alova.Get<IPageResult<LinkCategoryItem>>('/admin/link-category', { params })
}

/**
 * 添加链接分类
 * @param params 链接分类参数
 * @returns 添加结果
 */
export function addLinkCategory(params: LinkCategoryParams) {
  return Alova.Post('/admin/link-category', params)
}

/**
 * 编辑链接分类
 * @param id 链接分类ID
 * @param params 链接分类参数
 * @returns 编辑结果
 */
export function editLinkCategory(id: number, params: LinkCategoryParams) {
  return Alova.Put(`/admin/link-category/${id}`, params)
}

/**
 * 删除链接分类
 * @param id 链接分类ID
 * @returns 删除结果
 */
export function deleteLinkCategory(id: number) {
  return Alova.Delete(`/admin/link-category/${id}`)
}

/**
 * 获取链接分类详情
 * @param id 链接分类ID
 * @returns 分类详情
 */
export function getLinkCategoryDetail(id: number) {
  return Alova.Get<LinkCategoryItem>(`/admin/link-category/${id}`)
}

/**
 * 获取链接列表
 * @param params 查询参数
 * @returns 分页结果
 */
export function getLinkList(params?: any) {
  return Alova.Get<IPageResult<LinkItem>>('/admin/link', { params })
}

/**
 * 添加链接
 * @param params 链接参数
 * @returns 添加结果
 */
export function addLink(params: LinkParams) {
  return Alova.Post('/admin/link', params)
}

/**
 * 编辑链接
 * @param params 链接参数(需包含ID)
 * @returns 编辑结果
 */
export function editLink(params: LinkParams) {
  return Alova.Put(`/admin/link/${params.id}`, params)
}

/**
 * 删除链接
 * @param id 链接ID
 * @returns 删除结果
 */
export function deleteLink(id: number) {
  return Alova.Delete(`/admin/link/${id}`)
}

/**
 * 获取链接详情
 * @param id 链接ID
 * @returns 链接详情
 */
export function getLinkDetail(id: number) {
  return Alova.Get<LinkItem>(`/admin/link/${id}`)
}
