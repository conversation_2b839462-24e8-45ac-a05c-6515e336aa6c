export interface StorageSetting {
  is_sync_mall?: boolean
  driver: string
  oss_access_key_id: string
  oss_access_key_secret: string
  oss_bucket: string
  oss_endpoint: string
}

export interface ExpireNoticeSetting {
  certificate: Certificate
  server: Server
  [property: string]: any
}

export interface Certificate {
  /**
   * 证书到期时间
   */
  admin_expire_date: string
  api_expire_date: string
  h5_expire_date: string
  /**
   * 是否开启提醒
   */
  open_notice: number
  ssl_path: string
  static_expire_date: string
  [property: string]: any
}

export interface Server {
  expire_date: string
  open_notice: number
  [property: string]: any
}

export interface MapSetting {
  qq_jsapi_key: string
  qq_jsapi_secret: string
}

export interface LoginSetting {
  platform: {
    logo: string
    name: string
    login_logo: string
    login_bg: string
  }
  mall: {
    login_desc: string
    login_bg: string
    credentials: number
    open_mobile_login_verify: number
    open_register: number
    registered_bg: string
  }
}

export interface VersionSetting {
  platform: {
    public_record: string
    record_belong: string
    record_number: string
  }
  mall: {
    copyright_url: string
    login_bg: string
    version_logo: string | string[]
    copyright_status: number
    copyright: string
  }
}
