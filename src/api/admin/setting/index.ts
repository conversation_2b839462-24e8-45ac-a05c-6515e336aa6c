import { Alova } from '@/utils/http/alova/index'
import {
  StorageSetting,
  ExpireNoticeSetting,
  MapSetting,
  LoginSetting,
  VersionSetting,
} from './type'

/**
 * 获取存储设置
 * @returns
 */
export function getStorageSetting() {
  return Alova.Get<StorageSetting>('/admin/setting/get-storage')
}

/**
 * 设置存储设置
 * @param data
 * @returns
 */
export function setStorageSetting(params: any) {
  return Alova.Post('/admin/setting/save-storage', params)
}

/**
 * 获取传输限制设置
 * @returns
 */
export function getTransferLimitSetting() {
  return Alova.Get<{ img: number; video: number; audio: number }>('/admin/setting/get-limit')
}

/**
 * 设置传输限制设置
 * @param data
 * @returns
 */
export function setTransferLimitSetting(params: any) {
  return Alova.Post('/admin/setting/save-limit', params)
}

/**
 * 获取提醒管理设置
 * @returns
 */
export function getExpireNoticeSetting() {
  return Alova.Get<ExpireNoticeSetting>('/admin/setting/get-expire-notice')
}

/**
 * 设置提醒管理设置
 * @param data
 * @returns
 */
export function setExpireNoticeSetting(params: any) {
  return Alova.Post('/admin/setting/save-expire-notice', params)
}

/**
 * 获取地图设置
 * @returns
 */
export function getMapSetting() {
  return Alova.Get<MapSetting>('/admin/setting/get-map')
}

/**
 * 设置地图设置
 * @param data
 * @returns
 */
export function setMapSetting(params: any) {
  return Alova.Post('/admin/setting/save-map', params)
}

/**
 * 获取登录设置
 * @returns
 */
export function getLoginSetting() {
  return Alova.Get<LoginSetting>('/admin/setting/get-login')
}

/**
 * 设置登录设置
 * @param data
 * @returns
 */
export function setLoginSetting(params: any) {
  return Alova.Post('/admin/setting/save-login', params)
}

/**
 * 获取版权设置
 * @returns
 */
export function getCopyrightSetting() {
  return Alova.Get<VersionSetting>('/admin/setting/get-copyright')
}

/**
 * 设置版权设置
 * @param data
 * @returns
 */
export function setCopyrightSetting(params: any) {
  return Alova.Post('/admin/setting/save-copyright', params)
}
