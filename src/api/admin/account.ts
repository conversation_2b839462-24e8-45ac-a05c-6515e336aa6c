import { Alova } from '@/utils/http/alova/index'

/**
 * 新增/编辑管理员请求参数类型
 * @remarks 用于管理员创建或编辑操作时传递参数
 */
export interface RequestParams {
  /**
   * 可创建商城数量
   */
  mall_num: string

  /**
   * 手机号
   */
  mobile: string

  /**
   * 用户昵称
   */
  nickname: string

  /**
   * 密码
   * @remarks
   * - 创建时必填，编辑时为空则不修改
   */
  password: string

  /**
   * 备注
   */
  remark: string

  /**
   * 角色ID
   */
  role_ids: number[]

  /**
   * 登录账号
   */
  username: string

  /**
   * 扩展属性 - 用于兼容未来可能的字段扩展
   */
  [property: string]: any
}

/**
 * 管理员账户详细信息类型
 * @remarks 用于展示管理员账户的完整信息
 */
export interface AccountItem {
  /**
   * 唯一标识符 - 自动生成
   * @example 123456
   */
  id: number

  /**
   * 登录用户名 - 系统唯一标识
   * @example "admin_user"
   */
  username: string

  /**
   * 显示昵称 - 用于前端展示
   * @example "张经理"
   */
  nickname: string

  /**
   * 管理员类型（同RequestParams定义）
   * @see RequestParams.admin_type
   */
  admin_type: number

  /**
   * 分配的角色ID列表 - 对应权限集合
   * @example [1, 3]
   */
  roles: number[]

  /**
   * 备注信息 - 最大长度255字符
   * @example "2023年度优秀管理员"
   */
  remark: string

  /**
   * 账户创建时间 - ISO 8601格式
   * @example "2023-08-20T15:30:00Z"
   */
  created_at: string
}

/**
 * 获取管理员列表
 * @param params
 * @returns
 */
export function getAccountList(params: IPageRequest) {
  return Alova.Get<IPageResult<AccountItem>>('/admin/account', { params })
}

/**
 * 新增管理员
 * @param params
 * @returns
 */
export function addAccount(params: RequestParams) {
  return Alova.Post('/admin/account', params)
}

/**
 * 编辑管理员
 * @param params
 * @returns
 */
export function editAccount(params: RequestParams) {
  return Alova.Put(`/admin/account/${params.id}`, params)
}

/**
 * 删除管理员
 * @param id
 * @returns
 */
export function deleteAccount(id: number) {
  return Alova.Delete(`/admin/account/${id}`)
}

/**
 * 获取管理员详情
 * @param id
 * @returns
 */
export function getAccountDetail(id: number) {
  return Alova.Get<AccountItem>(`/admin/account/${id}`)
}
