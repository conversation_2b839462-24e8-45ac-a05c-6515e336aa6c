import { Alova } from '@/utils/http/alova/index'
import {
  QuerySeaLeadsType,
  RequestTransferLeadsToUserType,
} from '@/views/admin/leads/config/leadTypes'
/**
 * @description: 我的线索列表
 */
export function getMyLeadList(params: QuerySeaLeadsType) {
  return Alova.Get(`/leads/myLeads`, { params })
}

/**
 * @description: 转移线索
 */
export function transferMyLeads(data: RequestTransferLeadsToUserType) {
  return Alova.Post(`/leads/myLeads/transferLeadsToUser`, data)
}
