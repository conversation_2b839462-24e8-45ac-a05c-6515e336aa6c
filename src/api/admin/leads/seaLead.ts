import { <PERSON>ova, AlovaIndependent } from '@/utils/http/alova/index'
import {
  FollowStatusType,
  SopStatusType,
  DepartmentType,
  IndustryType,
  SourceType,
  RegionType,
  EntryMethodType,
  CallStatusType,
  LeadLabelType,
  CheckPhoneType,
  RequestCreateSeaLeadsType,
  RequestCheckPhoneType,
  RequestReceiveLeadsType,
  QuerySeaLeadsType,
  RequestTransferLeadsToDepartmentType,
  AssignPeopleType,
  RequestAssignLeadsType,
  RequestBatchDelLeadsType,
  ExportLeadsType,
  RequestGenerateExportTaskType,
  LeadDetailType,
  LabelsTagsType,
  RequestAddRecordUpRecordsType,
  RequestGetRecordUpRecordsType,
  FollowRecord,
} from '@/views/admin/leads/config/leadTypes'
import {
  FROM_TYPE_PUBLIC_LEAD,
  FROM_TYPE_MY_LEAD,
  FROM_TYPE_DEPARTMENT_LEAD,
  FROM_TYPE_RECYCLE_LEAD,
} from '@/enums/fromTypeEnum'
/**
 * @description: 公海线索列表
 */
export function getSeaLeadList(params: QuerySeaLeadsType) {
  return Alova.Get(`/leads/seaLeads`, { params })
}

/**
 * @description: 选择公海池/部门
 */
export function getDepartments() {
  return Alova.Get<DepartmentType[]>(`/leads/getDepartments`)
}

/**
 * @description: 获取渠道来源
 */
export function getSource() {
  return Alova.Get<SourceType[]>(`/source`)
}

/**
 * @description: 获取所属行业
 */
export function getIndustry() {
  return Alova.Get<IndustryType[]>(`/industry`)
}

/**
 * @description: 获取省/市/区
 */
export function getRegion() {
  return AlovaIndependent.Get<RegionType[]>(`/region`)
}

/**
 * @description: 录入方式
 */
export function getEntryMethods() {
  return Alova.Get<EntryMethodType[]>(`/leads/getEntryMethods`)
}

/**
 * @description: 通话状态
 */
export function getCallStatus() {
  return Alova.Get<CallStatusType[]>(`/leads/getCallStatus`)
}

/**
 * @description: 线索标签
 */
export function getLeadsLabels() {
  return Alova.Get<LeadLabelType[]>(`/leads/getLeadsLabels`)
}

/**
 * @description: 跟进状态
 */
export function getPubFollowStatus() {
  return Alova.Get<FollowStatusType[]>(`/leads/getPubFollowStatus`)
}

/**
 * @description: SOP阶段
 */
export function getSopStage() {
  return Alova.Get<SopStatusType[]>(`/leads/getSopStage`)
}

/**
 * @description: 新增公海线索
 */
export function createSeaLeads(data: RequestCreateSeaLeadsType, from: number) {
  let api = ''
  // 公海线索
  if (from == FROM_TYPE_PUBLIC_LEAD) {
    api = '/leads/seaLeads'
  }
  // 部门线索接口未出
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = ``
  }
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads`
  }

  return Alova.Post(api, data)
}

/**
 * @description: 检查手机号是否存在
 */
export function checkPhone(data: RequestCheckPhoneType) {
  return Alova.Post<CheckPhoneType>(`/leads/check_phone`, data)
}

/**
 * @description: 领取线索
 */
export function receiveLeads(data: RequestReceiveLeadsType) {
  return Alova.Post(`/leads/seaLeads/receiveLeads`, data)
}

/**
 * @description: 转移线索
 */
export function transferLeadsToDepartment(data: RequestTransferLeadsToDepartmentType) {
  return Alova.Post(`/leads/seaLeads/transferLeadsToDepartment`, data)
}

/**
 * @description: 获取分配人员
 */
export function getAssignPeople() {
  return Alova.Post<AssignPeopleType[]>(`/leads/seaLeads/getAssignPeople`)
}

/**
 * @description: 分配线索
 */
export function assignLeads(data: RequestAssignLeadsType) {
  return Alova.Post(`/leads/seaLeads/assignLeads`, data)
}

/**
 * @description: 批量删除线索
 */
export function batchDelLeads(data: RequestBatchDelLeadsType, formType: number) {
  let api = ''
  if (formType === FROM_TYPE_PUBLIC_LEAD) {
    api = `/leads/seaLeads/batchDelLeads`
  }
  if (formType === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/batchDelLeads`
  }
  if (formType === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/batchDelLeads`
  }
  return Alova.Post(api, data)
}

/**
 * @description: 获取导出字段
 */
export function getExportFields() {
  return Alova.Get<ExportLeadsType>(`/download-task/getTaskFields`, {
    params: { key: 'leads' },
  })
}

/**
 * @description: 生成导出任务
 */
export function generateExportTask(data: RequestGenerateExportTaskType, from: number) {
  let api = ''
  if (from === FROM_TYPE_PUBLIC_LEAD) {
    api = `/leads/seaLeads/exportLeadsTask`
  }
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/recycleLeads/exportLeadsTask`
  }
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/exportLeadsTask`
  }
  return Alova.Post(api, data)
}

/**
 * @description: 获取公海线索详情
 */
export function getLeadDetail(id: string) {
  return Alova.Get<LeadDetailType>(`/leads/seaLeads/${id}`)
}

/**
 * @description: 获取线索标签
 */
export function getFormatLabels(id: string) {
  return Alova.Get<LabelsTagsType[]>(`/label/getFormatLabels`, {
    params: { type: 1, link_id: id },
  })
}

/**
 * 添加标签
 * @param type 1：线索，2：客户，3：商机
 * @param link_id 关联ID；type=1时是传线索ID，type=2时是传客户ID，type=3时是传商机ID
 * @param label_ids 标签ID数组
 * @param from 从哪个页面进入的详情页（1：公海，2：部门，3：我的,4：回收站）
 * @returns
 */
export function addLabelRelationship(
  type: number,
  link_id: string,
  label_ids: number[],
  from: number,
) {
  let api = ''
  // 我的线索退回
  if (from == FROM_TYPE_PUBLIC_LEAD) {
    api = '/leads/seaLeads/editLabel'
  }
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/editLabel`
  }
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/editLabel`
  }

  return Alova.Post(api, {
    type: type,
    link_id: link_id,
    label_ids: label_ids,
  })
}

/**
 * @description: 修改跟进状态
 */
export function changeCallStatus(id: string, call_status_id: number) {
  return Alova.Post(`/leads/changeCallStatus`, { id, call_status_id })
}

/**
 * @description: 修改公海线索
 */
export function updateSeaLeads(id: string, data: RequestCreateSeaLeadsType, from: number) {
  let api = ''
  // 编辑公海线索
  if (from == FROM_TYPE_PUBLIC_LEAD) {
    api = '/leads/seaLeads/'
  }
  // 编辑部门线索
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/`
  }
  // 编辑我的线索
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/`
  }
  return Alova.Put(`${api}${id}`, { id, ...data })
}

/**
 * 设为无效
 * @param id
 * @param from 从哪个页面进入的详情页（1：公海，2：部门，3：我的,4：回收站）
 * @returns
 */
export function setValid(id: string, from: number) {
  let api = ''
  if (from == FROM_TYPE_PUBLIC_LEAD) {
    api = '/leads/seaLeads/setValid'
  }
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/setValid`
  }
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/setValid`
  }

  return Alova.Post(api, { id, from })
}

/**
 * 标识Key，客户导入：customer，线索导入：lead，商机导入：opportunity
 * @description: 获取导入模板
 */
export function getImportTemplate(key: string) {
  return Alova.Get(`/getImportTemplate`, { params: { key } })
}

/**
 * @description: 退回公海
 */
export function returnLeads(data) {
  const { ids, reason, from } = data
  let api = ''
  // 我的线索退回
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/returnLeads`
  }
  // 部门线索
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/returnLeads`
  }
  if (from === FROM_TYPE_RECYCLE_LEAD) {
    api = `/leads/recycleLeads/returnLeads`
  }
  return Alova.Post(api, { ids, reason })
}

/**
 * 添加操作记录
 * @param data
 * @returns
 */
export function addRecordUpRecords(data: RequestAddRecordUpRecordsType, from: number) {
  let api = ''
  // 公海线索
  if (from === FROM_TYPE_PUBLIC_LEAD) {
    api = `/leads/addRecordUpRecords`
  }
  // 我的线索
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/myLeads/addRecordUpRecords`
  }
  // 部门线索
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/departmentLeads/addRecordUpRecords`
  }
  return Alova.Post(api, data)
}

/**
 * 获取操作记录
 * @param data
 * @returns
 */
export function getRecordUpRecords(data: RequestGetRecordUpRecordsType, from: number) {
  let api = ''
  // 公海线索
  if (from === FROM_TYPE_PUBLIC_LEAD) {
    api = `/leads/getRecordUpRecords`
  }
  // 我的线索
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/myLeads/getRecordUpRecords`
  }
  // 部门线索
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/departmentLeads/getRecordUpRecords`
  }
  return Alova.Get<IPageResult<FollowRecord>>(api, { params: { ...data, type: 1 } })
}

/**
 * 删除跟进记录
 * @param id
 * @returns
 */
export function delFollowUpRecord(id: string, from: number) {
  let api = ''
  // 公海线索
  if (from === FROM_TYPE_PUBLIC_LEAD) {
    api = `/leads/delFollowUpRecord`
  }
  // 我的线索
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/myLeads/delFollowUpRecord`
  }
  // 部门线索
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/departmentLeads/delFollowUpRecord`
  }
  return Alova.Delete(`${api}/${id}`)
}

/**
 * 转客户
 * @param id
 * @returns
 */
export function becomeCustomer(id: string, from: number) {
  let api = ''
  if (from === FROM_TYPE_MY_LEAD) {
    api = `/leads/myLeads/becomeCustomer`
  }
  if (from === FROM_TYPE_DEPARTMENT_LEAD) {
    api = `/leads/departmentLeads/becomeCustomer`
  }
  return Alova.Post(api, { id })
}
