export interface AddonPackageItem {
  id: number
  title: string
  description: string
  is_default: number
  mall_num: number
  created_at: number
}
export interface addonParams {
  title?: string
  create_data?: [string, string]
}
export interface MealParams {
  title: string
  description: string
  is_forever: number
  duration: number | null
  periodUnit?: number
  unit: number
  addons: string[]
}
export interface addonsDetails {
  /**
   * 应用分组列表
   */
  addons_list: AddonsList
  /**
   * 套餐详情
   */
  detail: Detail
  [property: string]: any
}
/**
 * 应用分组列表
 */
export interface AddonsList {
  marketing: Marketing
  service: Service
  [property: string]: any
}

export interface Marketing {
  list: MarketingList[]
  title: string
  [property: string]: any
}
export interface MarketingList {
  name?: string
  title?: string
  [property: string]: any
}
export interface Service {
  /**
   * 应用列表
   */
  list: ServiceList[]
  /**
   * 分组名称
   */
  title: string
  [property: string]: any
}

export interface ServiceList {
  /**
   * 应用标识
   */
  name: string
  /**
   * 应用名称
   */
  title: string
  [property: string]: any
}

/**
 * 套餐详情
 */
export interface Detail {
  /**
   * 套餐包含的插件标识
   */
  addons: string[]
  created_at: number
  deleted_at: null
  /**
   * 套餐说明
   */
  description: string
  /**
   * 过期时间
   */
  expire: Expire
  id: number
  /**
   * 是否是基础套餐：1是；0：否
   */
  is_default: number
  /**
   * 站点数量
   */
  mall_num: number
  status: number
  /**
   * 套餐名称
   */
  title: string
  updated_at: number
  [property: string]: any
}
/**
 * 过期时间
 */
export interface Expire {
  /**
   * 数量
   */
  duration: number
  /**
   * 是否永久，0:否；1是
   */
  is_forever: number
  /**
   * 单位，1：月；2：年
   */
  unit: number
  [property: string]: any
}
