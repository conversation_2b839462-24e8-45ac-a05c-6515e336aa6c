import { Alova } from '@/utils/http/alova/index'
import { AddonPackageItem, addonParams, addonsDetails, MealParams } from './type'
/**
 * 应用列表项类型
 * @remarks 用于展示应用列表中的单个应用信息
 */
export interface AddonItem {
  /**
   * 应用ID
   * @example 1
   */
  id: number

  /**
   * 应用名称
   * @example "拼团"
   */
  title: string

  /**
   * 应用标识
   * @example "GroupBuy"
   */
  name: string

  /**
   * 作者
   * @example "qimall"
   */
  author: string

  /**
   * 版本号
   * @example "1.0.0"
   */
  version: string

  /**
   * 应用图
   * @example ""
   */
  cover: string | null

  /**
   * 分组
   * @example "marketing"
   */
  group: string

  /**
   * 简介
   * @example "拼单成团，裂变式转化"
   */
  brief_introduction: string

  /**
   * 是否上架
   * @remarks 1：已上架；0：未上架
   * @example 1
   */
  is_online: number
}
export interface AddonGroupItem {
  /**
   * 分组图标
   * @example "iconfont icon-business"
   */
  icon: string

  /**
   * 分组名称
   * @example "商业模式"
   */
  name: string

  /**
   * 分组标题
   * @example "商业模式"
   */
  title: string
}
export interface AddonGroupList {
  /**
   * 分组列表
   */
  addons_group_arr: AddonGroupItem[]
  /**
   * 应用列表
   */
  list: AddonItem[]
}

/**
 * 本地应用项类型
 */
export interface LocalAddonItem {
  /**
   * 应用标识
   * @example "Boss"
   */
  name: string

  /**
   * 应用标题
   * @example "股东管理"
   */
  title: string

  /**
   * 简介
   * @example "升级做股东，多一份利益"
   */
  brief_introduction: string

  /**
   * 详细描述
   * @example "升级做股东，多一份利益"
   */
  description: string

  /**
   * 作者
   * @example "qimall"
   */
  author: string

  /**
   * 版本
   * @example "1.0.0"
   */
  version: string

  /**
   * 分组
   * @example "商业模式"
   */
  group: string
}

/**
 * 价格设置项类型
 */
export interface PriceSettingItem {
  /**
   * 单位
   * @remarks 0：永久；1：月；2：年
   * @example 1
   */
  unit: number

  /**
   * 价格
   * @example 12
   */
  money: number

  /**
   * 时长
   * @remarks -1代表永久
   * @example 12
   */
  duration: number
}

/**
 * 应用安装请求参数类型
 */
export interface InstallAddonParams {
  /**
   * 应用标识
   * @example "Boss"
   */
  name: string
}
export interface UpdateConfigParams {
  /**
   * 应用标识
   * @example "Boss"
   */
  name: string
}
export interface PaySettingParams {
  /**
   * 应用中文名称
   * @example "Boss"
   */
  title: string
}
/**
 * 批量安装应用请求参数类型
 */
export interface BatchInstallAddonParams {
  /**
   * 应用标识列表
   * @example ["Boss", "Relationship"]
   */
  name: string[]
}

/**
 * 编辑应用请求参数类型
 */
export interface EditAddonParams {
  /**
   * 应用ID
   */
  id?: number

  /**
   * 应用名称
   * @example "插件abc"
   */
  title: string

  /**
   * 分组
   * @example "business"
   */
  group: string

  /**
   * 应用图
   * @example ""
   */
  cover: string

  /**
   * 简介
   * @example ""
   */
  brief_introduction: string

  /**
   * 详细描述
   * @example "1323fdsfsdfsd"
   */
  description: string
}

/**
 * 修改付费设置请求参数类型
 */
export interface ChangePaySettingParams {
  /**
   * 应用ID
   * @example "51"
   */
  id: number

  /**
   * 支付方式
   * @remarks [1=微信,2=支付宝]
   * @example [1, 2]
   */
  payment_type: number[]

  /**
   * 价格设置
   */
  price_setting: PriceSettingItem[]
}

/**
 * 获取应用列表
 * @param params 分页参数
 * @returns 应用列表数据
 */
export function getAddonList(params: IPageRequest) {
  return Alova.Get<IPageResult<AddonItem>>('/admin/addons', { params })
}
/**
 * 获取应用详情
 *
 *
 */
export interface PriceSettingItem {
  unit: number
  money: number
  duration: number
}

export interface ApplicationParams {
  id: number
  title: string
  group: string
  cover: string
  brief_introduction: string
  description: string
  price_setting: PriceSettingItem[]
  payment_type: number[]
}
export interface PayOption {
  label: string
  money: number
}
// 主体类型
export interface PaySettingItem {
  id: number
  title: string
  name: string
  author: string
  version: string
  cover: string
  group: string
  brief_introduction: string
  is_online: number
  payment_type: number[]
  price_setting: PriceSettingItem[]
  pay_options?: PayOption[]
  payVal?: number
  payTimeVal?: string
}

/**
 * 获取应用详情
 * @param id 应用ID
 * @returns 应用详情数据
 */
export function getAddonDetail(id: number) {
  return Alova.Get<ApplicationParams>(`/admin/addons/${id}`)
}

/**
 * 编辑应用
 * @param params 应用编辑参数
 * @returns 操作结果
 */
export function editAddon(params: EditAddonParams, id: number) {
  return Alova.Put(`/admin/addons/${id}`, params)
}

/**
 * 应用上下架操作
 * @param id 应用ID
 * @returns 操作结果
 */
export function changeAddonStatus(id: number) {
  return Alova.Get('/admin/addons/change-online', { params: { id } })
}

/**
 * 应用安装
 * @param params 安装参数
 * @returns 操作结果
 */
export function installAddon(params: InstallAddonParams) {
  return Alova.Post('/admin/addons/install', params)
}

/**
 * 批量安装应用
 * @param params 批量安装参数
 * @returns 操作结果
 */
export function batchInstallAddons(params: BatchInstallAddonParams) {
  return Alova.Post('/admin/addons/batch-install', params)
}

/**
 * 应用卸载
 * @param id 应用ID
 * @returns 操作结果
 */
export function uninstallAddon(id: number) {
  return Alova.Get('/admin/addons/uninstall', { params: { id } })
}

/**
 * 获取本地应用列表
 * @returns 本地应用列表数据
 */
export function getLocalAddonList(params) {
  return Alova.Get<{ list: LocalAddonItem[] }>('/admin/addons/local', params)
}

/**
 * 获取付费设置列表
 * @returns 付费设置列表数据
 */
export function getPaySettingList(params: PaySettingParams) {
  return Alova.Get<IPageResult<PaySettingItem>>('/admin/addons/pay-setting', { params })
}
/**
 * 获取付费设置详情
 * @param id 付费设置ID
 * @returns 付费设置详情数据
 */
export function getPaySettingDetail(id: number) {
  return Alova.Get<PaySettingItem>(`/admin/addons/${id}`)
}
/**
 * 修改付费设置
 * @param params 付费设置参数
 * @returns 操作结果
 */
export function changePaySetting(params: ChangePaySettingParams) {
  return Alova.Post('/admin/addons/change-pay-setting', params)
}
/**
 * 更新配置
 * @param params 更新配置参数
 * @returns 操作结果
 */
export function updateConfig(params: UpdateConfigParams) {
  return Alova.Post('/admin/addons/update-config', params)
}
/**
 * 应用套餐列表
 * @returns 应用套餐列表数据
 */
export function getAddonPackageList(params: addonParams) {
  return Alova.Get<IPageResult<AddonPackageItem>>('/admin/addons-combo', { params })
}
/**
 * 应用套餐详情
 */
export function addonsComboInfo(id: number) {
  return Alova.Get<addonsDetails>(`/admin/addons-combo/${id}`)
}
/**
 * 应用套餐修改
 */
export function editAddonsCombo(id: number, params: MealParams) {
  return Alova.Put(`/admin/addons-combo/${id}`, params)
}
/**
 * 应用套餐删除
 */
export function deleteAddonsCombo(id: number) {
  return Alova.Delete(`/admin/addons-combo/${id}`)
}
