import { Alova } from '@/utils/http/alova/index'
/**
 * 用户参数
 */
export interface UserParams {
  id?: number // 用户id
  username: string // 账号
  nickname: string // 昵称
  password?: string // 密码
  type: number // 1 超级管理员 2 管理员 3 用户
}

/**
 * 用户列表
 */
export interface UserItem {
  id: number
  username: string
  nickname: string
  admin_type: number
  status: number
  created_at: string
  updated_at: string
}

/**
 * 商城列表项
 */
interface mallItem {
  id: number
  name: string
  expired_at: number
  creator: {
    nickname: string
  }
}
/**
 * 用户列表请求参数
 */
interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 获取用户列表
 * @param params
 * @returns
 */
export function getUserList(params: UserListRequest) {
  return Alova.Get<IPageResult<UserItem>>('/admin/user', { params })
}

/**
 * 添加用户
 * @param params
 * @returns
 */
export function addUser(params: UserParams) {
  return Alova.Post('/admin/user', params)
}

/**
 * 编辑用户
 * @param params
 * @returns
 */
export function editUser(params: UserParams) {
  return Alova.Put(`/admin/user/${params.id}`, params)
}

/**
 * 获取用户详情
 * @param id
 * @returns
 */
export function getUserDetail(id: number) {
  return Alova.Get<UserItem>(`/admin/user/${id}`)
}

/**
 * 删除用户
 * @param id
 * @returns
 */
export function deleteUser(id: number) {
  return Alova.Delete(`/admin/user/${id}`)
}

/**
 * 获取用户商城列表
 * @param id
 * @returns
 */
export function getUserMallList(params: { id: number }) {
  return Alova.Get<IPageResult<mallItem>>(`/admin/user/mall`, { params })
}
