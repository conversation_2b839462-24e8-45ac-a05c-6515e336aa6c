import { Alova } from '@/utils/http/alova/index'

export type SOPListType = {
  created_at?: string
  id?: number
  is_key?: number
  multiline_id?: number
  is_completed?: number
  is_current?: number
  sort: number
  tasks: SOPListTaskType[]
  title?: string
  type?: number
  updated_at?: string
  status?: 'noStart' | 'noExecute' | 'progressNoAcitve' | 'progressAcitve' | 'completed'
  direction?: 'left' | 'right' | 'start' | 'end'
}
export type SOPListTaskType = {
  content?: string
  created_at?: string
  id: string
  is_key?: number
  sort?: number
  stage_id?: number
  title?: string
  updated_at?: string
  is_completed?: number
}
export type SOPTaskDetailType = {
  content: string
  summary: string
  title: string
}
export type RequestExecuteSopTaskType = {
  id: string
  stage_id: string
  task_id: string
  summary: string
}

export type RequestLightUpSopStageType = {
  type: string
  id: string
  stage_id: string
}

export type RequestSwitchSopStageType = {
  /**
   * 变更后SOP阶段ID
   */
  after_stage_id: number
  /**
   * 变更前SOP阶段ID
   */
  before_stage_id: number
  /**
   * 线索id
   */
  id: string
  /**
   * 固定传1，表示我的线索SOP
   */
  type: string
}

// 根据 type 值处理请求地址
/**
 * 
 *        1,//我的线索-详情
          2,//我的客户-详情
          3,//我的商机-详情
          11,//部门线索-详情
          22,//部门客户-详情
          33,//部门商机-详情
          101,//公海线索-详情
          102,//回收站线索-详情
 * @param type 
 * @returns 
 */
const getSopListUrl = (type: string) => {
  switch (type) {
    case '1':
      return '/myLeads'
    case '2':
      return '/customer'
    case '3':
      return '/customer'
    case '11':
      return '/leads'
    case '22':
      return '/customer'
    case '33':
      return '/customer'
    case '101':
      return '/leads'
    case '102':
      return '/leads'
  }
}

/**
 * type: 1：线索SOP，2：客户SOP，3：商机SOP
 * @description: 获取线索SOP流程
 */
export function showLeadsSopList(id: string, type: string) {
  return Alova.Get<SOPListType[]>(`${getSopListUrl(type)}/getStagePreviewData`, {
    params: { id, type },
  })
}

/**
 * type: 1：线索SOP，2：客户SOP，3：商机SOP
 * @description: 获取线索SOP任务详情
 */
export function getSopTaskDetail(id: string, task_id: string, type: string) {
  return Alova.Get<SOPTaskDetailType[]>(`${getSopListUrl(type)}/getSopTaskDetail`, {
    params: { id, task_id, type },
  })
}

/**
 * type: 1：线索SOP，2：客户SOP，3：商机SOP
 * @description: 执行SOP任务
 */
export function executeSopTask(data: RequestExecuteSopTaskType, type: string) {
  return Alova.Post(`${getSopListUrl(type)}/executeSopTask`, { ...data, type })
}

/**
 * type: 1：线索SOP，2：客户SOP，3：商机SOP
 * @description: 点亮SOP阶段
 */
export function lightUpSopStage(data: RequestLightUpSopStageType) {
  return Alova.Post(`${getSopListUrl(data.type)}/lightUpSopStage`, data)
}

/**
 * 详情-SOP阶段切换
 * @description: 切换SOP阶段
 */
export function switchSopStage(data: RequestSwitchSopStageType) {
  return Alova.Post(`${getSopListUrl(data.type)}/switchSopStage`, data)
}
