import { Alova } from '@/utils/http/alova/index'

export declare interface SettingItem {
  id: number
  title: string
  multiline_id: number
  type: number
  is_key: number
  sort: number
  created_at: string
  updated_at: string
}
export declare interface TaskItem {
  id: number
  title: string
  stage_id: number
  content: string
  is_key: number
  sort: number
  created_at: string
  updated_at: string
}

/**
 * @description: 创建阶段
 */
export function createStage(params: any) {
  return Alova.Post(`/setting/sop/stage`, params)
}

/**
 * @description: 编辑阶段
 */
export function editStage(id: number, params: any) {
  console.log(params)
  return Alova.Put(`/setting/sop/stage/${id}`, params)
}

/**
 * @description: 排序阶段
 */
export function sortStage(ids: number[]) {
  return Alova.Post(`/setting/sop/stage/transfer_sort`, { ids })
}

/**
 * @description: 删除阶段
 */
export function deleteStage(id: number) {
  return Alova.Delete(`/setting/sop/stage/${id}`)
}

/**
 * @description: 获取阶段列表
 */
export function getStageList(params: any) {
  return Alova.Get<IPageResult<SettingItem>>(`/setting/sop/stage`, { params })
}

/**
 * @description: 获取阶段任务列表
 */
export function getTaskSetting() {
  return Alova.Get<TaskItem[]>(`/setting/sop/task`)
}

/**
 * @description: 获取阶段列表
 */
export function addTaskSetting(params: any) {
  return Alova.Post(`/setting/sop/task`, params)
}

/**
 * @description: 获取阶段列表
 */
export function editTaskSetting(id: number, params: any) {
  return Alova.Put(`/setting/sop/task/${id}`, params)
}

/**
 * @description: 删除任务
 */
export function deleteTask(id: number) {
  return Alova.Delete(`/setting/sop/task/${id}`)
}

/**
 * @description: 排序任务
 */
export function sortTask(ids: number[]) {
  return Alova.Post(`/setting/sop/task/transfer_sort`, { ids })
}

export function getSopAutoReset() {
  return Alova.Get(`/setting/sop/showAutoReset`)
}

export function editSopAutoReset() {
  return Alova.Post(`/setting/sop/enableAutoReset`)
}
