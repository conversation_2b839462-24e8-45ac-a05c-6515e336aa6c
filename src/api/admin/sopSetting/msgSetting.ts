import { Alova } from '@/utils/http/alova/index'

export interface MessageItem {
  content: string
  created_at: string
  id: number
  internal_notify_status: number
  status: number
  title: string
  type: number //消息模版类型（1：公共消息，2：部门负责人消息，3：个人消息，4：协作组消息
  updated_at: string
}

/**
 * @description: 获取消息模板列表
 */
export function getMessageTemplateList() {
  return Alova.Get<MessageItem[]>(`/setting/message/template`)
}

/**
 * @description: 编辑模板(控制开关)
 */
export function updateMessageTemplate(id: number, params: any) {
  return Alova.Put(`/setting/message/template/${id}`, params)
}
