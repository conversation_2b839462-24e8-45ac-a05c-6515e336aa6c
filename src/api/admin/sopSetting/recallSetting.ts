import { Alova } from '@/utils/http/alova/index'

export declare interface SettingForm {
  lead_daily_receive_num: number
  lead_pool_limit: number
  no_business_recall: number
  no_operation_recall: number
  unconverted_recall: number
}

/**
 * @description: 获取设置列表
 */
export function getSettingList(params: any) {
  return Alova.Get<SettingForm>(`/setting`, { params })
}

/**
 * @description: 修改设置列表
 */
export function batchSet(params: any) {
  return Alova.Post(`/setting/batch_set`, params)
}
