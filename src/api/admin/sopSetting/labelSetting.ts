import { Alova } from '@/utils/http/alova/index'

export interface Setting {
  id: number
  category_name: string
  status: number
  apply_range: string
  created_at: string
  updated_at: string
  hover: boolean
}

export interface LabelSetting {
  id: number
  name: string
  color: string
  category_id: number
  use_times: number
  remark: string
  status: number
  created_at: string
  updated_at: string
}

/**
 * @description: 获取分类列表
 */
export function getCategorySettingList() {
  return Alova.Get<IPageResult<Setting>>(`/setting/label/category`)
}

/**
 * @description: 修改分类状态
 */
export function transferCategoryStatus(params: any) {
  return Alova.Post(`/setting/label/category/transfer_status`, params)
}

/**
 * @description: 修改分类状态
 */
export function transferStatus(params: any) {
  return Alova.Post(`/setting/label/transfer_status`, params)
}

/**
 * @description: 获取单个分类的标签列表
 */
export function getLabelSettingList(params: any) {
  return Alova.Get<IPageResult<LabelSetting>>(`/setting/label`, { params })
}

/**
 * @description: 添加分类
 */
export function addCategory(params: any) {
  return Alova.Post(`/setting/label/category`, params)
}

/**
 * @description: 编辑分类
 */
export function updateCategory(id: number, params: any) {
  return Alova.Put(`/setting/label/category/${id}`, params)
}

/**
 * @description: 删除分类
 */
export function deleteCategory(id: number) {
  return Alova.Delete(`/setting/label/category/${id}`)
}

/**
 * @description: 添加标签
 */
export function addLabel(params: any) {
  return Alova.Post(`/setting/label`, params)
}

/**
 * @description: 编辑标签
 */
export function updateLabel(id: number, params: any) {
  return Alova.Put(`/setting/label/${id}`, params)
}

/**
 * @description: 删除标签
 */
export function deleteLabel(id: number) {
  return Alova.Delete(`/setting/label/${id}`)
}
