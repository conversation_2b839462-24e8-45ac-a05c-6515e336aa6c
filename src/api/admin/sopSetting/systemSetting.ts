import { Alova } from '@/utils/http/alova/index'

export interface systemSetting {
  location_setting: location_setting[]
}

export interface location_setting {
  type: string
  platform: string
  auth_id: string
  access_key_id: string
  access_key_secret: string
  selected: boolean
}

/**
 * @description: 获取归属地配置
 */
export function getSystemSetting() {
  return Alova.Get<systemSetting>(`/setting?group=system_setting`)
}
/**
 * @description: 设置系统配置
 */
export function systemSettingSet(params: any) {
  return Alova.Post(`/system_setting/batch_set`, params)
}
