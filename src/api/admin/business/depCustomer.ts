import { Alova } from '@/utils/http/alova/index'

// 定义客户接口
export interface Customer {
  id: number
  customer_name: string
  phone: string
  company_name: string
  wechat_avatar: string
  industry_id: number
  source: number
  source_account: string
  entry_methods: number
  transaction_status: number
  director_id: number
  other_info: string
  created_at: string
  updated_at: string
  creator: string
  entry_methods_text: string
  transaction_status_text: string
  opportunity_num: number
  label_relationship: any[]
  _showCustomerDetail?: (row: any) => void
  industry: Industry
  director: Director
}

export interface CustomerDetail {
  id: number
  department_id: number
  customer_name: string
  phone: string
  company_name: string
  wechat_avatar: string
  industry_id: number
  source: number
  director_id: number
  phone_address: string
  address: string
  remark: string
  creator_id: number
  first_assign_time: string
  assign_time: string
  call_status: number
  sex: string
  execute_assign_id: number
  follow_status: number
  created_at: string
  updated_at: string
  creator: string
  phone_id: number
  follow_status_text: string
  call_status_text: string
  collaborator_text: string
  industry_name: string
  source_name: string
  assigner_name: string
  director_name: string
  department_name: string
  current_position: string
  opportunity_num: number
}

// 定义行业接口
export interface Industry {
  id: number
  industry_name: string
}

// 定义负责人接口
export interface Director {
  id: number
  employee_name: string
  username: string
  lead_daily_receive_num: number | null
  lead_pool_limit: number | null
  department_link: string
  roles: Role[]
  jobs: Job[]
}

// 定义角色接口
export interface Role {
  id: number
  role_name: string
  identify: string
  parent_id: number
  description: string
  data_range: number
  check_public_lead_permission: number
  public_lead_permission: any // 根据实际情况定义类型
  creator_id: number
  created_at: string
  updated_at: string
  member_count: number
  pivot: {
    user_id: number
    role_id: number
  }
}

// 定义工作接口
export interface Job {
  id: number
  job_name: string
  coding: string | null
  status: number
  sort: number
  description: string | null
  creator_id: number
  created_at: string
  updated_at: string
  pivot: {
    user_id: number
    job_id: number
  }
}

/**
 * 导出字段
 */
export interface Fields {
  age?: string
  company_name?: string
  created_at?: string
  customer_name?: string
  director_id?: string
  follow_status?: string
  industry_id?: string
  label_id?: string
  other_info?: string
  phone?: string
  phone_address?: string
  sex?: string
  source?: string
  wechat?: string
  transaction_status?: string
}

export interface ExportData {
  fields: Fields
  key: string
}

export type CustomerLabelType = {
  id: number
  name: string
}

export interface FormatLabels {
  list: Category[]
  selected_count: number
  selected_list: LabelSetting[]
}

export interface Category {
  id: number
  category_name: string
  label_setting: LabelSetting[]
}

export interface LabelSetting {
  id: number
  name: string
  category_id: number
  color: string
}

export interface CustomerFilterOptions {
  entry_methods_options: any[]
  transaction_status_options: any[]
}

// 客户操作记录
export interface CustomerRecord {
  created_at: string
  creator: string
  creator_id: number
  creator_role: string
  date: string
  id: number
  link_id: number
  operate_content: string
  time: string
  type: number
}

export interface CollaboratorTree {
  id?: number
  name: string
  parent_id: number
  is_employee: number
  children: CollaboratorTree[]
}

export interface Collaborator {
  created_at?: string
  creator?: string
  customer_id?: number
  department_link?: string
  employee_name?: string
  id?: number
  opportunity_id?: number
  permission?: number
  roles_link?: string
  type?: number
  updated_at?: string
  user_id?: number
  username?: string
}

/**
 * @description: 部门客户列表
 */
export function getCustomerList(params: any) {
  return Alova.Get<IPageResult<Customer>>(`/departmentCustomer/customer`, { params })
}

/**
 * @description: 获取客户详情
 */
export function getCustomerDetail(id: number) {
  return Alova.Get<CustomerDetail>(`/departmentCustomer/customer/${id}`)
}

/**
 * @description: 编辑客户信息
 */
export function updateCustomerInfo(id: number, params: any) {
  return Alova.Put(`/departmentCustomer/customer/${id}`, params)
}

/**
 * @description: 获取部门客户导出字段
 */
export function getTaskFields() {
  return Alova.Get<ExportData>(`/departmentCustomer/getTaskFields?key=customer`)
}

/**
 * @description: 部门客户导出
 */
export function generateTask(params: any) {
  return Alova.Post(`/departmentCustomer/generateTask`, params)
}

/**
 * @description: 获取客户标签下拉选项
 */
export function getCustomerLabels() {
  return Alova.Get<CustomerLabelType[]>(`/customer/getCustomerLabels`)
}

/**
 * @description: 获取详情页标签列表
 */
export function getFormatLabels(id: number) {
  return Alova.Get<FormatLabels>(`/label/getFormatLabels?type=2&link_id=${id}`)
}

/**
 * @description: 获取成交状态，转化方式下拉选项
 */
export function getCustomerFilterOptions() {
  return Alova.Get<CustomerFilterOptions>(`/customer/getCustomerFilterOptions`)
}

/**
 * @description: 获取客户操作记录
 */
export function getCustomerRecord(params: any) {
  return Alova.Get<IPageResult<CustomerRecord>>(`/departmentCustomer/getLogData?type=2`, { params })
}

/**
 * @description: 操作记录导出
 */
export function generateLogTask(params: any) {
  return Alova.Post(`/departmentCustomer/log/generateTask`, params)
}

/**
 * @description: 详情退回线索
 */
export function detailReturnLead(params: any) {
  return Alova.Post(`/departmentCustomer/detailBackToLead`, params)
}

/**
 *
 * @description: 获取部门客户详情--协作员列表
 */
export function getCollaborator(params: any) {
  return Alova.Get<Collaborator[]>(`/departmentCustomer/collaborator`, { params })
}

/**
 * @description: 部门客户详情--添加协作员
 */
export function addCollaborator(params: any) {
  return Alova.Post(`/departmentCustomer/collaborator`, params)
}

/**
 * @description: 部门客户详情--删除协作员
 */
export function deleteCollaborator(id: number) {
  return Alova.Delete(`/departmentCustomer/collaborator/${id}`)
}

/**
 * @description: 修改协作员权限
 */
export function transferPermission(id: number) {
  return Alova.Put(`/departmentCustomer/collaborator/changePermission/${id}`)
}

/**
 * @description: 获取负责人列表
 */
export function getAssignPeople() {
  return Alova.Get<Director[]>(`/customer/getSelectUserTree`)
}

/**
 * 商机列表项接口
 */
export interface OpportunityItem {
  id: number
  business_name: string
  business_stage: string
  estimated_win_amount: number
  estimated_win_rate: string
  actual_win_date: string
  created_at: string
  updated_at: string
}

/**
 * 根据客户ID获取商机列表
 * @param customerId 客户ID
 * @returns 商机列表
 */
export function getOpportunityListByCustomerId(customerId: number) {
  return Alova.Get<OpportunityItem[]>(`/admin/customer/${customerId}/opportunities`)
}

// 跟进记录
export interface FollowRecord {
  creator: string
  creator_id: number
  follow_up_at: string
  follow_up_content: string
  id: number
}

// 跟进记录列表
export function getFollowRecord(params: any) {
  return Alova.Get<IPageResult<FollowRecord>>(`/departmentCustomer/getRecordUpRecords?type=2`, {
    params,
  })
}

// 删除跟进记录
export function deleteFollowRecord(id: number) {
  return Alova.Delete(`/departmentCustomer/delFollowUpRecord/${id}`)
}

/**
 * @description: 添加跟进记录
 */
export function addFollowRecord(params: any) {
  return Alova.Post('/departmentCustomer/addRecordUpRecords', params)
}
