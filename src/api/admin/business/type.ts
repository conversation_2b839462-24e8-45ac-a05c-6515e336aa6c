export interface RequestGetOpportunityList {
  /**
   * 企业名称
   */
  company_name?: string
  /**
   * 创建时间
   */
  'created_at[]'?: string[]
  /**
   * 联系人姓名
   */
  customer_name?: string
  /**
   * 商机标签
   */
  label_id?: number
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 场景值，此次传 opportunity_my_page
   */
  scene?: string
  /**
   * SOP阶段
   */
  sop_stage_id?: number
  /**
   * 商机状态
   */
  status?: number
  /**
   * 0：全部，1：我负责的，2：我协作的
   */
  tabType?: string
  /**
   * 商机名称
   */
  title?: string
}

/**
 * 商机列表
 */
export interface BusinessList {
  /**
   * 协作员
   */
  collaborators_name_text: string
  /**
   * 企业名称
   */
  company_name: string
  /**
   * 创建时间
   */
  created_at: string
  /**
   * 客户ID
   */
  customer_id: number
  /**
   * 客户名称
   */
  customer_name: string
  /**
   * 所属部门
   */
  department_name: string
  /**
   * 负责人
   */
  director_name: string
  /**
   * 负责人ID
   */
  director_user_id: number
  /**
   * 预计成交金额
   */
  expected_transaction_amount: string
  /**
   * 预计成交日期
   */
  expected_transaction_at: string
  /**
   * 预计赢单率
   */
  expected_win_rate: string
  /**
   * 最新跟进记录-时间
   */
  follow_up_at: string
  /**
   * 最新跟进记录-内容
   */
  follow_up_content: string
  /**
   * 最新跟进记录创建人
   */
  follow_up_creator: string
  /**
   * 商机ID
   */
  id: number
  /**
   * 所属行业
   */
  industry_name: string
  /**
   * 标签关联数组
   */
  label_relationship: LabelRelationship[]
  /**
   * 客户手机号
   */
  phone: string
  /**
   * 商机金额
   */
  sales_amount: string
  /**
   * 当前商机阶段ID
   */
  sop_stage_id: number
  /**
   * 商机阶段排序
   */
  sop_stage_sort: number
  /**
   * SOP阶段
   */
  sop_stage_title: string
  /**
   * 商机状态
   */
  status: number
  /**
   * 商机状态文案
   */
  status_text: string
  /**
   * 商机名称
   */
  title: string
  /**
   * 头像
   */
  wechat_avatar: string
}

export interface LabelRelationship {
  /**
   * 关联ID
   */
  id: number
  /**
   * 标签颜色
   */
  label_color: string
  /**
   * 标签ID
   */
  label_id: number
  /**
   * 标签名称
   */
  label_name: string
  /**
   * 商机ID
   */
  opportunity_id: number
  /**
   * 类型
   */
  type: number
}

/**
 * 协作员列表
 */
export interface RequestCollaboratorListParams {
  /**
   * 客户协作员
   */
  customer_id?: string
  /**
   * 商机ID
   */
  opportunity_id?: string
  /**
   * 场景值，新增商机传add，编辑商机传edit
   */
  scene?: string
}

export interface CollaboratorList {
  /**
   * 员工名称
   */
  employee_name: string
  /**
   * 数据ID
   */
  id: number
  /**
   * 是否选中，1是，2否
   */
  is_selected: number
  /**
   * 关联ID
   */
  relationship_id: number
  /**
   * 用户ID
   */
  user_id: number
}

export interface RequestGetCustomerList {
  /**
   * 客户名称或手机搜索字符串
   */
  mixNameOrPhone?: string
  /**
   * 场景值，此次传opportunity_select_customer
   */
  scene?: string
}

export interface CustomerList {
  /**
   * 企业名称
   */
  company_name: string
  /**
   * 客户名称
   */
  customer_name: string
  /**
   * 客户ID
   */
  id: number
  /**
   * 手机号
   */
  phone: string
  /**
   * 当前阶段ID
   */
  sop_stage_id: number
  /**
   * SOP阶段
   */
  sop_stage_title: string
  /**
   * 成交状志
   */
  transaction_status: number
  /**
   * 成交状态文案
   */
  transaction_status_text: string
  /**
   * 头像
   */
  wechat_avatar: string
}
export interface RequestAddProductDetail {
  /**
   * 客户ID
   */
  customer_id: number
  /**
   * 预计成交金额
   */
  expected_transaction_amount: string
  /**
   * 预计成交日期
   */
  expected_transaction_at: string
  /**
   * 预计赢单率
   */
  expected_win_rate: string
  /**
   * 数据权限，1：只读，2：读写
   */
  permission: number
  /**
   * 产品信息
   */
  products: Product[]
  /**
   * 备注
   */
  remark: string
  /**
   * 场景值，此次传 customer_detail_add
   */
  scene: string
  /**
   * 商机名称
   */
  title: string
  /**
   * 协作员用户ID数组
   */
  user_ids: number[]
  /**
   * 整单折扣
   */
  whole_order_discount: number
}
export interface Product {
  /**
   * 折扣
   */
  discount?: number
  /**
   * 数量
   */
  num?: number
  /**
   * 售价
   */
  price?: number
  /**
   * 产品ID
   */
  product_id?: number
}
