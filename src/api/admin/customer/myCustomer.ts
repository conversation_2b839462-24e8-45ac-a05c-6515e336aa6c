import { Alova } from '@/utils/http/alova/index'
import {
  RequestAddProductDetail,
  CollaboratorList,
} from '@/views/admin/customer/config/customerTypes'
import { FROM_TYPE_MY_CUSTOMER, FROM_TYPE_DEPARTMENT_CUSTOMER } from '@/enums/fromTypeEnum'
export interface FormatLabels {
  list: Category[]
  selected_count: number
  selected_list: LabelSetting[]
}
export interface LabelSetting {
  id: number
  name: string
  category_id: number
  color: string
}

// 定义分类接口
export interface Category {
  id: number
  category_name: string
  label_setting: LabelSetting[]
}

// 定义角色接口
export interface Role {
  id: number
  role_name: string
  identify: string
  parent_id: number
  description: string
  data_range: number
  check_public_lead_permission: number
  public_lead_permission: any // 根据实际情况定义类型
  creator_id: number
  created_at: string
  updated_at: string
  member_count: number
  pivot: {
    user_id: number
    role_id: number
  }
}

// 定义工作接口
export interface Job {
  id: number
  job_name: string
  coding: string | null
  status: number
  sort: number
  description: string | null
  creator_id: number
  created_at: string
  updated_at: string
  pivot: {
    user_id: number
    job_id: number
  }
}

// 定义行业接口
export interface Industry {
  id: number
  industry_name: string
}

// 定义负责人接口
export interface Director {
  id: number
  employee_name: string
  username: string
  lead_daily_receive_num: number | null
  lead_pool_limit: number | null
  department_link: string
  roles: Role[]
  jobs: Job[]
}

// 定义客户接口
export interface Customer {
  id: number
  customer_name: string
  phone: string
  company_name: string
  wechat_avatar: string
  industry_id: number
  source: number
  source_account: string
  entry_methods: number
  transaction_status: number
  director_id: number
  other_info: string
  created_at: string
  updated_at: string
  creator: string
  entry_methods_text: string
  transaction_status_text: string
  opportunity_num: number
  label_relationship: any[]
  industry: Industry
  director: Director
  _showCustomerDetail: (row: Customer) => void
}

export interface DirectorTree {
  id: number
  is_employee: number
  name: string
  parent_id: number
  children: DirectorTree[]
}

export interface CustomerDetail {
  id: number
  department_id: number
  customer_name: string
  phone: string
  company_name: string
  wechat_avatar: string
  industry_id: number
  source: number
  director_id: number
  phone_address: string
  address: string
  remark: string
  creator_id: number
  first_assign_time: string
  assign_time: string
  call_status: number
  sex: string
  execute_assign_id: number
  follow_status: number
  created_at: string
  updated_at: string
  creator: string
  phone_id: number
  follow_status_text: string
  call_status_text: string
  collaborator_text: string
  industry_name: string
  source_name: string
  assigner_name: string
  director_name: string
  department_name: string
  current_position: string
  opportunity_num: number
  location_district_id: number
  location_city_id: number
  location_province_id: number
  location_address: string
}

export interface CustomerFilterOptions {
  entry_methods_options: any[]
  transaction_status_options: any[]
}

export type CustomerLabelType = {
  id: number
  name: string
}

export interface Collaborator {
  created_at?: string
  creator?: string
  customer_id?: number
  department_link?: string
  employee_name?: string
  id?: number
  opportunity_id?: number
  permission?: number
  roles_link?: string
  type?: number
  updated_at?: string
  user_id?: number
  username?: string
}

export interface ExportData {
  fields: Fields
  key: string
}

/**
 * 导出字段
 */
export interface Fields {
  age?: string
  company_name?: string
  created_at?: string
  customer_name?: string
  director_id?: string
  follow_status?: string
  industry_id?: string
  label_id?: string
  other_info?: string
  phone?: string
  phone_address?: string
  sex?: string
  source?: string
  wechat?: string
  transaction_status?: string
}

export interface CustomerTabCount {
  tab_all_count: number
  tab_director_count: number
  tab_collaborator_count: number
  sub_tab_director_all_count: number
  sub_tab_collaborator_all_count: number
  sub_tab_director_transacted_count: number
  sub_tab_director_not_transacted_count: number
  sub_tab_collaborator_transacted_count: number
  sub_tab_collaborator_not_transacted_count: number
}

// 部门类型（递归结构）
export interface CollaboratorTree {
  id?: number
  name: string
  parent_id: number
  is_employee: number
  children: CollaboratorTree[]
}

// 导入客户的返回
export interface ImportCustomerResponse {
  success_count: number
  failure_count: number
  download_url: string
}

// 客户操作记录
export interface CustomerRecord {
  created_at: string
  creator: string
  creator_id: number
  creator_role: string
  date: string
  id: number
  link_id: number
  operate_content: string
  time: string
  type: number
  _showCustomerDetail: (row: CustomerRecord) => void
}
export type RequestGenerateExportTaskType = {
  /**
   * 导出字段信息
   */
  checked: string[]
  /**
   * 搜索信息
   */
  filter: Filter
  /**
   * 导出位置标识
   */
  key: string
}

/**
 * 搜索信息
 */
export type Filter = {
  tab_type?: number
  sub_tab_type?: number
  scene?: string
  from?: number
}
/**
 * @description: 我的客户列表
 */
export function getCustomerList(params: any) {
  return Alova.Get<IPageResult<Customer>>(`/customer/customer`, { params })
}

/**
 * @description: 获取客户详情
 */
export function getCustomerDetail(id: string) {
  return Alova.Get<CustomerDetail>(`/customer/customer/${id}`)
}

/**
 * @description: 编辑客户信息
 */
export function updateCustomerInfo(id: number, params: any) {
  return Alova.Put(`/customer/customer/${id}`, params)
}

/**
 * @description: Tab栏数据统计
 */
export function getTabData() {
  return Alova.Get<CustomerTabCount>(`/customer/getTabData`)
}

/**
 * @description: 获取详情页标签列表
 */
export function getFormatLabels(id: number) {
  return Alova.Get<FormatLabels>(`/label/getFormatLabels?type=2&link_id=${id}`)
}

/**
 * @description: 添加客户标签
 */
export function addLabelRelationship(
  type: number,
  link_id: number,
  label_ids: number[],
  fromType: number,
) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/editLabel'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/editLabel'
  }
  return Alova.Post(api, {
    type: type,
    link_id: link_id,
    label_ids: label_ids,
  })
}

/**
 * @description: 新增客户
 */
export function addCustomer(params: any) {
  return Alova.Post(`/customer/customer`, params)
}

/**
 * @description: 获取成交状态，转化方式下拉选项
 */
export function getCustomerFilterOptions() {
  return Alova.Get<CustomerFilterOptions>(`/customer/getCustomerFilterOptions`)
}

/**
 * @description: 获取客户标签下拉选项
 */
export function getCustomerLabels() {
  return Alova.Get<CustomerLabelType[]>(`/customer/getCustomerLabels`)
}

/**
 * @description: 获取导出字段
 */
export function getTaskFields() {
  return Alova.Get<ExportData>(`/download-task/getTaskFields?key=customer`)
}

/**
 * @description: 我的客户导出
 */
export function generateTask(params: any) {
  return Alova.Post(`/customer/generateTask`, params)
}

/**
 * @description: 操作记录导出
 */
export function generateLogTask(params: any) {
  return Alova.Post(`/customer/log/generateTask`, params)
}

/**
 * @description: 获取导入客户模板文件
 */
export function getImportTemplate() {
  return Alova.Get<{ url: string }>(`/getImportTemplate?key=customer`)
}

/**
 * @description: 导入客户
 */
export function importCustomer(params: any) {
  return Alova.Post<ImportCustomerResponse>(`/customer/importCustomer`, params)
}

/**
 * @description: 协作员列表
 */
export function getCollaborator(params: any, fromType: number) {
  console.log('params', params)

  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/collaborator'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/collaborator'
  }
  return Alova.Get<Collaborator[]>(api, { params: { ...params } })
}

/**
 * @description: 修改协作员权限
 */
export function transferPermission(id: number, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/collaborator/changePermission'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/collaborator/changePermission'
  }
  return Alova.Put(`${api}/${id}`)
}

/**
 * @description: 我的客户详情--协作员下拉列表
 */
export function getSelectCollaboratorTree(fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/getSelectCollaboratorTree'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/getUserTree'
  }
  return Alova.Get<CollaboratorTree[]>(api)
}

/**
 * @description: 添加协作员
 */
export function addCollaborator(params: any, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/collaborator'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/collaborator'
  }
  return Alova.Post(api, params)
}

/**
 * @description: 删除协作员
 */
export function deleteCollaborator(id: number, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/collaborator'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/collaborator'
  }
  return Alova.Delete(`${api}/${id}`)
}

/**
 * @description: 退回线索
 */
export function returnLead(params: any, fromType: number, isDetail: boolean) {
  let api = ''
  if (isDetail) {
    if (fromType === FROM_TYPE_MY_CUSTOMER) {
      api = '/customer/detailBackToLead'
    } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
      api = '/departmentCustomer/detailBackToLead'
    }
  } else {
    api = '/customer/backToLead'
  }
  return Alova.Post(api, params)
}

/**
 * @description: 详情退回线索
 */
export function detailReturnLead(params: any) {
  return Alova.Post(`/customer/detailBackToLead`, params)
}

/**
 * @description: 获取负责人列表
 */
export function getAssignPeople() {
  return Alova.Get<DirectorTree[]>(`/customer/getSelectUserTree`)
}

/**
 * @description: 转移客户
 */
export function transferCustomers(params: any) {
  return Alova.Post(`/customer/changeDirector`, params)
}

/**
 * @description: 获取客户操作记录
 */
export function getCustomerRecord(params: any, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/getLogData'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/getLogData'
  }
  return Alova.Get<IPageResult<CustomerRecord>>(`${api}?type=2`, { params })
}

/**
 * @description: 修改跟进状态
 */
export function changeCallStatus(id: number, call_status_id: number, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/changeCallStatus'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/changeCallStatus'
  }
  return Alova.Post(api, { id, call_status_id })
}

// 跟进记录接口
export interface FollowRecord {
  creator: string
  creator_id: number
  follow_up_at: string
  follow_up_content: string
  id: number
}

//获取跟进记录列表
export function getFollowRecord(params: any, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/getRecordUpRecords'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/getRecordUpRecords'
  }
  return Alova.Get<IPageResult<FollowRecord>>(`${api}?type=2`, { params })
}

//添加跟记录
export function addFollowRecord(params: any, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/addRecordUpRecords'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/addRecordUpRecords'
  }
  return Alova.Post(api, params)
}

//删除跟进记录
export function deleteFollowRecord(id: number, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/delFollowUpRecord'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/delFollowUpRecord'
  }
  return Alova.Delete(`${api}/${id}`)
}

export interface Business {
  collaborators_name_text: string
  created_at: string
  customer_id: number
  customer_name: string
  employee_name: string
  expected_win_rate: string
  id: number
  products_name_text: string
  sop_stage_id: number
  sop_stage_title: string
  title: string
}

//获取商机列表
export function getBusinessList(params: any) {
  return Alova.Get<IPageResult<Business>>(
    `/customer/businessOpportunities/getOpportunityPageList`,
    { params },
  )
}

// 新增商机
export function addBusiness(params: RequestAddProductDetail, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = '/customer/businessOpportunities/executeUpdate'
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = '/departmentCustomer/businessOpportunities/executeUpdate'
  }
  return Alova.Post(api, params)
}

//编辑商机
export function updateBusiness(id: number, params: any, fromType: number) {
  let api = ''
  if (fromType === FROM_TYPE_MY_CUSTOMER) {
    api = `/customer/businessOpportunities/executeUpdate/${id}`
  } else if (fromType === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = `/departmentCustomer/businessOpportunities/executeUpdate/${id}`
  }
  return Alova.Put(api, params)
}
type GetBusinessDetailParams = {
  /**
   * 商机ID
   */
  opportunity_id?: number
  /**
   * 场景值
   */
  scene?: string
}
/**
 * @description: 获取商机详情
 */
export function getBusinessDetail(params: GetBusinessDetailParams) {
  return Alova.Get('/customer/businessOpportunities/getDetail', { params })
}
/**
 * @description: 生成导出任务
 */
export function generateExportTask(data: RequestGenerateExportTaskType, from: number) {
  let api = ''
  if (from === FROM_TYPE_MY_CUSTOMER) {
    api = `/customer/generateTask`
  }
  if (from === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = `/departmentCustomer/generateTask`
  }
  return Alova.Post(api, data)
}
/**
 * @description: 获取导出字段
 */
export function getExportFields(from: number) {
  let api = ''
  if (from === FROM_TYPE_MY_CUSTOMER) {
    api = `/customer/getTaskFields`
  } else if (from === FROM_TYPE_DEPARTMENT_CUSTOMER) {
    api = `/departmentCustomer/getTaskFields`
  }
  return Alova.Get<ExportData>(api, {
    params: { key: 'customer' },
  })
}

/**
 * @description: 获取商机负责人列表
 */
export function getCollaboratorsList(params: any) {
  return Alova.Get<CollaboratorList[]>(`/businessOpportunities/getCollaboratorsList`, { params })
}
