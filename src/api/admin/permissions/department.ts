import { Alova } from '@/utils/http/alova/index'

// 定义部门接口
export interface Department {
  id: number
  parent_id: number
  parent_department_name?: string
  department_name: string
  status: number
  sort: number
  created_at: string
  remark: string
  creator: string
  children?: Department[] // 子部门，可选字段
  member_count?: number // 成员数量，可选字段
}

/**
 * @description: 部门列表
 */
export function getDepartmentList() {
  return Alova.Get<Department[]>(`/permissions/departments`)
}

/**
 * @description: 添加部门
 */
export function createDepartment(params) {
  return Alova.Post(`/permissions/departments`, params)
}

/**
 * @description: 更新部门
 */
export function updateDepartment(id: number, params: any) {
  return Alova.Put(`/permissions/departments/${id}`, params)
}

/**
 * @description: 删除部门
 */
export function deleteDepartment(id: number) {
  return Alova.Delete(`/permissions/departments/${id}`)
}
