import { Alova } from '@/utils/http/alova/index'
import { UserItem } from './user'

/**
 * 商城列表项
 */
export interface MallItem {
  /** 商城ID */
  id: number
  /** 商城名称 */
  name: string
  /** 商城过期时间 */
  expired_at: number
  /** 商城创建者 */
  creator: {
    /** 创建者昵称 */
    nickname: string
  }
}

/**
 * 获取商城列表
 * @param params
 * @returns
 */
export function getMallList(params: IPageRequest) {
  return Alova.Get<IPageResult<MallItem>>('/admin/mall', { params })
}

/**
 * 添加商城
 * @param params
 * @returns
 */
export function addMall(params: { name: string; admin_id: number; expired_at: number }) {
  return Alova.Post('/admin/mall', params)
}

/**
 * 编辑商城
 * @param params
 * @returns
 */
export function editMall(params: { id: number; name: string; expired_at: string }) {
  return Alova.Put(`/admin/mall/${params.id}`, params)
}

/**
 * 删除商城
 * @param id
 * @returns
 */
export function deleteMall(id: number) {
  return Alova.Delete(`/admin/mall/${id}`)
}

/**
 * 获取商城用户列表
 * @param params
 * @returns
 */
export function getMallUserList(params?: { keyword?: string }) {
  return Alova.Get<UserItem[]>('/admin/mall/user-list', { params })
}
