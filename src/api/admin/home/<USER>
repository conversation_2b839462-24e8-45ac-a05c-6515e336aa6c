import { Alova } from '@/utils/http/alova/index'

export interface NotifyItem {
  content: string
  created_at: string
  id: number
  read_status: number
  send_time: string
  sender: string
  title: string
  type: number
  updated_at: string
  user_id: number
}

/**
 * @description: 获取消息通知列表
 */
export function getInternalNotifyList(params?: any) {
  return Alova.Get<IPageResult<NotifyItem>>(`/internalNotify/list`, { params })
}

/**
 * @description: 批量标记消息为已读
 */
export function markAllNotificationRead(ids: number[]) {
  return Alova.Post(`/internalNotify/batchMarkRead`, { ids })
}

/**
 * @description: 批量删除消息
 */
export function batchDelInternalNotifyLogs(ids: number[]) {
  return Alova.Post(`/internalNotify/batchDelInternalNotifyLogs`, { ids })
}

/**
 * @description: 删除某条消息
 */
export function delInternalNotifyLogs(id: number) {
  return Alova.Delete(`/internalNotify/${id}`)
}

/**
 * @description: 获取某条通知详情
 */
export function getInternalNotifyDetail(id: number) {
  return Alova.Get<NotifyItem>(`/internalNotify/${id}`)
}
