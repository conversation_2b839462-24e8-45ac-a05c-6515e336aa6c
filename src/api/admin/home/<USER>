import { Alova } from '@/utils/http/alova/index'

export interface EmployeeInfo {
  id: number
  employee_name: string
  username: string
  email: string
  phone: string
  department_id: number
  is_principal: number
  creator_id: number
  status: number
  status_text: string
  remark: string
  updated_at: string
  department_link: string
  role_name: string
}

/**
 * @description: 获取个人信息
 */
export function getProfileInfo() {
  return Alova.Get<EmployeeInfo>(`/userCenter/getInfo`)
}

/**
 * @description: 修改基本信息
 */
export function updateInfo(params: any) {
  return Alova.Post(`/userCenter/updateInfo`, params)
}

/**
 * @description: 修改密码
 */
export function changePassword(params: any) {
  return Alova.Post(`/userCenter/changePassword`, params)
}

/**
 * @description: 重置密码
 */
export function resetPassword() {
  return Alova.Post(`/userCenter/resetPassword`)
}

/**
 * @description: 验证就密码是否正确
 */
export function checkPassword(params: any) {
  return Alova.Post(`/userCenter/checkPassword`, params)
}
