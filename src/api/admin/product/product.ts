import { Alova } from '@/utils/http/alova/index'

export interface ProductItem {
  id: number
  product_name: string
  category_id: number
  product_no: string
  is_sale: number
  product_price: string
  product_unit: number
  product_pic: string[]
  product_detail: string
  unfinish_opportunity_num: number
  created_at: string
  updated_at: string
  creator: string
  product_category: ProductCategory
  _addonProductStatus: (id: number) => Promise<void>
  quantity?: number
  price?: number
  discount?: number
  actual_price?: number
  _calculateTotalPrice?: () => number
}

export interface ProductCategory {
  id: number
  category_name: string
}

export interface ProductCategory {
  id: number
  category_name: string
}

export interface UnitOption {
  key: number
  label: string
}

export interface UnitData {
  unit_options: UnitOption[]
}

/**
 * @description: 获取产品列表
 */
export function getProductList(params: any) {
  return Alova.Get<IPageResult<ProductItem>>(`/product/product`, { params })
}

/**
 * @description: 获取单位列表
 */
export function getUnitOptions() {
  return Alova.Get<UnitData>(`/product/getProductOptions`)
}

/**
 * @description: 添加产品
 */
export function addProduct(params: any) {
  return Alova.Post(`/product/product`, params)
}

/**
 * @description: 修改产品上下架状态
 */
export function editProductStatus(id: number) {
  return Alova.Put(`/product/enable/${id}`)
}

/**
 * @description: 删除单条产品
 */
export function deleteProduct(id: number) {
  return Alova.Delete(`/product/product/${id}`)
}

/**
 * @description: 批量删除产品
 */
export function batchDeleteProduct(ids: number[]) {
  return Alova.Post(`/product/batchDel`, { ids })
}

/**
 * @description: 获取产品详情
 */
export function getProductDetail(id: number) {
  return Alova.Get<ProductItem>(`/product/product/${id}`)
}

/**
 * @description: 修改产品详情
 */
export function editProduct(id: number, params: any) {
  return Alova.Put(`/product/product/${id}`, params)
}
