import { Alova } from '@/utils/http/alova/index'

export interface CategoryItem {
  id: number
  category_name: string
  relation_num: number
  created_at: Date
  updated_at: Date
  creator: string
}

/**
 * @description: 获取分类列表
 */
export function getCategoryList(params?: any) {
  return Alova.Get<IPageResult<CategoryItem>>(`/product/category`, { params })
}

/**
 * @description: 添加分类
 */
export function createCategory(params) {
  return Alova.Post(`/product/category`, params)
}

/**
 * @description: 更新分类
 */
export function updateCategory(id: number, params: any) {
  return Alova.Put(`/product/category/${id}`, params)
}

/**
 * @description: 删除分类
 */
export function deleteCategory(id: number) {
  return Alova.Delete(`/product/category/${id}`)
}
