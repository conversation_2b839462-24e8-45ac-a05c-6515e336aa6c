import { Alova } from '@/utils/http/alova/index'
import { UserParams, UserItem, UserListRequest } from './type'

// ------------------------------订阅通知--------------------------------
/**
 * 通知列表请求参数
 */
/**
 * 获取会员通知列表
 * @param params
 * @returns
 */
export function getNoticeList(params: UserListRequest) {
  return Alova.Get<IPageResult<UserItem>>('/mall/subscribe-notice', { params })
}

/**
 * 编辑通知
 * @param params
 * @returns
 */
export function editNotice(params: UserParams) {
  return Alova.Put(`/mall/subscribe-notice/${params.notice_type}`, params)
}

/**
 * 修改通知状态
 * @param params
 * @returns
 */
export function editNoticeStatus(params: any) {
  return Alova.Put(`/mall/subscribe-notice/status`, params)
}
