export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 通知参数
 */
export interface UserParams {
  notice_type: string // 编号
  notice_type_name: string // 消息类型
  sms_template_id: string // 昵称
  sms_template_sign?: string // 密码
  sms_disabled: number // 1 超级管理员 2 管理员 3 用户
  sms_template_content: string
  wechat_template_id: string
  wechat_disabled: number
  wechat_template_content: string
}

/**
 * 通知列表
 */
export interface UserItem {
  notice_type: String
  notice_type_name: string
  sms_disabled: number
  sms_template_content: string
  sms_template_id: string
  sms_template_sign: string
  wechat_disabled: number
  wechat_template_content: string
  wechat_template_id: string
}
