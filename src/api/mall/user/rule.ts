/**
 * 标签规则
 */
export interface Rule {
  goods_condition: BaseCondition
  promotion_condition: PromotionCondition
  satisfy_condition: number
  transaction_condition: TransactionCondition
  user_condition: UserCondition
  [property: string]: any
}

/**
 * 基础条件
 */
export interface BaseCondition {
  is_selected: number
  value: number[]
  [property: string]: any
}

/**
 * 基础条件
 */
export interface BaseConditionString {
  is_selected: number
  value: string
  [property: string]: any
}
/**
 * 推广条件
 */
export interface PromotionCondition {
  channel_number: BaseConditionString
  friends_number: BaseConditionString
  shares_number: BaseConditionString
  total_expand_revenue: BaseConditionString
  [property: string]: any
}

/**
 * 交易条件
 */
export interface TransactionCondition {
  accumulated_consumption_times: BaseConditionString
  return_frequency: BaseConditionString
  total_consumption_amount: BaseConditionString
  [property: string]: any
}

/**
 * 用户条件
 */
export interface UserCondition {
  channel_weight: BaseCondition
  user_level: BaseCondition
  visits_platform_number: BaseConditionString
  year_birth: BaseConditionString
  [property: string]: any
}
