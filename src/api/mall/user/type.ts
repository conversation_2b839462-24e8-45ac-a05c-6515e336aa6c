import type { Rule } from './rule'

export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 用户参数
 */
export interface UserParams {
  /**
   * 会员权总
   */
  level?: number
  /**
   * 手机号
   */
  mobile: string
  /**
   * 昵称
   */
  nickname: string
  /**
   * 父级ID 平台0
   */
  parent_id: number
  /**
   * 密码
   */
  password?: string
  /**
   * 真实姓名
   */
  realname?: string
  /**
   * 标签ID
   */
  tag_ids?: number[]
  [property: string]: any
}

/**
 * 会员关联的标签信息
 */
export interface UserTag {
  /**
   * 标签id
   */
  id: number
  /**
   * 标签名称
   */
  name: string
  /**
   * 标签组id
   */
  tag_group_id: number
  /**
   * 标签组信息
   */
  userTagGroup: UserTagGroup
  [property: string]: any
}

/**
 * 标签组信息
 */
export interface UserTagGroup {
  /**
   * 标签组id
   */
  id: number
  /**
   * 标签组名称
   */
  name: string
  [property: string]: any
}

/**
 * 钱包列表
 */
export interface WalletItem {
  balance: number
  score: number
}

export interface UserDetail {
  /**
   * 头像
   */
  avatar_url: string
  /**
   * 余额
   */
  balance: number
  /**
   * 注册时间
   */
  created_at: number
  /**
   * 设备MAC地址
   */
  device_mac: string
  /**
   * 登录设备
   */
  device_type: string
  id: number
  /**
   * 是否关注公众号，1:已关注；0：未关注
   */
  is_follow: number
  /**
   * 推广资格，1：有；0：无
   */
  is_inviter: number
  /**
   * 最近活跃
   */
  last_login_at: number
  /**
   * 会员等级
   */
  level: number
  /**
   * 会员等级名称
   */
  level_name: string
  /**
   * 手机号
   */
  mobile: number
  /**
   * 昵称
   */
  nickname: string
  /**
   * 累计消费总额
   */
  pay_money: number
  /**
   * 总订单数
   */
  pay_num: number
  /**
   * 来源渠道
   */
  platform: string
  /**
   * 积分
   */
  score: number
  /**
   * 来源方式
   */
  source: string
  /**
   * 所属门店id
   */
  store_id: number
  /**
   * 默认收货地址
   */
  user_default_address: string
  /**
   * 会员扩展信息
   */
  user_extend: UserExtend
  /**
   * 真实姓名
   */
  username: string
  [property: string]: any
}

/**
 * 会员扩展信息
 */
export interface UserExtend {
  /**
   * 区
   */
  area: string
  area_id: number
  /**
   * 市
   */
  city: string
  city_id: number
  /**
   * 邀请码
   */
  code: string
  id: number
  /**
   * 身份证号码
   */
  id_card: string
  /**
   * 省
   */
  province: string
  province_id: number
  /**
   * 性别，0：未知；1：男；2：女
   */
  sex: number
  /**
   * 身份过期时间：0代表永久
   */
  user_level_expiration: number
  [property: string]: any
}

/**
 * 用户列表
 */
export interface UserItem {
  id: number
  username: string
  nickname: string
  admin_type: number
  status: number
  created_at: string
  updated_at: string
}

/**
 * 商城列表项
 */
export interface mallItem {
  id: number
  name: string
  expired_at: number
  creator: {
    nickname: string
  }
}

/**
 * 会员等级列表项
 */
export interface LevelItem {
  created_at?: number
  deleted_at?: null
  /**
   * 折扣
   */
  discount?: number
  /**
   * 有效期天数
   */
  expired_day?: number
  id?: number
  /**
   * 是否开启会员期限，0不开启，1开启
   */
  is_open_expired?: number
  /**
   * 开启指定等级会员价展示，0不开启，1开启
   */
  is_specified_level?: number
  /**
   * 等级权重
   */
  level: number
  /**
   * 商城ID
   */
  mall_id?: number
  /**
   * 是否展示下级会员价格，0 不展示 ，1展示
   */
  member_price_is_show?: number
  /**
   * 等级名称
   */
  name: string
  /**
   * 会员图标
   */
  pic_url?: string
  /**
   * 指定等级
   */
  specified_level?: string
  /**
   * 状态
   */
  status?: number
  updated_at?: number
  /**
   * 会员数量
   */
  user_num?: number
  [property: string]: any
}

/**
 * 等级权重列表
 */
export interface LevelList {
  /**
   * 是否禁用，0未禁用，1禁用
   */
  disabled: boolean
  /**
   * 权重值
   */
  level: number
  /**
   * 权重名称
   */
  name: string
  [property: string]: any
}

export interface levelData {
  /**
   * 购买商品升级场景类型，0订单完成时，1订单支付时
   */
  buy_goods_type: number
  /**
   * 选中的升级条件ID集合
   */
  checked_condition_keys: number[]
  /**
   * 升级条件原生数据
   */
  checked_condition_values: CheckedConditionValues
  /**
   * 条件升级条件数据
   */
  condition_data: ConditionDatum[]
  /**
   * 条件升级类型，0 未选择、1满足其一  2、满足所有
   */
  condition_type: number

  /**
   * 折扣
   */
  discount: number
  /**
   * 有效期天数
   */
  expired_day: number
  /**
   * 升级有效商品ID集合
   */
  goods_ids: number[]
  /**
   * 商品升级类型，1- 任意商品 2- 指定商品
   */
  goods_type: number
  /**
   * 等级ID
   */
  id?: number
  /**
   * 是否开启自动升级，0不开启，1开启
   */
  is_auto_upgrade: number
  /**
   * 是否开启会员期限
   */
  is_open_expired: number
  /**
   * 是否开启指定等级会员价展示，0不开启，1开启
   */
  is_specified_level: number
  /**
   * 等级权重
   */
  level: number | null
  /**
   * 等级权重列表
   */
  level_list?: LevelList[]

  /**
   * 是否展示下级会员价格，0不展示，1展示
   */
  member_price_is_show: number
  /**
   * 等级名称
   */
  name: string
  /**
   * 等级图标
   */
  pic_url: string
  /**
   * 指定等级权重
   */
  specified_level: number[]
  /**
   * 状态
   */
  status?: number
  /**
   * 是否开启条件升级，0不开启，1开启
   */
  upgrade_type_condition: number
  /**
   * 是否开启购买商品升级，0不开启，1开启
   */
  upgrade_type_goods: number
  [property: string]: any
}
/**
 * 升级条件原生数据
 */
export interface CheckedConditionValues {
  [property: string]: {
    [property: string]: any
  }
}

export interface ConditionDatum {
  /**
   * 条件ID
   */
  id: number
  /**
   * 是否选择，0未选择，1选择
   */
  is_selected: number
  /**
   * 条件配置
   */
  setting: Setting[]
  /**
   * 条件标题
   */
  title: string
  [property: string]: any
}

export interface Setting {
  /**
   * 默认值
   */
  default_val: number
  /**
   * 配置名称
   */
  name: string
  /**
   * 配置源数据，一般非文本框时需要
   */
  source_data?: SourceDatum[]
  /**
   * 配置类型
   */
  type: string
  [property: string]: any
}

export interface SourceDatum {
  /**
   * 数据标题
   */
  label: string
  /**
   * 数据值
   */
  value: number
  [property: string]: any
}

/**
 * 初始化会员等级
 * @returns
 */
export interface InitLevelDate {
  /**
   * 等级条件数据
   */
  condition_data: ConditionDatum[]
  /**
   * 当前默认权重，初始化时为存在最大等级权重的下一权重
   */
  level: number
  /**
   * 权重列表
   */
  level_list: LevelList[]
  [property: string]: any
}
export interface Data {}

// ------------------------------标签组--------------------------------

/**
 * 标签组数据
 */
export interface TagGroupData {
  add_rule: number
  created_at?: number
  deleted_at?: null
  id?: number
  mall_id?: number
  name: string
  status: number
  tag: TagItem[]
  updated_at?: number
  [property: string]: any
}

/**
 * 标签列表项
 */
export interface TagItem {
  id?: number
  is_delete_auto?: number
  name?: string
  rule?: Rule
  status?: number
  tag_group_id?: number
  users_number?: number
  [property: string]: any
}
