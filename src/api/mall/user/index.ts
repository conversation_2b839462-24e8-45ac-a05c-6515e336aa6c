import { Alova } from '@/utils/http/alova/index'
import {
  UserParams,
  mallItem,
  TagGroupData,
  LevelItem,
  InitLevelDate,
  UserItem,
  levelData,
  ConditionDatum,
  WalletItem,
  UserListRequest,
  UserTag,
  UserDetail,
} from './type'

import { LinkSetting } from './link'
// ------------------------------用户--------------------------------
/**
 * 用户列表请求参数
 */
/**
 * 获取用户列表
 * @param params
 * @returns
 */
export function getUserList(params: UserListRequest) {
  return Alova.Get<IPageResult<UserItem>>('/mall/user', { params })
}

/**
 * 添加用户
 * @param params
 * @returns
 */
export function addUser(params: UserParams) {
  return Alova.Post('/mall/user', params)
}

/**
 * 编辑用户
 * @param params
 * @returns
 */
export function editUser(params: UserParams) {
  return Alova.Put(`/mall/user/${params.id}`, params)
}

/**
 * 获取用户详情
 * @param id
 * @returns
 */
export function getUserDetail(id: number) {
  return Alova.Get<UserDetail>(`/mall/user/${id}`)
}

/**
 * 删除用户
 * @param id
 * @returns
 */
export function deleteUser(id: number) {
  return Alova.Delete(`/mall/user/${id}`)
}

/**
 * 设置用户为黑名单
 * @param id
 * @returns
 */
export function setUserBlack(id: number) {
  return Alova.Get(`/mall/user/set-black`, {
    params: {
      id,
    },
  })
}

/**
 * 重置用户密码
 * @param id
 * @returns
 */
export function resetUserPassword(params: { id: number; password: string }) {
  return Alova.Post(`/mall/user/password?id=${params.id}`, {
    password: params.password,
  })
}

/**
 * 设置用户标签
 * @param id
 * @param tag_id
 * @returns
 */
export function setUserTag(params: { user_id: number; tag_ids: number[] }) {
  return Alova.Post(`/mall/user/add-tag`, params)
}

/**
 * 获取会员关联的标签信息
 * @param id
 * @returns
 */
export function getUserTag(id: number) {
  return Alova.Get<UserTag[]>(`/mall/user/get-user-tag`, { params: { id } })
}

/**
 * 会员批量导出
 * @param params
 * @returns
 */
export function exportUserList(params: IPageRequest) {
  return Alova.Get(`/mall/user/index`, { params })
}

/**
 * 会员钱包
 * @param id
 * @returns
 */
export function getUserWalletList(id: number) {
  return Alova.Get<WalletItem>(`/mall/user/wallet`, {
    params: {
      id,
    },
  })
}

/**
 * 业务员列表（新增会员/编辑会员使用）
 * @param id
 * @param params
 * @returns
 */
export function getSalesmanList(params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/salesman-list`, { params })
}

/**
 * TODO: 会员订单列表（消费列表）
 * @param id
 * @returns
 */
export function getUserOrderList(id: number, params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/order`, { params: { id, ...params } })
}

/**
 * TODO: 会员积分明细
 * @param id
 * @returns
 */
export function getUserScoreList(id: number, params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/score`, { params: { id, ...params } })
}

/**
 * TODO: 会员余额明细
 * @param id
 * @returns
 */
export function getUserBalanceList(id: number, params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/balance`, { params: { id, ...params } })
}

/**
 * TODO: 好友关系列表
 * @param id
 * @returns
 */
export function getUserFriendList(id: number, params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/friend`, { params: { id, ...params } })
}

/**
 * TODO:推荐人变更记录
 * @param id
 * @returns
 */
export function getUserRecommendRecordList(id: number, params: IPageRequest) {
  return Alova.Get<IPageResult<any>>(`/mall/user/parent-change`, { params: { id, ...params } })
}

// ------------------------------end--用户--------------------------------

/**
 * 获取用户商城列表
 * @param id
 * @returns
 */
export function getUserMallList(params: { id: number }) {
  return Alova.Get<IPageResult<mallItem>>(`/mall/user/mall`, { params })
}

// ------------------------------标签组--------------------------------
/**
 * 获取标签组列表
 * @returns
 */
export function getTagGroupList(params?: IPageRequest) {
  return Alova.Get<IPageResult<TagGroupData>>(`/mall/tag-group`, { params })
}

/**
 * 添加标签组
 * @param params
 * @returns
 */
export function addTagGroup(params: TagGroupData) {
  return Alova.Post('/mall/tag-group', params)
}

/**
 * 编辑标签组
 * @param params
 * @returns
 */
export function editTagGroup(params: TagGroupData) {
  return Alova.Put(`/mall/tag-group/${params.id}`, params)
}

/**
 * 获取标签组详情
 * @param id
 * @returns
 */
export function getTagGroupDetail(id: number) {
  return Alova.Get<TagGroupData>(`/mall/tag-group/${id}`)
}

/**
 * 设置标签组状态
 * @param id
 * @param status
 * @returns
 */
export function updateTagGroupStatus(id: number, status: number) {
  return Alova.Post(
    `/mall/tag-group/update-status`,
    { status },
    {
      params: {
        id,
      },
    },
  )
}

/**
 * 删除标签组
 * @param id
 * @returns
 */
export function deleteTagGroup(id: number) {
  return Alova.Delete(`/mall/tag-group/${id}`)
}
// ------------------------------end--标签组--------------------------------

/**
 * 获取会员等级列表
 * @returns
 */
export function getLevelList(params?: IPageRequest) {
  return Alova.Get<IPageResult<LevelItem>>(`/mall/user-level`, { params })
}

/**
 * 初始化会员等级
 * @returns
 */
export function initLevel() {
  return Alova.Post<InitLevelDate>('/mall/user-level/init')
}

/**
 * 添加会员等级
 * @param params
 */
export function addLevel(params: levelData) {
  return Alova.Post('/mall/user-level', params)
}

/**
 * 编辑会员等级
 * @param params
 */
export function editLevel(params: levelData) {
  return Alova.Put(`/mall/user-level/${params.id}`, params)
}

/**
 * 编辑会员等级
 * @param params
 */
export function deleteLevel(id: number) {
  return Alova.Delete(`/mall/user-level/${id}`)
}

/**
 * 获取会员等级详情
 * @param id
 * @returns
 */
export function getLevelDetail(id: number) {
  return Alova.Get<levelData>(`/mall/user-level/${id}`)
}

/**
 * 设置会员等级状态
 * @param id
 * @param status
 * @returns
 */
export function updateLevelStatus(id: number) {
  return Alova.Put(`/mall/user-level/change-status?id=${id}`)
}
// ------------------------------end--会员等级--------------------------------

// ------------------------------会员升级条件--------------------------------
/**
 * 获取会员等级升级条件列表
 * @returns
 */
export function getConditionList(params: IPageRequest) {
  return Alova.Get<IPageResult<ConditionDatum>>('/mall/user-level/get-condition', { params })
}

/**
 *  添加会员升级条件
 * @param params
 * @returns
 */
export function addCondition(ids: number[]) {
  return Alova.Post('/mall/user-level/add-condition', {
    ids,
  })
}

/**
 * 更新会员升级条件(勾选形式)
 * @param params
 * @returns
 */
export function updatePracticalCondition(ids: number[]) {
  return Alova.Post('/mall/user-level/update-practical-condition', {
    ids,
  })
}

/**
 * 更新会员升级条件（开关形式）
 * @param params
 * @returns
 */
export function changeConditionPractical(params: { id: number; opt: number }) {
  return Alova.Post('/mall/user-level/change-condition-practical', params)
}

/**
 * 删除会员升级条件
 * @param id
 * @returns
 */
export function deleteCondition(id: number) {
  return Alova.Delete(`/mall/user-level/del-condition?id=${id}`)
}

/**
 * 可添加的会员升级条件
 * @returns
 */
export function getAvailableCondition(params: IPageRequest) {
  return Alova.Get<IPageResult<ConditionDatum>>('/mall/user-level/get-vailable-condition', {
    params,
  })
}

// ------------------------------end--会员升级条件--------------------------------

// ------------------------------链接设置--------------------------------
/**
 * 获取链接设置列表
 * @returns
 */
export function getLinkSettingsList() {
  return Alova.Get<LinkSetting>('/mall/link-settings/index')
}

/**
 * 添加链接设置
 * @param params
 * @returns
 */
export function setLinkSettings(params: LinkSetting) {
  return Alova.Post('/mall/link-settings/index', params)
}
// ------------------------------end--链接设置--------------------------------
