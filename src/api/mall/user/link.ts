import type { LinkGoodsList, LinkCatList } from '@/components/SelectLink'
/**
 * 链接设置
 */
export interface LinkSetting {
  /**
   * 购买商品开关：0:否；1:是
   */
  buy_goods_selected: number
  /**
   * 1：任意商品；2：指定商品；3：指定分类
   */
  buy_goods_way: number
  /**
   * 消费次数
   */
  buy_num: number
  /**
   * 消费次数开关：0:否；1:是
   */
  buy_num_selected: number
  /**
   * 消费金额
   */
  buy_price: number
  /**
   * 消费金额开关：0:否；1:是
   */
  buy_price_selected: number
  /**
   * 分类id
   */
  cat_ids: string[]
  cat_list?: LinkCatList[]
  /**
   * 权利获取条件：1：无；2：或；3：与
   */
  get_power_way: number
  /**
   * 商品id
   */
  goods_ids: string[]
  /**
   * 商品信息列表
   */
  goods_list?: LinkGoodsList[]
  /**
   * 链接时效性设置
   */
  link_timeliness_setting: MiniProgramLinkTimelinessSetting
  /**
   * 推广方式
   */
  promotion_methods: MiniProgramPromotionMethods
  /**
   * 是否启用关系链:0:否；1:是
   */
  status: number
  [property: string]: any
}

export interface H5LinkTimelinessSetting {
  status: number
  value: number
  [property: string]: any
}

export interface H5PromotionMethods {
  invitation_code_binding: number
  link_sharing: PurpleLinkSharing
  promote_mobile: number
  [property: string]: any
}

export interface PurpleLinkSharing {
  status: number
  value: number
  [property: string]: any
}

/**
 * 链接时效性设置
 */
export interface MiniProgramLinkTimelinessSetting {
  /**
   * 开关：0：否；1：是
   */
  status: number
  /**
   * 链接生成后X天
   */
  value: number
  [property: string]: any
}

/**
 * 推广方式
 */
export interface MiniProgramPromotionMethods {
  /**
   * 邀请码绑定开关：0：否；1：是
   */
  invitation_code_binding: number
  /**
   * 连接分享
   */
  link_sharing: FluffyLinkSharing
  /**
   * 填写推广手机号开关：0：否；1：是
   */
  promote_mobile: number
  [property: string]: any
}

/**
 * 连接分享
 */
export interface FluffyLinkSharing {
  /**
   * 开关：0：否；1：是
   */
  status: number
  /**
   * 1：首次点击分享海报；2：首次完成订单；3：首次付款
   */
  value: number
  [property: string]: any
}
