import { Alova } from '@/utils/http/alova/index'

// 商品分类级别类型
export type GoodsCateLevel = 1 | 2 | 3

// 商品分类状态类型
export type GoodsCateStatus = 0 | 1

// 商品分类信息
export interface GoodsCateInfo {
  /**
   * 创建时间
   */
  created_at: number
  id: number
  /**
   * 名称
   */
  name: string
  /**
   * 父ID
   */
  parent_id: number
  /**
   * 分类图
   */
  pic: string
  /**
   * 排序
   */
  sort: number
  /**
   * 状态（1开启 0 禁用）
   */
  status: number
  /**
   * 子分类
   */
  children?: GoodsCateInfo[]
  updated_at: number
  [property: string]: any
}

// 商品分类树
export interface GoodsCateTree {
  id: number
  level: GoodsCateLevel
  parent_id: number
  name: string
  children?: GoodsCateTree[]
}

// 商品分类基础数据
export interface GoodsCateBase {
  level: GoodsCateLevel
  parent_id: number
  name: string
  pic: string
  status: GoodsCateStatus
  sort: number
}

// 商品分类创建请求
export type GoodsCateCreateRequest = GoodsCateBase

// 商品分类更新请求
export type GoodsCateUpdateRequest = GoodsCateBase

// 商品标签信息
export interface GoodsTagInfo {
  /**
   * 背景颜色
   */
  bg_color?: string
  created_at?: number
  /**
   * 字体颜色
   */
  font_color?: string
  id?: number
  /**
   * 名称
   */
  name?: string
  /**
   * 排序
   */
  sort?: number
  /**
   * 状态
   */
  status?: number
  [property: string]: any
}

// 商品标签状态类型
export type GoodsTagStatus = 0 | 1

// 商品标签基础数据
export interface GoodsTagBase {
  name: string
  font_color: string
  bg_color: string
  status: GoodsTagStatus
  sort: number
}

/**
 * @description: 获取商品分类列表
 */
export function getGoodsCateList(params: IPageRequest) {
  return Alova.Get<IPageResult<GoodsCateInfo>>('/mall/goods-cate', { params })
}

/**
 * @description: 获取商品分类树
 */
export function getGoodsCateTree() {
  return Alova.Get<GoodsCateTree[]>('/mall/goods-cate/tree')
}

/**
 * @description: 添加商品分类
 */
export function addGoodsCate(data: GoodsCateCreateRequest) {
  return Alova.Post<void>('/mall/goods-cate', data)
}

/**
 * @description: 编辑商品分类
 */
export function editGoodsCate(id: number, data: GoodsCateUpdateRequest) {
  return Alova.Put<void>(`/mall/goods-cate/${id}`, data)
}

/**
 * @description: 更新商品分类状态
 */
export function updateGoodsCateStatus(id: number) {
  return Alova.Get<void>(`/mall/goods-cate/change-status/${id}`)
}

/**
 * @description: 删除商品分类
 */
export function deleteGoodsCate(id: number) {
  return Alova.Delete<void>(`/mall/goods-cate/${id}`)
}

/**
 * @description: 获取商品标签列表
 */
export function getGoodsTagList(params: IPageRequest) {
  return Alova.Get<IPageResult<GoodsTagInfo>>('/mall/goods-tag', { params })
}

/**
 * @description: 添加商品标签
 */
export function addGoodsTag(data: GoodsTagBase) {
  return Alova.Post<void>('/mall/goods-tag', data)
}

/**
 * @description: 编辑商品标签
 */
export function editGoodsTag(id: number, data: GoodsTagBase) {
  return Alova.Put<void>(`/mall/goods-tag/${id}`, data)
}

/**
 * @description: 删除商品标签
 */
export function deleteGoodsTag(id: number) {
  return Alova.Delete<void>(`/mall/goods-tag/${id}`)
}

/**
 * @description: 更新商品标签状态
 */
export function updateGoodsTagStatus(id: number) {
  return Alova.Get<void>(`/mall/goods-tag/change-status/${id}`)
}
