// 定义可能存在的子对象结构
interface BackendUrlObj {
  backend_url: string
}

export interface UrlObj {
  url: string
}

// 渠道类型
export interface Channel {
  [key: string]: BackendUrlObj | UrlObj | string | null
}

/**
 * 小程序发布审核列表
 */
export interface MiniProgramPublishAudit {
  /**
   * api接口域名
   */
  api_domain?: string
  /**
   * app_id
   */
  app_id?: string
  /**
   * 描述
   */
  desc?: string
  id?: number
  /**
   * 审核状态【1：已审核；0：待审核】
   */
  is_review?: number
  /**
   * 商城签名
   */
  mall_sign?: string
  /**
   * 资源域名
   */
  static_domain?: string
  /**
   * 执行状态【1：已处理；2：处理异常；0：待处理】
   */
  status?: number
  /**
   * 修改时间
   */
  updated_at?: number
  /**
   * 版本号
   */
  version?: string
  [property: string]: any
}

/** 小程序基础配置 */
export interface MiniProgramBaseConfig {
  /**
   * 名称
   */
  name: string
  /**
   * app_id
   */
  app_id: string
  /**
   * 小程序原始ID
   */
  original_id: string
  /**
   * 小程序secret
   */
  secret: string
  /**
   * 小程序上传密钥
   */
  upload_secret_key: string
}
/**
 * 小程序配置
 */
export interface MiniProgramConfig extends MiniProgramBaseConfig {
  addons_list: string[]
  /**
   * api域名
   */
  api_domain: string

  /**
   * 描述
   */
  desc: string
  is_docker_deploy: number

  /**
   * 小程序secret_key_file
   */
  secret_key_file: string
  /**
   * 资源域名
   */
  static_domain: string

  /**
   * 版本号
   */
  version: string
  [property: string]: any
}
/**
 * 公众号配置
 */
export interface WechatOfficialAccountConfig {
  /**
   * 消息加解密密钥(EncodingAESKey)
   */
  aes_key?: string
  /**
   * 公众号appid
   */
  app_id?: string
  /**
   * 公众号h5_url
   */
  h5_url?: string
  /**
   * 公众号名称
   */
  name?: string
  /**
   * 公众号二维码
   */
  qrcode?: string
  /**
   * 公众号secret
   */
  secret?: string
  /**
   * 公众号token
   */
  token?: string
  /**
   * 公众号web_url
   */
  web_url?: string
  /**
   * 公众号wechat_js
   */
  wechat_js?: string
  /**
   * 公众号ssl_path
   */
  ssl_path?: string
  /**
   * 公众号clearCache
   */
  clearCache?: string
  /**
   * 公众号h5_link
   */
  h5_link?: string
  [property: string]: any
}

/**
 * 自动回复搜索参数
 */
export interface SearchParams {
  /**
   * 消息类型，0关键词回复，1关注回复
   */
  is_follow_reply?: number
  /**
   * 规则名称
   */
  name?: string

  /**
   * 回复类型，text文本，image图片，video视频，voice音频，article图文
   */
  reply_type?: string
  /**
   * 状态，0禁用，1正常
   */
  status?: number
  [property: string]: any
}

/**
 * 自动回复列表
 */
export interface WechatReplyRule {
  id: number
  reply_type: string
  rule_name: string
  reply_format: string
  keywords: string
  status: number
  update_time: string
  match_keywords: string[]
  include_keywords: string[]
}

/**
 * 素材列表
 */
export interface WechatMaterial {
  id: number
  media_id: string
  title: string
  type: string
  update_time: string
}

/**
 *  公众号自定义菜单
 */

export interface WechatMenu {
  /**
   * 菜单名称
   */
  name: string
  /**
   * 菜单类型
   * article_id 图文
   * click 关键字
   * view 跳转链接
   * miniprogram 小程序
   */
  type: string
  /**
   * 素材id
   */
  article_id?: number
  /**
   * 跳转链接url
   */
  url?: string
  /**
   * 小程序页面路径
   */
  pagepath?: string
  /**
   * 小程序appid
   */
  appid?: string

  /**
   * 素材id
   */
  media_id?: string
  [property: string]: any
}

/**公众号自定义菜单 */
export interface WechatMenuButton extends WechatMenu {
  /**
   * 子菜单
   */
  sub_button: WechatMenu[]
  [property: string]: any
}
