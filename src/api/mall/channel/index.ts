import { Alova } from '@/utils/http/alova/index'
import {
  SearchParams,
  WechatReplyRule,
  MiniProgramConfig,
  MiniProgramPublishAudit,
  WechatMaterial,
  WechatMenuButton,
  WechatOfficialAccountConfig,
  Channel,
  MiniProgramBaseConfig,
} from './type'

/**
 * 渠道管理列表
 * @param params
 * @returns
 */
export function getChannelList() {
  return Alova.Get<Channel>(`/mall/channel`)
}

/**
 * 获取小程序配置
 * @param id
 * @returns
 */
export function getMiniProgram() {
  return Alova.Get<MiniProgramBaseConfig>(`/mall/mini-program/get-setting`)
}

/**
 * 更新小程序配置
 * @param id
 * @param data
 * @returns
 */
export function setMiniProgramConfig(params: MiniProgramBaseConfig) {
  return Alova.Put(`/mall/mini-program/get-setting`, params)
}

/**
 * 小程序授权清除
 * @returns
 */
export function clearMiniProgramAuth() {
  return Alova.Delete(`/mall/mini-program/clear-auth`)
}

/**
 * 小程序发布审核列表
 * @param id
 * @returns
 */
export function getMiniProgramPublishAuditList(params: IPageRequest) {
  return Alova.Get<IPageResult<MiniProgramPublishAudit>>(`/mall/mini-program`, {
    params,
  })
}
/**
 * 获取小程序配置
 * @param id
 * @returns
 */
export function getMiniProgramConfig() {
  return Alova.Get<MiniProgramConfig>(`/mall/mini-program/get-mini-program-config`)
}
/**
 * 提交小程序上传任务
 * @param id
 * @returns
 */
export function submitMiniProgramUploadTask(params: MiniProgramConfig) {
  return Alova.Post(`/mall/mini-program/upload`, params)
}

/** 审核状态修改 */
export function changeMiniProgramReviewStatus(id: number) {
  return Alova.Get(`/mall/mini-program/task-change-review`, {
    params: {
      id,
    },
  })
}
/**
 * 执行日志
 * @param id
 * @returns
 */
export function getMiniProgramTaskLog(id: number) {
  return Alova.Get<{ output: string }>(`/mall/mini-program/task-log`, {
    params: {
      id,
    },
  })
}
/**
 * 获取公众号基础配置
 * @param id
 * @returns
 */
export function getWechatOfficialAccountConfig() {
  return Alova.Get<WechatOfficialAccountConfig>(`/mall/wechat`)
}

/**
 * 更新公众号基础配置
 * @param id
 * @returns
 */
export function setWechatOfficialAccountConfig(params: WechatOfficialAccountConfig) {
  return Alova.Put(`/mall/wechat/edit`, params)
}

/**
 * 清除公众号授权
 * @param id 用户User_ID，未传时清除全部用户公众号授权
 * @returns
 */
export function clearWechatOfficialAccountAuth(id?: number) {
  return Alova.Delete(id ? `/mall/wechat/clear-auth/${id}` : `/mall/wechat/clear-auth`)
}

/**
 * 获取自定义菜单
 * @returns
 */
export function getWechatMenu() {
  return Alova.Get<WechatMenuButton[]>(`/mall/wechat-menu`)
}

/**
 * 更新自定义菜单
 * @param id
 * @param data
 * @returns
 */
export function updateWechatMenu(params: WechatMenuButton[]) {
  return Alova.Put(`/mall/wechat-menu/update`, {
    buttons: params,
  })
}

/**
 * 自动回复列表
 * @returns
 */
export function getWechatReplyRuleList(params: SearchParams) {
  return Alova.Get<IPageResult<WechatReplyRule>>(`/mall/wechat-replyrule`, { params })
}

/**
 * 自动回复添加
 * @param data
 * @returns
 */
export function addWechatReplyRule(params: WechatReplyRule) {
  return Alova.Post(`/mall/wechat-replyrule`, params)
}

/**
 * 自动回复更新
 * @param id
 * @param data
 * @returns
 */
export function updateWechatReplyRule(id: number, params: WechatReplyRule) {
  return Alova.Put(`/mall/wechat-replyrule/${id}`, params)
}

/**
 * 自动回复删除
 * @param id
 * @returns
 */
export function deleteWechatReplyRule(id: number) {
  return Alova.Delete(`/mall/wechat-replyrule/${id}`)
}

/**
 * 自动回复状态更新
 * @param id
 * @returns
 */
export function changeWechatReplyRuleStatus({ id, status }: { id: number; status: number }) {
  return Alova.Put(`/mall/wechat-replyrule/status?id=${id}`, { status: status })
}
/**
 * 素材列表
 * @param params
 * @returns
 */
export function getWechatMaterialList(params: SearchParams) {
  return Alova.Get<IPageResult<WechatMaterial>>(`/mall/wechat-material`, { params })
}

/**
 * 素材添加
 * @param data
 * @returns
 */
export function addWechatMaterial(data: WechatMaterial) {
  return Alova.Post(`/mall/wechat-material`, data)
}

/**
 * 素材详情
 * @param id
 * @returns
 */
export function getWechatMaterialDetail(id: number) {
  return Alova.Get<WechatMaterial>(`/mall/wechat-material/${id}`)
}

/**
 * 素材删除
 * @param id
 * @returns
 */
export function deleteWechatMaterial(id: number) {
  return Alova.Delete(`/mall/wechat-material/${id}`)
}
