import { Alova } from '@/utils/http/alova/index'
import type { downLoadResult, downLoadItem } from './type'
/**
 * 获取下载列表
 * @param params
 * @returns
 */
export function getDownloadList(params: IPageRequest) {
  return Alova.Get<downLoadResult<downLoadItem>>(`/mall/download`, { params })
}

/**
 * 删除下载
 * @param id
 * @returns
 */
export function deleteDownload(id: number) {
  return Alova.Delete(`/mall/download/${id}`)
}

/**
 * 文件下载
 * @param id
 * @returns
 */
export function downloadFile(id: number) {
  return Alova.Get(`/mall/download/down`, { params: { id } })
}

/**
 * 批量下载
 * @param params
 * @returns
 */
export function multipleDownload(params: any) {
  return Alova.Post(`/mall/download/multiple-down`, params)
}
