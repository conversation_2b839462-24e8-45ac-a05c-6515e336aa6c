/**
 * 下载列表项
 */
export interface downLoadItem {
  created_at: number
  deleted_at: null
  error_msg: null | string
  handler_class: string
  id: number
  mall_id: number
  method: string
  /**
   * 文件名称
   */
  name: string
  /**
   * 数据量
   */
  num: number
  params: null | string
  /**
   * 文件大小
   */
  size: string
  /**
   * 状态
   */
  status: number
  store_id: number
  /**
   * 类型
   */
  type: number
  updated_at: number
  url: string
  [property: string]: any
}
/**
 * 下载列表结果
 */
export interface downLoadResult<T> extends IPageResult<T> {
  /**
   * 类型列表
   */
  type_list: {
    [key: string]: string
  }
}
