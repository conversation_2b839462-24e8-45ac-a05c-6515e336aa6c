/**
 * 门店列表
 */
export interface StoreList {
  /**
   * 门店地址-详细地址
   */
  address?: string
  /**
   * 门店余额
   */
  balance?: number
  /**
   * 营业状态，0未营业，1营业中
   */
  business_status?: number
  /**
   * 营业结束时间
   */
  business_time_end?: string
  /**
   * 营业开始时间
   */
  business_time_start?: string
  /**
   * 门店分类信息
   */
  cate?: Cate
  /**
   * 门店分类ID
   */
  cate_id?: number
  /**
   * 门店地址-市级ID
   */
  city_id?: number
  /**
   * 门店联系方式
   */
  contact?: number
  created_at?: number
  deleted_at?: null
  /**
   * 门店地址-区级ID
   */
  district_id?: number
  id?: number
  /**
   * 纬度坐标
   */
  latitude?: number
  /**
   * 营业执照
   */
  license_img?: string
  /**
   * 门店定位地址
   */
  location_address?: string
  /**
   * 门店logo
   */
  logo_img?: string
  /**
   * 经度坐标
   */
  longitude?: number
  mall_id?: number
  /**
   * 门店地址，省份ID
   */
  province_id?: number
  /**
   * 门店统计信息
   */
  statistic?: Statistic
  status?: number
  /**
   * 门店简介
   */
  store_desc?: string
  /**
   * 门店名称
   */
  store_name?: string
  /**
   * 门店经营类型，自营(self_operated)/加盟(franchise)
   */
  store_type?: string
  updated_at?: number
  /**
   * 店长/负责人信息
   */
  user?: User
  user_id?: number
  [property: string]: any
}

/**
 * 门店分类信息
 */
export interface Cate {
  id: number
  name: string
  [property: string]: any
}

/**
 * 门店统计信息
 */
export interface Statistic {
  created_at: number
  deleted_at: null
  id: number
  mall_id: number
  store_id: number
  total_expenses: number
  total_money: number
  updated_at: number
  user_nums: number
  [property: string]: any
}

/**
 * 店长/负责人信息
 */
export interface User {
  avatar_url: string
  id: number
  mall_id: number
  mobile: number
  nickname: string
  [property: string]: any
}

export interface Pagination {
  defaultPageSize: number
  forcePageParam: boolean
  pageParam: string
  pageSizeLimit: number[]
  pageSizeParam: string
  params: null
  route: null
  totalCount: number
  urlManager: null
  validatePage: boolean
  [property: string]: any
}
