import { Alova } from '@/utils/http/alova/index'
import {
  orderParams,
  orderItem,
  RemarkItem,
  shipParams,
  OrderDictionaries,
  remarkParams,
  OrderDetail,
  SiteParamsType,
  AfterSalesQuery,
  AfterSalesItem,
  ExpressItem,
  AfterSalesDict,
  AfterSalesDetail,
  reShipParams,
} from './type'
/**
 * 获取订单列表
 * @param params
 * @returns
 */
export function getOrderList(params: orderParams) {
  return Alova.Get<IPageResult<orderItem>>('/mall/order', { params })
}
/**
 * 订单字典
 * @param params
 * @returns
 */
export function getOrderDict() {
  return Alova.Get<OrderDictionaries>('/mall/order/dictionaries')
}
/**
 * 快递公司列表
 */
export function getExpressList(params) {
  return Alova.Get<IPageResult<ExpressItem>>('/common/express/index', { params })
}
/**
 * 订单发货
 * @param params
 * @returns
 */
export function orderShip(params: shipParams, id?: number) {
  return Alova.Post(`/mall/order/ship?id=${id}`, params)
}
/**
 * 确认订单收货
 * @param params
 * @returns
 */
export function confirmDelivery(params: { id: string | number }) {
  return Alova.Get('/mall/order/confirm-delivery', { params })
}
/**
 * 订单完成
 * @param params
 * @returns
 */
export function orderComplete(params: { id: string | number }) {
  return Alova.Get('/mall/order/complete', { params })
}
/**
 * 创建订单备注
 * @param params
 * @returns
 */
export function createOrderRemark(params: remarkParams, id: string | number) {
  return Alova.Post(`/mall/order/create-remark?id=${id}`, params)
}
/**
 * 取消订单
 * @param id
 * @returns
 */
export function cancelOrder(id: number) {
  return Alova.Get(`/mall/order/cancel?id=${id}`)
}
/**
 * 获取订单备注列表
 * @param params
 * @returns
 */
export function getOrderRemark(params: { id: string | number; page: number; pageSize: number }) {
  return Alova.Get<IPageResult<RemarkItem>>('/mall/order/remarks', { params })
}
/**
 * 修改订单备注
 * @param params
 * @returns
 */
export function updateOrderRemark(params: remarkParams, id: string | number) {
  return Alova.Post(`/mall/order/update-remark?id=${id}`, params)
}
/**
 * 删除订单备注
 * @param id
 * @returns
 */
export function deleteOrderRemark(id: number) {
  return Alova.Get(`/mall/order/delete-remark?id=${id}`)
}
/**
 * 修改地址
 */
export function updateOrderAddress(params: SiteParamsType, id: number) {
  return Alova.Post(`/mall/order/update-address?id=${id}`, params)
}
/**
 * 订单详情
 * @param id
 * @returns
 */
export function getOrderDetail(id: number) {
  return Alova.Get<OrderDetail>(`/mall/order/${id}`)
}

/**
 * 售后列表
 */
export function getAfterSales(params: AfterSalesQuery) {
  return Alova.Get<IPageResult<AfterSalesItem>>(`/mall/after-sales`, { params })
}
/**
 * 售后字典集
 */
export function afterSalesDict() {
  return Alova.Get<AfterSalesDict>('/mall/after-sales/dictionaries')
}
/**
 * 售后详情
 */
export function getAfterSalesDetail(id: number) {
  return Alova.Get<AfterSalesDetail>(`/mall/after-sales/${id}`)
}
/**
 * 同意（待处理下显示）
 */
export function agreeAfterSales(id: number) {
  return Alova.Get(`/mall/after-sales/${id}/approve`)
}
/**
 * 拒绝（待处理下显示）
 */
export function rejectAfterSales(id: number, params: { reason: string }) {
  return Alova.Post(`/mall/after-sales/${id}/reject`, params)
}
/**
 * 同意退款
 */
export function agreeRefund(id: number, params: { refund_amount: Number }) {
  return Alova.Post(`/mall/after-sales/${id}/confirm-refund`, params)
}
/**
 * 确认收货
 */
export function confirmReceive(id: number) {
  return Alova.Get(`/mall/after-sales/${id}/confirm-receive`)
}
/**
 * 重新发货（换货且确认收货后）
 */
export function reShip(id: number, params: reShipParams) {
  return Alova.Post(`/mall/after-sales/${id}/re-ship`, params)
}
