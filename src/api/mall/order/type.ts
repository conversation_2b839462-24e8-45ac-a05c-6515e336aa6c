// 通用字典项类型
export interface DictItem {
  value: string | number
  label: string
  color?: string
}
export interface ExpressItem {
  name: string
  code: string
}
// 订单字典汇总类型
export interface OrderDictionaries {
  order_status: DictItem[]
  pay_status: DictItem[]
  delivery_status: DictItem[]
  receipt_status: DictItem[]
  refund_status: DictItem[]
  order_type: DictItem[]
  order_source: DictItem[]
  shipping_status: DictItem[]
}

export interface orderParams {
  user_keyword?: string // 买家关键词
  province?: number // 省id
  city?: number // 市id
  area?: number // 区id
  payment_type?: number // 支付方式
  shipping_type?: '1' | '2' | '3' // 配送方式：1物流配送、2同城配送、3门店自提
  order_no?: string // 订单编号
  out_trade_no?: string // 交易单号
  create_data?: [string, string] // 创建时间范围
  store_name?: string // 门店名称
  goods_keyword?: string // 商品信息
}

// 用户信息接口
export interface userInfo {
  id: number
  mall_id: number
  nickname: string
  username: string
  avatar_url: string
  mobile: number
}

// 子订单接口
export interface subOrder {
  id: number
  store_id: number
  store_order_id: number
  goods_id: number
  goods_name: string
  goods_img: string
  attr_name: string
  goods_price: number
  quantity: number
  sub_total: number
  promotion_total: number
  status?: number
  shipped_quantity?: number
}

// 订单列表接口
export interface orderItem {
  id: number
  mall_id: number
  store_id: number
  user_id: number
  order_no: string
  receiver_name: string
  receiver_phone: number
  province: string
  city: string
  district: string
  detail_address: string
  delivery_type: number
  freight_amount: number
  pay_amount: number
  payment_type: number
  payment_status: number
  order_type: number
  plugin_source: string
  order_time: number
  pay_time: number | null
  deliver_time: number
  finish_time: number | null
  order_status: number
  subOrders: subOrder[]
  user: userInfo
}

// 物流信息接口
export interface subOrderShipment {
  sub_order_id: number
  quantity: number
}

export interface shipParams {
  id?: number
  sub_order_shipments: subOrderShipment[]
  delivery_type: number
  delivery_company: string | null
  tracking_number: string
}

export interface remarkParams {
  content: string
}

/**
 * 重新发货参数
 */
export interface reShipParams {
  delivery_company: string
  tracking_number: string
}

/**
 * 备注操作人信息
 */
export interface RemarkOperator {
  id: number
  nickname: string
}

/**
 * 备注详情
 */
export interface RemarkItem {
  id: number
  remark_type: number // 备注类型
  content: string // 备注内容
  operator_id: number
  created_at: number
  updated_at: number
  operator: RemarkOperator // 备注人信息
}
/**
 * 订单详情接口
 */
export interface OrderDetail {
  id: number
  mall_id: number
  store_id: number
  user_id: number
  order_no: string
  receiver_name: string // 收货人姓名
  receiver_phone: number // 收货人电话
  province: string
  city: string
  district: string
  subdistrict: string // 街道
  province_code: number // 省编码
  city_code: number // 市编码
  district_code: number // 区编码
  subdistrict_code: number // 街道编码
  postal_code: string // 邮政编码
  detail_address: string
  delivery_type: number // 配送方式
  total_amount: number // 总金额
  freight_amount: number // 运费金额
  pay_amount: number // 支付金额
  discount_amount: number // 优惠金额
  payment_type: number // 支付方式
  payment_status: number // 支付状态
  order_type: number // 订单类型
  plugin_source: string // 来源
  order_time: number // 下单时间
  pay_time: number // 支付时间
  deliver_time: number // 发货时间
  shipped_time: number | null // 发货时间
  delivered_time: number | null // 送达时间
  complete_time: number | null // 完成时间
  finish_time: number | null // 完成时间
  order_status: number // 订单状态
  updated_at: number // 更新时间
  created_at: number // 创建时间
  shipping_fee: number // 运费
  auto_close_time: number // 自动关闭时间
  subOrders: subOrder[]
  remarks: RemarkItem[]
  user: userInfo
  shipments: OrderShipment[]
}

export interface SiteParamsType {
  receiver_name: string
  receiver_phone: number | string
  province: string
  province_code: number | string
  city: string
  city_code: number | string
  district: string
  district_code: number | string
  subdistrict: string
  subdistrict_code: number | string
  detail_address: string
  postal_code: string
  address?: string
}
export enum AfterSalesStatus {
  PendingMerchant = 'pending_merchant',
  PendingBuyer = 'pending_buyer',
  PendingReceipt = 'pending_receipt',
  Success = 'success',
  Closed = 'closed',
}
export interface AfterSalesQuery {
  status?: AfterSalesStatus
  user_keyword?: string
  create_data?: [string?, string?] // [开始时间, 结束时间]
  store_name?: string
  after_sales_no?: string
  order_no?: string
  type?: string
  after_sales_status?: number
}

export interface SubOrder {
  goods_name: string
  goods_img: string
  attr_id: number
  attr_name: string
  goods_price: number
  quantity: number
  shipping_status: number
  shipped_quantity: number
  sub_total: number
  status?: number
}

export interface Order {
  order_no: string
  receiver_name: string
  receiver_phone: number
  province: string
  province_code: number
  city: string
  city_code: number
  district: string
  district_code: number
  subdistrict: string
  subdistrict_code: number
  detail_address: string
  delivery_type: number
  total_amount: number
  freight_amount: number
  pay_amount: number
}

export interface User {
  id: number
  username: string
  nickname: string
  mobile: number
  avatar_url: string
}

export interface Store {
  id: number
  store_name: string
  logo_img: string
}

export interface AfterSalesItem {
  id: number
  store_id: number
  store_order_id: number
  store_sub_order_id: number
  user_id: number
  order_no: string
  type: string
  is_shipped: number
  quantity: number
  amount: number
  reason: string
  remarks: string
  status: number
  updated_at: number
  created_at: number
  subOrder: SubOrder
  order: Order
  user: User
  store: Store
}

// 单个商品信息
export interface GoodsItem {
  goods_name: string
  goods_img: string
  quantity: number
  attr_name: string
  // 其他商品相关字段可补充
}

// 订单详情项
export interface DetailItem {
  id: number
  shipment_id: number
  store_sub_order_id: number
  shipped_quantity: number
  goods: GoodsItem
}

// 物流追踪信息
export interface TrackingInfo {
  h_m: string // 时间（如 "18:15"）
  date: string // 日期（如 "4月27号"）
  time: string // 完整时间（如 "2025-04-27 18:15:59"）
  status: string // 状态描述
}

// 订单主结构
export interface OrderShipment {
  delivery_company: string
  details: DetailItem[]
  last_tracking: TrackingInfo[]
  store_order_id: number
  tracking_number: string
  shipped_time: string | null
  shipping_fee: number
  delivery_company_name: string
  remark: string
  logistics_status: number
  operator_name: string
}
/**
 * 售后字典
 */
export interface AfterSalesDict {
  types: DictItem[]
  status: DictItem[]
  source: DictItem[]
  step_type: DictItem[]
  snapshot_key: DictItem[]
}
/**
 * 协商记录
 */
export interface AfterSalesStepSnapshot {
  after_sales_type: string // 售后类型，例如 "return"
  after_sales_amount: number // 售后金额
  after_sales_quantity: number // 售后数量
  after_sales_reason: string // 售后原因
  after_sales_remarks: string // 售后备注
  return_address: string // 退货地址
  return_phone: string | number // 退货电话
  return_name: string // 退货人
  message: string //消息
  reason: string //拒绝理由
  apply_remarks: string // 申请备注
  delivery_company: string // 物流公司
  tracking_number: string // 物流单号
  rejection_reason: string // 拒绝理由
}

export interface AfterSalesStep {
  after_sales_id: number
  step_type: string
  source: string
  created_at: number
  snapshot: AfterSalesStepSnapshot
}
export interface AfterSalesDetail extends OrderDetail {
  goods_id: number
  type: string
  amount: number
  reason: string
  status: number
  subOrder: subOrder
  step: AfterSalesStep[]
  order: orderItem
  return_receiver_info: {
    return_phone: string | number
    return_name: string
    return_address: string
  }
}
