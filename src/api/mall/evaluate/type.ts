export type YesNo = '0' | '1'

export interface CommentQuery {
  order_no?: string
  goods_name?: string
  store_name?: string
  user_info?: string
  is_top?: YesNo
  is_default?: YesNo
  date?: string
  page: number
  limit: number
}
export interface CommentParams {
  goods_id: number | string
  virtual_user?: string
  virtual_avatar?: string
  virtual_time?: string | null // ISO8601 日期时间字符串
  is_cryptonym?: number // 0 否, 1 是
  score: number // 1=差评, 2/3=中评, 4/5=好评
  content?: string
  pic_url?: string[]
}
export interface CommentItem {
  id: number
  mall_id: number
  user_id: number
  goods_id: number
  attr_id: number
  score: number
  content: string
  pic_url: string[]
  is_top: number
  is_default: number
  is_cryptonym: number
  reply_content: string
  store_id: number
  order_id: number
  sub_order_id: number
  is_virtual: number
  virtual_user: string
  virtual_avatar: string
  virtual_time: number
  status: number
  created_at: number
  updated_at: number
  deleted_at: number | null
  user: any // 可根据实际结构替换为具体类型
  goods: any // 可根据实际结构替换为具体类型
  attr: any // 可根据实际结构替换为具体类型
  store: any // 可根据实际结构替换为具体类型
  subOrder: any // 可根据实际结构替换为具体类型
}
export interface CommentTop {
  is_top: number
}
export interface CommentStatus {
  status: number
}
export interface CommentReply {
  content: string
}
