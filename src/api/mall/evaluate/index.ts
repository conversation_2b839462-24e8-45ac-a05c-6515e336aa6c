import { Alova } from '@/utils/http/alova/index'
import {
  CommentQuery,
  CommentParams,
  CommentItem,
  CommentTop,
  CommentStatus,
  CommentReply,
} from './type'
/**
 * 获取商品评论列表
 * @param params
 * @returns
 */
export function getGoodsCommentList(params: CommentQuery) {
  return Alova.Get<IPageResult<CommentItem>>('/mall/goods-comment', { params })
}
/**
 * 获取商品评论详情
 */
export function getCommentDetail(id: number) {
  return Alova.Get<CommentItem>(`/mall/goods-comment/${id}`)
}
/**
 * 添加评论
 */
export function addComment(params: CommentParams) {
  return Alova.Post(`/mall/goods-comment`, params)
}
/**
 * 置顶/取消置顶
 */
export function toggleCommentTop(params) {
  return Alova.Put<CommentTop>(`/mall/goods-comment/change-top?id=${params.id}`)
}
/**
 * 隐藏/取消隐藏
 */
export function toggleCommentHide(params) {
  return Alova.Put<CommentStatus>(`/mall/goods-comment/change-status?id=${params.id}`)
}
/**
 * 删除评论
 */
export function deleteComment(id: number) {
  return Alova.Delete(`/mall/goods-comment/${id}`)
}
/**
 * 添加回复
 */
export function addReply(params: CommentReply, id: number) {
  return Alova.Post(`/mall/goods-comment/reply?id=${id}`, params)
}
