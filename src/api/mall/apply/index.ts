import { Alova } from '@/utils/http/alova/index'
import {
  marketParams,
  purchasedParams,
  MarketResponse,
  ApplyDetail,
  buyParams,
  BuyItem,
  nameParams,
  orderParams,
  orderItem,
  codeOrderResponse,
  closeParams,
  orderDetail,
} from './type'

/**
 * 获取插件市场
 * @param params
 * @returns
 */
export function getMarketList(params: marketParams) {
  return Alova.Get<MarketResponse>('/mall/addons/market', { params })
}
/**
 * 获取已购应用
 * @param params
 * @returns
 */
export function getPurchasedList(params: purchasedParams) {
  return Alova.Get<MarketResponse>('/mall/addons/purchased', { params })
}
/**
 * 获取应用详情
 * @param params
 * @returns
 */
export function getApplyDetail(name: string) {
  return Alova.Get<ApplyDetail>(`/mall/addons/detail/${name}`, {})
}

/**
 * 获取购买记录
 * @param params
 * @returns
 */
export function getBuyList(params: buyParams) {
  return Alova.Get<IPageResult<BuyItem>>('/mall/addons-order', { params })
}
/**
 * 我的应用
 * @param params
 * @returns
 */
export function getApplyList(params: purchasedParams) {
  return Alova.Get<MarketResponse>('/mall/addons', { params })
}
/**
 * 设置应用置顶
 * @param params
 * @returns
 */
export function setApplyToggle(api: string, params: nameParams) {
  return Alova.Post(api, params)
}
/**
 * 应用卸载
 * @param params
 * @returns
 */
export function uninstallApply(params: nameParams) {
  return Alova.Post('/mall/addons/uninstall', params)
}
/**
 * 应用安装
 * @param params
 * @returns
 */
export function installApply(params: nameParams) {
  return Alova.Post('/mall/addons/install', params)
}
/**
 * 确认订单
 * @param params
 * @returns
 */
export function confirmOrder(params: orderParams) {
  return Alova.Post<orderItem>('/mall/addons-order/confirm-order', params)
}
/**
 * 生成扫码订单
 * @param params
 * @returns
 */
export function codeOrder(params: orderParams) {
  return Alova.Post<codeOrderResponse>('/mall/addons-order', params)
}
/**
 * 取消订单
 * @param params
 * @returns
 */
export function closeOrder(params: closeParams) {
  return Alova.Post('/mall/addons-order/close', params)
}

/**
 * 订单详情
 * @param params
 * @returns
 */
export function getApplyOrder(id: number) {
  return Alova.Get<orderDetail>(`/mall/addons-order/${id}`, {})
}
