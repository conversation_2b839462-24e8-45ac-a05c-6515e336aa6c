import type { DropdownOption } from 'naive-ui'

export interface marketParams {
  title?: string //标题
}
export interface purchasedParams {
  keyword?: string //标题
}
export interface nameParams {
  name: string //标题
}
export interface closeParams {
  id: number | string //ID
}

export interface orderParams {
  name: string //应用名称
  setting_index: number
}

export interface MarketItem {
  title: string
  list: ChildItem[]
}

export interface MarketResponse {
  data: MarketItem[]
}
export type ApplyOption = DropdownOption & {
  label: string
  key: string
  id: number
}
export interface ChildItem {
  id: number
  name: string
  title: string
  cover: string
  group: string
  brief_introduction: string
  course_url: string
  is_install: number
  // dataItem?: any
  // dataItem?: any // 根据实际数据结构调整类型
  [key: string]: any // 允许扩展其他属性
  status?: number // 根据你的代码中使用了 `dataItem.status`，这里假设 `status` 可能存在
  applyOptions?: ApplyOption[] // 根据你的代码中使用了 `dataItem.applyOptions`，这里假设 `applyOptions` 可能存在
}

// 定义应用价格设置的类型
export interface PriceSetting {
  // 根据实际需求定义价格设置的具体属性
  // 示例：
  unit?: number
  duration?: number
  money?: number
  price: number
  period: string // 如 "3天", "7天" 等
}

// 定义应用详情的接口
export interface ApplyDetail {
  title: string // 应用名称
  name: string // 应用英文标识
  cover: string // 应用图
  group: string // 应用所属分组
  brief_introduction: string // 应用备注
  course_url: null | string // 课程链接，可能为null
  description: string // 应用详情
  price_setting: PriceSetting[] // 应用价格设置数组，包含3个对象
  is_renew: 0 | 1 // 是否可以续费，0表示否，1表示是
  is_buy: 0 | 1 // 是否已购买，0表示未购买，1表示已购买,
  allow_uninstall: 0 | 1 // 是否允许卸载，0表示否，1表示是
  allow_install: 0 | 1 // 是否允许安装，0表示否，1表示是
  expire_time: number
}

//购买记录
interface CreateData {
  [key: number]: string
}

type orderStatus = 0 | 4 | -4
export type buyStatus = 0 | 4 | -4
export interface buyParams {
  create_data: CreateData
  order_status?: orderStatus // 可选参数
}
export interface BuyItem {
  id: number
  order_no: string
  addons_name: string
  created_at: number
  order_price: number
  pay_money: number
  order_status: buyStatus
  expire_time: number
  operator_username: string
  addons: AddonsInfo
}
interface AddonsInfo {
  title: string // 应用名称
  name: string // 应用标识
  cover: string | null // 应用图
  brief_introduction: string // 应用备注
}
export interface BuyListResponse {
  list: BuyItem[]
}
/**
 * 安装应用功能
 */
export interface orderItem {
  id?: number
  name?: string
  title?: string
  cover?: string
  brief_introduction?: string
  cycle?: string
  num?: number
  order_price?: number
  payAgreement?: string
  subtotal?: number
  [key: string]: any // 允许动态扩展其他字段
}

export interface codeOrderResponse {
  order: orderResponse // 添加 order 属性
}
export interface orderResponse {
  order_id: number // 必需
  order_no: string // 必需
  title: string // 必需
  pay_status: number // 必需, 订单付款状态[0=未支付,1=已支付]
  addons_name: string // 必需
  unit: number // 必需
  duration: number // 必需
  pay_money: number // 必需
  mall_name: string
}

//订单详情
export interface orderDetail {
  id?: number
  addons_name?: string
  order_no?: string
  created_at: number | Date
  close_time: number | null | Date
  updated_at: number | Date
  order_status?: number // 0:待付款, 4:已完成, -4:已关闭
  pay_money?: number
  order_price?: number
  unit?: number // 周期单位[0=永久, 1=月, 2=年]
  duration?: number
  addons?: AddonsInfo
  mall_name?: string
  expiration?: number
}

interface AddonsInfo {
  title: string
  id: number
  brief_introduction: string
  cover: string | null
}
