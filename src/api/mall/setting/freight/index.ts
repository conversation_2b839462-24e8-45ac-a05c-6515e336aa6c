import { Alova } from '@/utils/http/alova/index'
import { tableList, UserListRequest, formParams, AddressType } from './type'

// ------------------------------商品服务--------------------------------
/**
 * 运费模板列表请求参数
 */
/**
 * 获取列表
 * @param params
 * @returns
 */
export function getShippingList(params: UserListRequest) {
  return Alova.Get<IPageResult<tableList>>('/mall/shipping-template', { params })
}

/**
 * 新增运费模板
 * @param params
 * @returns
 */
export function addShippingTemplate(params: formParams) {
  return Alova.Post(`/mall/shipping-template`, params)
}

//获取地区数据
export function allProvinces(params?) {
  return Alova.Get('/common/region/tree', {
    params,
  })
}

export function regionParent(params) {
  return Alova.Get('/common/region/tree', {
    params,
  })
}
/**
 * 获取街道
 * @param params
 * @returns
 */
export function regionStreet(params: { pid: number }) {
  return Alova.Get<AddressType[]>('/common/region/get-subdistrict', {
    params,
  })
}
/**
 * 编辑模板
 * @param params
 * @returns
 */
export function editShippingTemplate(params: formParams) {
  return Alova.Put(`/mall/shipping-template/${params.id}`, params)
}

/**
 * 模板详情
 * @param params
 * @returns
 */
export function shippingTemplateInfo(id: number) {
  return Alova.Get<formParams>(`/mall/shipping-template/${id}`)
}

/**
 * 删除模板
 * @param params
 * @returns
 */
export function deleteShippingTemplate(id: number) {
  return Alova.Delete(`/mall/shipping-template/${id}`)
}

/**
 * 设置默认
 * @param params
 * @returns
 */
export function editShippingDefault(id: number) {
  return Alova.Get<[]>(`/mall/shipping-template/set-default?id=${id}`)
}
