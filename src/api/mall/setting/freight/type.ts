export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 参数
 */
export interface UserParams {
  id: Number
  service_name: String
  description: String
  icon_type: Number
  custom_icon_url: String
  sort: Number
  status: Number
  created_at: String
}

/**
 * 列表
 */
export interface tableList {
  id: Number
  service_name: String
  description: String
  icon_type: Number
  custom_icon_url: String
  sort: Number
  status: Number
  created_at: String
}

/**
 * 配送区域
 */
export interface addTemplate {
  region_ids: Number[]
  first_item_count: String
  first_item_fee: String
  additional_item_count: String
  additional_item_fee: String
  free_threshold: String
}

export interface addNonTemplate {
  areaList: Number[]
}

/**
 * 提交参数
 */
export interface formParams {
  id: String
  template_name: String
  billing_method: Number
  status: Number
  region_shipping_fees: Number[]
  excluded_regions: Number[]
}
/**
 * 地址类型
 */
export interface AddressType {
  id: number
  fullname: string
  parent_id: number
}
