export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 通知参数
 */
export interface UserParams {
  default_level_name: String
  force_user_profile: Number
  is_accept_mobile_code: Number
  is_log_off_del: Number
  is_manual_verification_code: Number
  is_multi_terminal_login: Number
  is_scan_code_to_app: Number
  is_show_logoff: Number
  is_show_report: Number
}

/**
 * 通知列表
 */
export interface rewardParams {
  is_recommend_register_score: Number
  is_user_register_score: Number
  recommend_register_amount: String | Number
  user_register_amount: String | Number
}

export interface logisticsInfo {
  kdniao?: kdniao
  qijianshi?: qijianshi
  request_type: string
  update_interval?: Number
}

export interface logisticsList {
  request_type: string
  list: logisticsItem[]
}

/* 快递型号 */
export interface typeItem {
  label: string
  value: string
}

export interface logisticsItem {
  name: string
  title: string
}
interface kdniao {
  request_type: Number
  mch_id: String
  api_key: String
  jd_mch_code: String
}
interface qijianshi {
  app_id: String
  app_key: String
  app_secret: String
}

export interface tabList {
  id: Number
  name: String
}
