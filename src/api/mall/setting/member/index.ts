import { Alova } from '@/utils/http/alova/index'
import { UserParams, rewardParams, logisticsInfo, logisticsList } from './type'

// ------------------------------商品服务--------------------------------
/**
 * 会员设置请求参数
 */
/**
 * 获取基本信息
 * @param params
 * @returns
 */
export function getBaseSetInfo() {
  return Alova.Get<UserParams>('/mall/setting/get-user')
}

/**
 * 编辑基本信息
 * @param params
 * @returns
 */
export function editBaseSetInfo(params: UserParams) {
  return Alova.Post(`/mall/setting/save-user`, params)
}

/**
 * 获取奖励信息
 * @param params
 * @returns
 */
export function getRewardSetInfo() {
  return Alova.Get<rewardParams>('/mall/setting/get-user-reward')
}

/**
 * 编辑奖励信息
 * @param params
 * @returns
 */
export function editRewardSetInfo(params: rewardParams) {
  return Alova.Post(`/mall/setting/save-user-reward`, params)
}

/**
 * 获取物流公司
 * @param params
 * @returns
 */
export function getLogisticsInfo() {
  return Alova.Get<logisticsList>('/mall/setting/get-logistics')
}

/**
 * 编辑物流公司信息
 * @param params
 * @returns
 */
export function editLogisticsInfo(params: logisticsInfo) {
  return Alova.Post(`/mall/setting/save-logistics`, params)
}
