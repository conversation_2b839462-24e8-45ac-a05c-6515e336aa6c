export interface agreementInfo {
  // 基础字段
  id: number
  mall_id: number
  store_id: number
  supply_id: number
  key: string
  title: string
  remark: string | null
  created_at: number
  updated_at: number
  deleted_at: null | number
  name: string
}
export interface queryParams {
  name?: string
}

// 新增/编辑请求参数
export interface agreementParams {
  id?: number | string
  key: string
  title: string
  content: string
  remark?: string | null
  //   store_id: number
  //   mall_id: number
  //   supply_id: number
}
