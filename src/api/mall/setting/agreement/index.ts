import { Alova } from '@/utils/http/alova/index'
import { agreementInfo, queryParams, agreementParams } from './type'
// ------------------------------协议设置--------------------------------

/**
 * 获取协议列表
 * @returns
 */
export function getAgreementList(params: queryParams) {
  return Alova.Get<agreementInfo[]>('/mall/protocol', { params })
}
/**
 * 协议详情
 * @param id 协议id
 * @returns
 */
export function getAgreementDetail(id: number) {
  return Alova.Get<agreementParams>(`/mall/protocol/${id}`)
}
/**
 * 更新协议
 * @param id 协议id
 * @param params 协议参数
 * @returns
 */
export function updateAgreement(id: number, params: agreementParams) {
  return Alova.Put(`/mall/protocol/${id}`, params)
}
