export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 基本设置
 */
export interface BaseInfo {
  name: String
  contact_tel: String
  web_url: String
  logo: String
}

/**
 * 版权设置
 */
export interface CopyRight {
  status: Number
  description: String
  link_logo: String
}

export interface ShareInfo {
  title: String
  desc: String
  pic: String
}

export interface SensitiveInfo {
  platform: String
  api_key: String
  secret_key: String
}

export interface tabList {
  id: Number
  name: String
}
