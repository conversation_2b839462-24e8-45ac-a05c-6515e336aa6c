import { Alova } from '@/utils/http/alova/index'
import { BaseInfo, CopyRight, ShareInfo, SmsInfo, SensitiveInfo } from './type'

// ------------------------------商品服务--------------------------------
/**
 * 会员设置请求参数
 */
/**
 * 获取基本设置
 * @param params
 * @returns
 */
export function getBaseSetInfo() {
  return Alova.Get<BaseInfo>('/mall/setting/get-basic')
}

/**
 * 编辑基本信息
 * @param params
 * @returns
 */
export function editBaseSetInfo(params: BaseInfo) {
  return Alova.Post(`/mall/setting/save-basic`, params)
}

/**
 * 获取版权信息
 * @param params
 * @returns
 */
export function getCopyRightInfo() {
  return Alova.Get<CopyRight>('/mall/setting/get-copyright')
}

/**
 * 编辑版权信息
 * @param params
 * @returns
 */
export function editCopyRightInfo(params: CopyRight) {
  return Alova.Post(`/mall/setting/save-copyright`, params)
}

/**
 * 获取分享设置
 * @param params
 * @returns
 */
export function getShareInfo() {
  return Alova.Get<ShareInfo>('/mall/setting/get-share')
}

/**
 * 编辑分享设置
 * @param params
 * @returns
 */
export function editShareInfo(params: ShareInfo) {
  return Alova.Post(`/mall/setting/save-share`, params)
}

/**
 * 获取短信设置
 * @param params
 * @returns
 */
export function getSmsInfo() {
  return Alova.Get<SmsInfo>('/mall/setting/get-sms')
}

/**
 * 编辑短信设置
 * @param params
 * @returns
 */
export function editSmsInfo(params: SmsInfo) {
  return Alova.Post(`/mall/setting/save-sms`, params)
}

/**
 * 获取敏感词设置
 * @param params
 * @returns
 */
export function getSensitiveInfo() {
  return Alova.Get<SensitiveInfo>('/mall/setting/get-sensitive')
}

/**
 * 编辑敏感词设置
 * @param params
 * @returns
 */
export function editSensitiveInfo(params: SensitiveInfo) {
  return Alova.Post(`/mall/setting/save-share`, params)
}
