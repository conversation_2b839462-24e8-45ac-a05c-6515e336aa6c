/**
 * 交易设置接口返回数据类型
 */
import type { FormRules, FormItemRule } from 'naive-ui'

export interface tabItem {
  id: number
  name: string
}

export interface afterParams {
  name: string
  mobile: string
  address: string
  reason: string | string[]
}

export interface siteQuery {
  user_keyword: string
  create_data: [string, string]
}
export interface siteParams {
  name: string // 名称
  receiver_name: string // 收件人名称
  receiver_phone: string // 收件人手机
  province: string // 省份
  province_code: string // 省份code
  city: string // 城市
  city_code: string // 城市code
  district: string // 区县
  district_code: string // 区县code
  subdistrict: string // 街道
  subdistrict_code: string // 街道code
  detail_address: string // 详细地址
}
/**
 * 订单设置参数接口
 */
export interface dealParamsType {
  /**
   * @name 自动关闭时间
   * @description 订单未支付自动关闭时间
   * @type number
   * @unit 小时
   */
  auto_close_time: number

  /**
   * @name 自动发货时间
   * @description 订单自动发货时间
   * @type number
   * @unit 小时
   */
  auto_deliver_time: number

  /**
   * @name 退款时间
   * @description 订单退款自动处理时间
   * @type number
   * @unit 小时
   */
  refund_time: number
}

export interface afterSaleParams {
  name: string
  mobile: string
  address: string
  reason: string
}

export interface afterSaleRules {
  [key: string]: FormItemRule[] // 添加索引签名
  name: FormItemRule[]
  mobile: FormItemRule[]
  address: FormItemRule[]
  reason: FormItemRule[]
}
export interface dealPropsType {
  dealParams: dealParamsType
  dealRules: FormRules
  saleParams: afterParams
  saleRules: FormRules
}
export interface formData {
  params: dealParamsType | afterParams
  type: 'order' | 'afterSale'
}
export interface emitData {
  params: dealParamsType | afterSaleParams
  type: 'order' | 'afterSale'
}

export interface AddressItem {
  id: number // 地址ID
  mall_id: number // 商城ID
  store_id: number // 门店ID
  name: string // 地址名称
  receiver_name: string // 收件人
  receiver_phone: string // 手机
  province: string // 省份
  province_code: number // 省份编码
  city: string // 城市
  city_code: number // 城市编码
  district: string // 区域
  district_code: number // 区域编码
  subdistrict: string // 街道
  subdistrict_code: number // 街道编码
  detail_address: string // 详细地址
  is_default: number // 是否默认
  created_at: number // 创建售后时间
}
