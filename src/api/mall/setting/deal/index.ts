import { Alova } from '@/utils/http/alova/index'
import { afterParams, dealParamsType, AddressItem, siteQuery, siteParams } from './type'

// ------------------------------交易设置--------------------------------

/**
 * 获取订单设置
 * @returns
 */
export function getOrderSetting() {
  return Alova.Get<dealParamsType>('/mall/setting/get-order')
}

/**
 * 获取售后设置
 * @returns
 */
export function getAfterSaleSetting() {
  return Alova.Get<afterParams>('/mall/setting/get-refund')
}

/**
 * 更新订单设置
 * @returns
 */
export function setOrderSetting(params: dealParamsType) {
  return Alova.Post<dealParamsType>('/mall/setting/save-order', params)
}

/**
 * 更新售后设置
 * @returns
 */
export function setAfterSaleSetting(params: afterParams) {
  return Alova.Post<afterParams>('/mall/setting/save-refund', params)
}
/**
 * 售后地址列表
 * @returns
 */
export function getAfterSaleSiteList(params: siteQuery) {
  return Alova.Get<IPageResult<AddressItem>>('/mall/after-sales-address', { params })
}
/**
 * 新增售后地址
 * @returns
 */
export function addAfterSaleSite(params: siteParams) {
  return Alova.Post('/mall/after-sales-address', params)
}
/**
 * 删除售后地址
 * @returns
 */
export function deleteSaleSite(id: number) {
  return Alova.Delete(`/mall/after-sales-address/${id}`)
}
/**
 * 设置默认售后地址
 * @returns
 */
export function setDefaultSaleSite(id: number) {
  return Alova.Post(`/mall/after-sales-address/${id}/set-default`)
}
