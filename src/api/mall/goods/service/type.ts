export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 通知参数
 */
export interface UserParams {
  id: Number
  service_name: String
  description: String
  icon_type: Number
  custom_icon_url: String
  sort: Number
  status: Number
  created_at: String
}

/**
 * 通知列表
 */
export interface tableList {
  id: Number
  service_name: String
  description: String
  icon_type: Number
  custom_icon_url: String
  sort: Number
  status: Number
  created_at: String
}
