import { Alova } from '@/utils/http/alova/index'
import { UserParams, tableList, UserListRequest } from './type'

// ------------------------------商品服务--------------------------------
/**
 * 商品服务列表请求参数
 */
/**
 * 获取列表
 * @param params
 * @returns
 */
export function getServiceList(params: UserListRequest) {
  return Alova.Get<IPageResult<tableList>>('/mall/goods-service', { params })
}

/**
 * 新增服务
 * @param params
 * @returns
 */
export function addGoodsService(params: UserParams) {
  return Alova.Post(`/mall/goods-service`, params)
}

/**
 * 编辑服务
 * @param params
 * @returns
 */
export function editGoodsService(params: UserParams) {
  return Alova.Put(`/mall/goods-service/${params.id}`, params)
}

/**
 * 删除服务
 * @param params
 * @returns
 */
export function deleteGoodsService(id: number) {
  return Alova.Delete(`/mall/goods-service/${id}`)
}
