export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}

/**
 * 商品列表
 */
export interface goodsList {
  cateRelate: []
  cost_price: Number
  created_at: Number
  goods: goodsInfo
  goods_id: Number
  id: Number
  is_on_sale: Number
  price: Number
  sales: Number
  sort: Number
  stock: Number
  tagRelate: []
}

export interface goodsInfo {
  goods_id: Number
}

/**
 * 规格参数
 */
export interface UserParams {
  cate_name: String
  created_at: String
  id: Number
  temps: specification[]
}

/**
 * 规格列表
 */
export interface tableList {
  cate_name: String
  created_at: String
  id: Number
  temps: specification[]
}

export interface specification {
  group_name: String
  values: string[]
}

export interface editSort {
  id: number
  sort: number
}

export interface editSale {
  id: number
}

export interface formValue {
  id: String
  cate_name: String
}

export interface typeList {
  id: Number
  name: String
  tips: String
}

export interface typeOptions {
  children: []
  created_at: String
  id: Number
  name: String
  parent_id: Number
  pic: String
  sort: Number
  1
  status: Number
  updated_at: String
}

export interface tagOptions {
  bg_color: String
  font_color: String
  id: Number
  name: String
}

export interface serviceOptions {
  custom_icon_url: String
  description: String
  icon_type: Number
  id: Number
  service_name: String
}

export interface addTemplate {
  sku_image: String
  price: Number | String
  original_price: Number | String
  cost_price: Number | String
  stock: Number
  weight: Number
  volume: Number
  goods_no: String
  is_default: Number
}

export interface tempList {
  cate_name: String
  created_at: String
  id: String
  temps: specification[]
}

export interface specification {
  group_name: String
  values: string[]
}

export interface specificationsList {
  id: String
  spec_name: String
  values: any[]
}

export interface result {
  temp: temp[]
}

export interface temp {
  key: String
  id: String
  value: String
}

export interface formValueType {
  specificationsType: Number
  specifications: null
  isSubmit: Boolean
  attr: any[]
  groups: any[]
}

export interface submitFormType {
  goods_id: number
  specificationsImage: string
  banner_images: String[]
  cateRelate: number[]
  cate_ids: number[]
  cost_price: Number
  cover_pic: String
  created_at: String
  detail: String
  freight_id: null
  freight_rules_type: Number
  freight_type: String[]
  goods_name: String
  goods_source: String
  goods_type: Number
  groups: []
  id: String
  images: String[]
  isSubmit: Boolean
  isVideo: Boolean
  original_price: Number
  price: Number
  service_ids: number[]
  serviceRelate: number[]
  shipping_fee: String
  sort: Number
  specifications: Number
  specificationsType: Number
  status: Number
  stock: Number
  stock_warning: Number
  subtitle: String
  tagRelate: number[]
  tag_ids: number[]
  unit: String
  videoList: []
  videoType: Number
  virtual_sales: Number
  goods: any
  is_on_sale: Number
  attr: []
}

export interface tabList {
  id: Number
  name: String
}
