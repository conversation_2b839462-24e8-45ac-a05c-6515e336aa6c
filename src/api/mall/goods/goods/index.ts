import { Alova } from '@/utils/http/alova/index'
import {
  UserParams,
  goodsList,
  tableList,
  UserListRequest,
  tagOptions,
  serviceOptions,
  typeOptions,
  submitFormType,
  editSort,
  editSale,
} from './type'

// ------------------------------商品管理--------------------------------
/**
 * 商品列表请求参数
 */
/**
 * 获取列表
 * @param params
 * @returns
 */
export function getGoodsList(params: UserListRequest) {
  return Alova.Get<IPageResult<goodsList>>('/mall/goods', { params })
}

/**
 * 导出列表
 * @param params
 * @returns
 */
export function exportGoodsList() {
  return Alova.Get('/mall/goods')
}

/**
 * 新增商品
 * @param params
 * @returns
 */
export function addGoods(params: submitFormType) {
  return Alova.Post(`/mall/goods`, params)
}

/**
 * 编辑商品
 * @param params
 * @returns
 */
export function editGoods(params: submitFormType) {
  return Alova.Put(`/mall/goods/${params.id}`, params)
}

/**
 * 删除商品
 * @param params
 * @returns
 */
export function deleteGoods(id: String | Number) {
  return Alova.Delete(`/mall/goods/${id}`)
}

/**
 * 商品详情
 * @param params
 * @returns
 */
export function goodsDetail(id: String) {
  return Alova.Get<submitFormType>(`/mall/goods/${id}`)
}

/**
 * 获取商品分类
 * @param params
 * @returns
 */
export function getGoodsType() {
  return Alova.Get<typeOptions[]>('/mall/goods/cate-tree', {})
}

/**
 * 获取商品标签
 * @param params
 * @returns
 */
export function getGoodsTag() {
  return Alova.Get<tagOptions[]>('/mall/goods/tags', {})
}

/**
 * 获取商品服务
 * @param params
 * @returns
 */
export function getGoodsService() {
  return Alova.Get<serviceOptions[]>('/mall/goods/service', {})
}

/**
 * 获取商品规格
 * @param params
 * @returns
 */
export function getGoodsTemp() {
  return Alova.Get<IPageResult<tableList>>('/mall/goods/attr-temp', {})
}

/**
 * 获取商品规格
 * @param params
 * @returns
 */
export function getFreightTemp() {
  return Alova.Get<[]>('/mall/shipping-template', {})
}

/**
 * 编辑规格
 * @param params
 * @returns
 */
export function editGoodsSpecifications(params: UserParams) {
  return Alova.Put(`/mall/goods-attr-temp/${params.id}`, params)
}

/**
 * 编辑排序
 * @param params
 * @returns
 */
export function editGoodsSort(params: editSort) {
  return Alova.Get<[]>('/mall/goods/change-sort', { params })
}

/**
 * 编辑上下架
 * @param params
 * @returns
 */
export function editGoodsSale(params: editSale) {
  return Alova.Get<[]>(`/mall/goods/change-is-on-sale`, { params })
}

/**
 * 删除规格
 * @param params
 * @returns
 */
export function deleteGoodsSpecifications(id: number) {
  return Alova.Delete(`/mall/goods-attr-temp/${id}`)
}
