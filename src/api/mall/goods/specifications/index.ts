import { Alova } from '@/utils/http/alova/index'
import { UserParams, tableList, UserListRequest } from './type'

// ------------------------------商品规格--------------------------------
/**
 * 商品服务列表请求参数
 */
/**
 * 获取列表
 * @param params
 * @returns
 */
export function getSpecificationsList(params: UserListRequest) {
  return Alova.Get<IPageResult<tableList>>('/mall/goods-attr-temp', { params })
}

/**
 * 新增规格
 * @param params
 * @returns
 */
export function addGoodsSpecifications(params: UserParams) {
  return Alova.Post(`/mall/goods-attr-temp`, params)
}

/**
 * 编辑规格
 * @param params
 * @returns
 */
export function editGoodsSpecifications(params: UserParams) {
  return Alova.Put(`/mall/goods-attr-temp/${params.id}`, params)
}

/**
 * 删除规格
 * @param params
 * @returns
 */
export function deleteGoodsSpecifications(id: number) {
  return Alova.Delete(`/mall/goods-attr-temp/${id}`)
}
