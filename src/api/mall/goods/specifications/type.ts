export interface UserListRequest extends IPageRequest {
  keyword?: string
  create_data?: [string, string]
}
/**
 * 规格参数
 */
export interface UserParams {
  cate_name: String
  created_at: String
  id: Number
  temps: specification[]
}

/**
 * 规格列表
 */
export interface tableList {
  cate_name: String
  created_at: String
  id: String
  temps: specification[]
}

export interface specification {
  group_name: String
  values: string[]
}

export interface formValue {
  id: String
  cate_name: String
}
