import { computed } from 'vue'
import { useDesignSettingWithOut } from '@/store/modules/designSetting'
import { useProjectSetting } from '@/hooks/setting/useProjectSetting'

/**
 * 统一的主题配置管理
 * 提供所有 Naive UI 组件的主题覆盖配置
 * 避免多处配置不同步的问题
 */
export function useThemeConfig() {
  const designStore = useDesignSettingWithOut()
  const { getBorderRadius } = useProjectSetting()
  const appTheme = designStore.getAppTheme
  const colorMap = designStore.getColorMap

  // 获取边框圆角值 - 使用项目设置中的值
  const borderRadiusValue = computed(() => {
    return getBorderRadius.value
  })

  // 菜单主题配置
  const getMenuThemeConfig = computed(() => ({
    borderRadius: `${borderRadiusValue.value}px`,

    // hover 状态样式
    itemColorHover: 'rgba(243, 246, 253, 1)', // hover 背景色
    itemTextColorHover: 'rgba(0, 0, 0, 0.65)', // hover 文字颜色
    itemIconColorHover: 'rgba(0, 0, 0, 0.65)', // hover 图标颜色

    // 激活状态样式
    itemColorActive: '#FFFFFF', // 激活背景色
    itemColorActiveHover: '#FFFFFF', // 激活状态下hover背景色
    itemTextColorActive: appTheme, // 激活文字颜色
    itemIconColorActive: appTheme, // 激活图标颜色
    itemTextColorActiveHover: appTheme, // 激活状态下hover文字颜色
    itemIconColorActiveHover: appTheme, // 激活状态下hover图标颜色

    // 未激活状态样式（默认状态）
    itemTextColor: 'rgba(0, 0, 0, 0.65)', // 默认文字颜色
    itemIconColor: 'rgba(0, 0, 0, 0.65)', // 默认图标颜色

    // 子菜单样式
    itemTextColorChildActive: appTheme, // 子菜单激活文字颜色
    itemIconColorChildActive: appTheme, // 子菜单激活图标颜色

    // 分组标题样式
    groupTextColor: '#000000', // 分组标题颜色

    // 箭头颜色
    arrowColor: 'rgba(0, 0, 0, 0.65)', // 箭头默认颜色
    arrowColorHover: 'rgba(0, 0, 0, 0.65)', // 箭头hover颜色
    arrowColorActive: appTheme, // 箭头激活颜色
  }))

  // 完整的主题覆盖配置
  const getThemeOverrides = computed(() => {
    const borderRadius = `${borderRadiusValue.value}px`

    return {
      common: {
        fontFamily: "'MiSans', -apple-system, BlinkMacSystemFont, sans-serif",
        fontSize: '14px',
        bodyColor: '#f7f7f7',
        textColor2: '#333333',
        primaryColor: appTheme,
        primaryColorSuppl: appTheme,
        primaryColorHover: colorMap['blue-deep-hover'],
        primaryColorPressed: colorMap['blue-deep-pressed'],
        infoColorSuppl: colorMap['white'],
        infoColorPressed: colorMap['blue-deep-pressed'],
        successColor: colorMap['green-assist'],
        successColorHover: colorMap['green-assist-hover'],
        successColorPressed: colorMap['green-assist-pressed'],
        warningColor: colorMap['orange-assist'],
        warningColorHover: colorMap['orange-assist-hover'],
        warningColorPressed: colorMap['orange-assist-pressed'],
        errorColor: colorMap['red-assist'],
        errorColorHover: colorMap['red-assist-hover'],
        errorColorPressed: colorMap['red-assist-pressed'],
        errorColorSuppl: colorMap['red-assist'],
        // 全局圆角设置
        borderRadius: borderRadius,
        borderRadiusSmall: `${Math.max(borderRadiusValue.value - 2, 2)}px`,
      },
      Spin: {
        color: appTheme,
      },

      // 菜单组件
      Menu: getMenuThemeConfig.value,

      // 按钮组件
      Button: {
        borderRadius: borderRadius,
        // Info 按钮
        colorInfo: colorMap['white'],
        textColorHoverInfo: colorMap['blue-deep'],
        textColorPressedInfo: colorMap['blue-deep'],
        colorPressedInfo: colorMap['flow-blue-tag'],
        colorHoverInfo: colorMap['flow-blue-tag'],
        textColorInfo: colorMap['blue-deep'],
        textColorTextPressedInfo: colorMap['blue-deep'],
        borderInfo: '1px solid ' + colorMap['blue-deep'],
        colorFocusInfo: colorMap['white'],
        textColorFocusInfo: colorMap['blue-deep'],
        colorDisabledInfo: colorMap['gray-button'],
        colorFocusInfoSuppl: colorMap['blue-deep'],
        borderDisabledInfo: '1px solid ' + colorMap['gray-button'],
        textColorDisabledInfo: '#000',
        textColorTextDisabledInfo: colorMap['gray-button'],
        // Warning 按钮
        colorWarning: colorMap['white'],
        textColorWarning: colorMap['red-assist'],
        textColorHoverWarning: colorMap['red-assist'],
        textColorPressedWarning: colorMap['red-assist'],
        textColorFocusWarning: colorMap['red-assist'],
        borderWarning: '1px solid ' + colorMap['red-assist'],
        colorHoverWarning: colorMap['red-tag'],
        colorPressedWarning: colorMap['red-tag'],
        colorFocusWarning: colorMap['white'],
        colorDisabledWarning: colorMap['red-tag'],
        textColorDisabledWarning: colorMap['red-assist'],
        textColorTextDisabledWarning: colorMap['red-tag'],
        borderDisabledWarning: '1px solid ' + colorMap['red-tag'],
      },

      // 输入框组件
      Input: {
        borderRadius: borderRadius,
      },

      // 选择器组件
      Select: {
        borderRadius: borderRadius,
      },

      // 卡片组件
      Card: {
        borderRadius: borderRadius,
      },

      // 对话框组件
      Dialog: {
        borderRadius: borderRadius,
      },

      // 抽屉组件
      Drawer: {
        borderRadius: borderRadius,
      },

      // 模态框组件
      Modal: {
        borderRadius: borderRadius,
      },

      // 标签页组件
      Tabs: {
        borderRadius: borderRadius,
        colorSegment: colorMap['white'],
        tabTextColorSegment: colorMap['text-deep-gray'],
        tabTextColorActiveSegment: colorMap['blue-deep'],
        tabTextColorHoverSegment: colorMap['text-deep-gray'],
        tabColorSegment: colorMap['blue-tag'],
      },

      // 表格组件
      DataTable: {
        borderRadius: borderRadius,
        thTextColor: colorMap['text-deep-gray'],
        tdTextColor: colorMap['title-deep-gray'],
      },

      // 分页组件
      Pagination: {
        borderRadius: borderRadius,
        itemTextColor: colorMap['text-deep-gray'],
        itemTextColorHover: colorMap['text-deep-gray'],
        itemColorActive: colorMap['left-bg'],
        itemColorActiveHover: colorMap['left-bg'],
        itemBorderActive: '0px',
        itemColorHover: colorMap['gray-button'],
        buttonBorder: '0px',
        buttonBorderHover: '0px',
        buttonBorderPressed: '0px',
        itemBorderDisabled: '0px',
        itemColorDisabled: 'rgba(0, 0, 0, 0)',
        itemColorPressed: colorMap['left-bg'],
      },

      // 下拉菜单组件
      Dropdown: {
        borderRadius: borderRadius,
      },

      // 弹出框组件
      Popover: {
        borderRadius: borderRadius,
      },

      // 工具提示组件
      Tooltip: {
        borderRadius: borderRadius,
        color: '#787D80',
      },

      // 通知组件
      Notification: {
        borderRadius: borderRadius,
      },

      // 消息组件
      Message: {
        borderRadius: borderRadius,
        colorWarning: colorMap['yellow-assist'],
      },

      // 标签组件
      Tag: {
        // borderRadius: borderRadius,
        padding: '0px 8px',
        textColor: colorMap['text-title-deep-gray'],
        border: '0px',
      },

      // 徽章组件
      Badge: {
        borderRadius: borderRadius,
      },

      // 头像组件
      Avatar: {
        borderRadius: borderRadius,
      },

      // 进度条组件
      Progress: {
        borderRadius: borderRadius,
      },

      // 步骤条组件
      Steps: {
        borderRadius: borderRadius,
      },

      // 时间选择器组件
      TimePicker: {
        borderRadius: borderRadius,
      },

      // 日期选择器组件
      DatePicker: {
        borderRadius: borderRadius,
      },

      // 颜色选择器组件
      ColorPicker: {
        borderRadius: borderRadius,
      },

      // 上传组件
      Upload: {
        borderRadius: borderRadius,
      },

      // 树形控件组件
      Tree: {
        borderRadius: borderRadius,
      },

      // 级联选择器组件
      Cascader: {
        borderRadius: borderRadius,
      },

      // 树形选择器组件
      TreeSelect: {
        borderRadius: borderRadius,
      },

      // 穿梭框组件
      Transfer: {
        borderRadius: borderRadius,
      },

      // 表单组件
      Form: {
        borderRadius: borderRadius,
        labelTextColor: colorMap['text-deep-gray'],
      },

      // 列表组件
      List: {
        borderRadius: borderRadius,
      },

      // 描述列表组件
      Descriptions: {
        borderRadius: borderRadius,
        borderColor: '#fff',
        thTextColor: colorMap['text-middle-gray'],
      },

      // 结果页组件
      Result: {
        borderRadius: borderRadius,
        titleFontSizeSmall: '16px',
        iconSizeSmall: '45px',
        textColor: colorMap['text-middle-gray'],
      },

      // 统计数值组件
      Statistic: {
        borderRadius: borderRadius,
      },

      // 警告提示组件
      Alert: {
        borderRadius: borderRadius,
      },

      // 锚点组件
      Anchor: {
        borderRadius: borderRadius,
      },

      // 回到顶部组件
      BackTop: {
        borderRadius: borderRadius,
      },

      // 面包屑组件
      Breadcrumb: {
        borderRadius: borderRadius,
      },

      // 代码组件
      Code: {
        borderRadius: borderRadius,
      },

      // 分割线组件
      Divider: {
        borderRadius: borderRadius,
      },

      // 空状态组件
      Empty: {
        borderRadius: borderRadius,
      },

      // 图片组件
      Image: {
        borderRadius: borderRadius,
      },

      // 布局组件
      Layout: {
        borderRadius: borderRadius,
        colorEmbedded: '#f5f7f9',
      },

      // 加载条组件
      LoadingBar: {
        colorLoading: appTheme,
        borderRadius: borderRadius,
      },

      // 数字输入框组件
      InputNumber: {
        borderRadius: borderRadius,
      },

      // 评分组件
      Rate: {
        borderRadius: borderRadius,
      },

      // 滑动输入条组件
      Slider: {
        borderRadius: borderRadius,
      },

      // 开关组件
      Switch: {
        borderRadius: `${borderRadiusValue.value * 2}px`, // 开关通常使用更大的圆角
      },

      // 时间轴组件
      Timeline: {
        borderRadius: borderRadius,
      },

      // 文字提示组件
      Typography: {
        borderRadius: borderRadius,
      },
    }
  })

  // 菜单样式预设
  const menuStylePresets = {
    // 用户偏好样式
    userPreferred: {
      itemColorActive: '#FFFFFF',
      itemTextColorActive: appTheme,
      itemIconColorActive: appTheme,
    },

    // 默认样式
    default: {
      itemTextColorHover: appTheme,
      itemIconColorHover: appTheme,
      itemTextColorActive: appTheme,
      itemIconColorActive: appTheme,
    },

    // 深色主题样式
    dark: {
      itemTextColor: '#ffffff',
      itemIconColor: '#ffffff',
      itemColorHover: 'rgba(255, 255, 255, 0.1)',
      itemTextColorHover: '#ffffff',
      itemIconColorHover: '#ffffff',
      itemColorActive: 'rgba(255, 255, 255, 0.2)',
      itemTextColorActive: '#ffffff',
      itemIconColorActive: '#ffffff',
    },
  }

  return {
    getThemeOverrides,
    getMenuThemeConfig,
    getBorderRadius: borderRadiusValue,
    menuStylePresets,
  }
}
