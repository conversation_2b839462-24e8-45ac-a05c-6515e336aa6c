import { ref, onMounted, onUnmounted } from 'vue'
import { getInternalNotifyList, type NotifyItem } from '@/api/admin/home/<USER>'
// import { useMessage } from 'naive-ui'

export function useNotification(onNewNotifications?: (items: NotifyItem[]) => void) {
  const notifications = ref<NotifyItem[]>([])
  const unreadCount = ref(0)
  const recentUnreadCount = ref(0)
  const recentUnread = ref<NotifyItem[]>([])
  const lastCheckTime = ref<string>('')
  let pollingTimer: NodeJS.Timeout | null = null

  // const { notification } = useMessage()

  // 获取通知列表
  async function fetchNotifications() {
    try {
      const result = await getInternalNotifyList({
        read_status: 1,
        internal_notify_status: 1,
      })
      // 只保留未读消息
      const unreadNotifications = (result.list || []).filter((item) => item.read_status === 1)
      // 只要有三天内的未读就回调
      const now = Date.now()
      const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000
      const recentUnreadArr = unreadNotifications.filter((item) => {
        const t = new Date(item.send_time).getTime()
        return t >= threeDaysAgo
      })
      if (recentUnreadArr.length && onNewNotifications) {
        onNewNotifications(recentUnreadArr)
      }
      notifications.value = unreadNotifications
      unreadCount.value = unreadNotifications.length
      recentUnreadCount.value = recentUnreadArr.length
      recentUnread.value = recentUnreadArr
      lastCheckTime.value = new Date().toISOString()
    } catch (error) {
      console.error('获取通知失败:', error)
    }
  }

  // 开始轮询
  function startPolling() {
    // 立即执行一次
    fetchNotifications()

    // 每5分钟轮询一次
    pollingTimer = setInterval(
      () => {
        fetchNotifications()
      },
      5 * 60 * 1000,
    )
  }

  // 停止轮询
  function stopPolling() {
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
  }

  // 标记消息为已读
  async function markAsRead(id: number) {
    // 这里需要添加标记已读的API调用
    // await markNotificationRead(id)

    // 更新本地状态
    notifications.value = notifications.value.filter((item) => item.id !== id)
    unreadCount.value = notifications.value.length
  }

  // 清空所有通知
  function clearAll() {
    notifications.value = []
    unreadCount.value = 0
  }

  onMounted(() => {
    startPolling()
  })

  onUnmounted(() => {
    stopPolling()
  })

  return {
    notifications,
    unreadCount,
    recentUnreadCount,
    recentUnread,
    fetchNotifications,
    startPolling,
    stopPolling,
    markAsRead,
    clearAll,
  }
}
