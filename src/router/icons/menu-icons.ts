import { renderImageIcon } from '@/components/ImageIcon'

/**
 * 菜单图标映射
 * 使用menuIcons目录下的图片作为菜单图标
 * 格式: 'menu:iconName': renderImageIcon(普通状态图片路径, 激活状态图片路径, 菜单key)
 */
export const menuIcons = {
  // 首页
  'menu:home': renderImageIcon(
    'menuIcons/icon_nav_home_nor.png',
    'menuIcons/icon_nav_home_sel.png',
    'home', // 对应路由名称
  ),
  // 客户
  'menu:customer': renderImageIcon(
    'menuIcons/icon_nav_customer_nor.png',
    'menuIcons/icon_nav_customer_sel.png',
    'customer', // 测试用的路由名称
  ),
  // 线索
  'menu:clue': renderImageIcon(
    'menuIcons/icon_nav_clue_nor.png',
    'menuIcons/icon_nav_clue_sel.png',
    'leads', // 对应路由名称
  ),
  // 产品
  'menu:product': renderImageIcon(
    'menuIcons/icon_nav_product_nor.png',
    'menuIcons/icon_nav_product_sel.png',
    'product', // 对应路由名称
  ),
  // 商机
  'menu:business': renderImageIcon(
    'menuIcons/icon_nav_businessr_nor.png',
    'menuIcons/icon_nav_business_sel.png',
    'business', // 对应路由名称
  ),
  // 权限
  'menu:permission': renderImageIcon(
    'menuIcons/icon_nav_permission_nor.png',
    'menuIcons/icon_nav_permission_sel.png',
    'permissions', // 对应路由名称
  ),
  // 设置
  'menu:set': renderImageIcon(
    'menuIcons/icon_nav_set_nor.png',
    'menuIcons/icon_nav_set_sel.png',
    'setting', // 对应路由名称
  ),
  // AI功能
  'menu:ai': renderImageIcon(
    'menuIcons/icon_nav_ai.png',
    'menuIcons/icon_nav_ai.png', // 没有激活状态图标，使用相同图标
    'ai', // 对应路由名称
  ),
  // 应用
  'menu:application': renderImageIcon(
    'menuIcons/icon_nav_application_nor.png',
    'menuIcons/icon_nav_application_sel.png',
    'IntelligentApps', // 对应路由名称
  ),
}
