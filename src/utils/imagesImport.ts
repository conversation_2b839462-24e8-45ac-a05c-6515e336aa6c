/**
 * 动态导入所有图片资源
 * 自动导入 src/assets/images 目录下的所有 .png 文件
 */

// 获取所有图片模块
const imageModules = import.meta.glob('../assets/images/**/*.png', { eager: true })

// 工具函数：将文件名转换为驼峰命名
function toCamelCase(str: string): string {
  return str
    .split(/[-_\/]/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('')
}

// 构建图片映射对象
const images: Record<string, string> = {}

// 处理所有图片路径
Object.entries(imageModules).forEach(([path, module]) => {
  // 提取文件名（不带扩展名）
  const fileName = path.split('/').pop()?.replace('.png', '') || ''

  // 转换为驼峰命名
  const camelCaseName = toCamelCase(fileName)

  // 获取图片路径
  const imageUrl = (module as { default: string }).default

  // 添加到导出对象
  images[camelCaseName] = imageUrl
})

// 导出所有图片
export const {
  // Admin 目录下的图片
  Allocated,
  BgInfo,
  CopyIcon,
  File,
  FollowProgress,
  FollowedUp,
  Invalid,
  KeyStageIcon,
  LeadIcon,
  ProcessEndL,
  StageIcon,
  TagIcon,
  ToBack,
  Unlimited,
  Upload,
  Wait,

  // 其他目录的图片
  AccountLogo,
  DefaultAvatar,
  DefaultMall,
  IconCheck,
  LoginBg,
  Login,
  LogoLeft,
  Logo,
  MallLogoBg,
  PlatformLogoBg,
  RecordIcon,
  Schoolboy,
  StoreLogoBg,
  Tool,
  TriangleLight,
  Trophy,
  WxTencent,

  // 可以根据需要继续添加...
} = images

// 默认导出整个图片对象
export default images
