/**
 * 图标解析器
 * 用于将后端返回的图标字符串解析为对应的图标渲染函数
 */

import { constantRouterIcon } from '@/router/icons/router-icons'
import { renderIcon } from '@/utils/index'
import { EllipseSharp } from '@vicons/ionicons5'

/**
 * 解析图标字符串为图标渲染函数
 * @param iconString 后端返回的图标字符串，如 "menu:customer"
 * @returns 图标渲染函数
 */
export function resolveIcon(iconString: string | undefined) {
  // 如果没有图标字符串，返回默认图标
  if (!iconString) {
    return renderIcon(EllipseSharp, { size: 6 })
  }

  // 如果已经是函数，直接返回（兼容现有的硬编码方式）
  if (typeof iconString === 'function') {
    return iconString
  }

  // 如果是字符串，从图标映射表中查找
  if (typeof iconString === 'string') {
    // 检查是否在图标映射表中
    if (constantRouterIcon[iconString]) {
      return constantRouterIcon[iconString]
    }
    // 如果不在映射表中，返回默认图标
    console.warn(`图标 "${iconString}" 未在图标映射表中找到，使用默认图标`)
    return renderIcon(EllipseSharp, { size: 6 })
  }

  // 其他情况返回默认图标
  return renderIcon(EllipseSharp, { size: 6 })
}

/**
 * 批量解析菜单数据中的图标
 * @param menuData 菜单数据
 * @returns 解析后的菜单数据
 */
export function resolveMenuIcons(menuData: any[]): any[] {
  return menuData.map((item) => {
    const resolvedItem = { ...item }

    // 解析当前项的图标
    if (item.icon) {
      resolvedItem.icon = resolveIcon(item.icon)
    }

    // 解析 meta 中的图标
    if (item.meta?.icon) {
      resolvedItem.meta = {
        ...item.meta,
        icon: resolveIcon(item.meta.icon),
      }
    }

    // 递归处理子菜单
    if (item.children && item.children.length > 0) {
      resolvedItem.children = resolveMenuIcons(item.children)
    }

    return resolvedItem
  })
}

/**
 * 为菜单生成函数提供图标解析支持
 * 这个函数可以在 generatorMenu 等函数中使用
 */
export function enhanceMenuWithIconResolver(menuItem: any) {
  const enhanced = { ...menuItem }

  // 解析图标
  if (enhanced.icon) {
    enhanced.icon = resolveIcon(enhanced.icon)
  }

  if (enhanced.meta?.icon) {
    enhanced.meta = {
      ...enhanced.meta,
      icon: resolveIcon(enhanced.meta.icon),
    }
  }

  return enhanced
}
