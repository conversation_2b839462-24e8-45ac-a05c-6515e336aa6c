// 自动生成的 SCSS 变量文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

// ===== 项目设置变量 =====
$nav-width: 200px;
$nav-min-width: 64px;
$partition-nav-width: 168px;
$partition-sub-nav-width: 180px;
$partition-sub-nav-min-width: 64px;
$sub-nav-width: 124px;
$header-height: 57px;
$border-radius: 4px;

// ===== 颜色变量 =====
$color-orange-dot: #F68F46;
$color-red-dot: #F96F6E;
$color-blue-dot: #629BF6;
$color-green-dot: #18AF66;
$color-purple-dot: #9765F2;
$color-yellow-dot: #FF912B;
$color-cyan-dot: #00C1D4;
$color-gray-dot: #C7CBDC;
$color-blue-deep: #1C61F6;
$color-blue-deep-hover: #2F80FF;
$color-blue-deep-pressed: #004DF2;
$color-gray: #C7CBDC;
$color-red-assist: #ED4A3F;
$color-red-assist-hover: #F45E54;
$color-red-assist-pressed: #DC372C;
$color-orange-assist: #FF912B;
$color-yellow-assist: #FFF3DD;
$color-green-assist: #18AF66;
$color-purple-assist: #9765F2;
$color-blue-tag: #DFEEFF;
$color-blue-tag-border: #8BAFFF;
$color-red-tag: #FFE9E7;
$color-red-tag-border: #F3A09A;
$color-green-tag: #E3FAE8;
$color-green-tag-border: #80C790;
$color-yellow-tag: #FFF3DD;
$color-yellow-tag-border: #F4C05F;
$color-cyan-tag: #D5FAF6;
$color-cyan-tag-border: #7BBEB6;
$color-purple-tag: #FAEBFA;
$color-purple-tag-border: #E7A9EA;
$color-blue-purple-tag: #EFEFFD;
$color-blue-purple-tag-border: #AEADFF;
$color-orange-tag: #FFF1EA;
$color-orange-tag-border: #FFB28A;
$color-gray-tag: #E9E9E9;
$color-flow-gray-tag-border: rgba(0, 0, 0, 0.25);
$color-flow-blue-tag: #DFEEFF;
$color-flow-gray-tag: #E8ECF3;
$color-title-deep-gray: rgba(0, 0, 0, 0.85);
$color-text-deep-gray: rgba(0, 0, 0, 0.65);
$color-text-middle-gray: rgba(0, 0, 0, 0.45);
$color-text-light-gray: rgba(0, 0, 0, 0.25);
$color-white: #FFFFFF;
$color-left-bg: #E4EDFC;
$color-right-bg: #F3F6FD;
$color-tag-gray: #EDF0F5;
$color-line-gray: #E8ECF3;
$color-line-gray-2: #E9E9E9;
$color-table-hover-bg: #F7F8FA;
$color-table-hover-light: #FAFBFC;
$color-gray-button: #F2F3F5;
$color-text-border: #E1E1E1;
$color-text-border-hover: #C7CBDC;
$color-mask: rgba(0, 0, 0, 0.30);
$color-avatar-blue: #629BF6;

// ===== 项目设置映射 =====
$project-settings: (
  'navWidth': $nav-width,
  'navMinWidth': $nav-min-width,
  'partitionNavWidth': $partition-nav-width,
  'partitionSubNavWidth': $partition-sub-nav-width,
  'partitionSubNavMinWidth': $partition-sub-nav-min-width,
  'subNavWidth': $sub-nav-width,
  'headerHeight': $header-height,
  'borderRadius': $border-radius,
);

// ===== 颜色映射 =====
$color-map: (
  'orange-dot': $color-orange-dot,
  'red-dot': $color-red-dot,
  'blue-dot': $color-blue-dot,
  'green-dot': $color-green-dot,
  'purple-dot': $color-purple-dot,
  'yellow-dot': $color-yellow-dot,
  'cyan-dot': $color-cyan-dot,
  'gray-dot': $color-gray-dot,
  'blue-deep': $color-blue-deep,
  'blue-deep-hover': $color-blue-deep-hover,
  'blue-deep-pressed': $color-blue-deep-pressed,
  'gray': $color-gray,
  'red-assist': $color-red-assist,
  'red-assist-hover': $color-red-assist-hover,
  'red-assist-pressed': $color-red-assist-pressed,
  'orange-assist': $color-orange-assist,
  'yellow-assist': $color-yellow-assist,
  'green-assist': $color-green-assist,
  'purple-assist': $color-purple-assist,
  'blue-tag': $color-blue-tag,
  'blue-tag-border': $color-blue-tag-border,
  'red-tag': $color-red-tag,
  'red-tag-border': $color-red-tag-border,
  'green-tag': $color-green-tag,
  'green-tag-border': $color-green-tag-border,
  'yellow-tag': $color-yellow-tag,
  'yellow-tag-border': $color-yellow-tag-border,
  'cyan-tag': $color-cyan-tag,
  'cyan-tag-border': $color-cyan-tag-border,
  'purple-tag': $color-purple-tag,
  'purple-tag-border': $color-purple-tag-border,
  'blue-purple-tag': $color-blue-purple-tag,
  'blue-purple-tag-border': $color-blue-purple-tag-border,
  'orange-tag': $color-orange-tag,
  'orange-tag-border': $color-orange-tag-border,
  'gray-tag': $color-gray-tag,
  'flow-gray-tag-border': $color-flow-gray-tag-border,
  'flow-blue-tag': $color-flow-blue-tag,
  'flow-gray-tag': $color-flow-gray-tag,
  'title-deep-gray': $color-title-deep-gray,
  'text-deep-gray': $color-text-deep-gray,
  'text-middle-gray': $color-text-middle-gray,
  'text-light-gray': $color-text-light-gray,
  'white': $color-white,
  'left-bg': $color-left-bg,
  'right-bg': $color-right-bg,
  'tag-gray': $color-tag-gray,
  'line-gray': $color-line-gray,
  'line-gray-2': $color-line-gray-2,
  'table-hover-bg': $color-table-hover-bg,
  'table-hover-light': $color-table-hover-light,
  'gray-button': $color-gray-button,
  'text-border': $color-text-border,
  'text-border-hover': $color-text-border-hover,
  'mask': $color-mask,
  'avatar-blue': $color-avatar-blue,
);
