// 自动生成的工具类 SCSS 文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

// ===== 文本颜色工具类 =====
.text-orange-dot {
  color: #F68F46 !important;
}
.text-red-dot {
  color: #F96F6E !important;
}
.text-blue-dot {
  color: #629BF6 !important;
}
.text-green-dot {
  color: #18AF66 !important;
}
.text-purple-dot {
  color: #9765F2 !important;
}
.text-yellow-dot {
  color: #FF912B !important;
}
.text-cyan-dot {
  color: #00C1D4 !important;
}
.text-gray-dot {
  color: #C7CBDC !important;
}
.text-blue-deep {
  color: #1C61F6 !important;
}
.text-blue-deep-hover {
  color: #2F80FF !important;
}
.text-blue-deep-pressed {
  color: #004DF2 !important;
}
.text-gray {
  color: #C7CBDC !important;
}
.text-red-assist {
  color: #ED4A3F !important;
}
.text-red-assist-hover {
  color: #F45E54 !important;
}
.text-red-assist-pressed {
  color: #DC372C !important;
}
.text-orange-assist {
  color: #FF912B !important;
}
.text-yellow-assist {
  color: #FFF3DD !important;
}
.text-green-assist {
  color: #18AF66 !important;
}
.text-purple-assist {
  color: #9765F2 !important;
}
.text-blue-tag {
  color: #DFEEFF !important;
}
.text-blue-tag-border {
  color: #8BAFFF !important;
}
.text-red-tag {
  color: #FFE9E7 !important;
}
.text-red-tag-border {
  color: #F3A09A !important;
}
.text-green-tag {
  color: #E3FAE8 !important;
}
.text-green-tag-border {
  color: #80C790 !important;
}
.text-yellow-tag {
  color: #FFF3DD !important;
}
.text-yellow-tag-border {
  color: #F4C05F !important;
}
.text-cyan-tag {
  color: #D5FAF6 !important;
}
.text-cyan-tag-border {
  color: #7BBEB6 !important;
}
.text-purple-tag {
  color: #FAEBFA !important;
}
.text-purple-tag-border {
  color: #E7A9EA !important;
}
.text-blue-purple-tag {
  color: #EFEFFD !important;
}
.text-blue-purple-tag-border {
  color: #AEADFF !important;
}
.text-orange-tag {
  color: #FFF1EA !important;
}
.text-orange-tag-border {
  color: #FFB28A !important;
}
.text-gray-tag {
  color: #E9E9E9 !important;
}
.text-flow-gray-tag-border {
  color: rgba(0, 0, 0, 0.25) !important;
}
.text-flow-blue-tag {
  color: #DFEEFF !important;
}
.text-flow-gray-tag {
  color: #E8ECF3 !important;
}
.text-title-deep-gray {
  color: rgba(0, 0, 0, 0.85) !important;
}
.text-text-deep-gray {
  color: rgba(0, 0, 0, 0.65) !important;
}
.text-text-middle-gray {
  color: rgba(0, 0, 0, 0.45) !important;
}
.text-text-light-gray {
  color: rgba(0, 0, 0, 0.25) !important;
}
.text-white {
  color: #FFFFFF !important;
}
.text-left-bg {
  color: #E4EDFC !important;
}
.text-right-bg {
  color: #F3F6FD !important;
}
.text-tag-gray {
  color: #EDF0F5 !important;
}
.text-line-gray {
  color: #E8ECF3 !important;
}
.text-line-gray-2 {
  color: #E9E9E9 !important;
}
.text-table-hover-bg {
  color: #F7F8FA !important;
}
.text-table-hover-light {
  color: #FAFBFC !important;
}
.text-gray-button {
  color: #F2F3F5 !important;
}
.text-text-border {
  color: #E1E1E1 !important;
}
.text-text-border-hover {
  color: #C7CBDC !important;
}
.text-mask {
  color: rgba(0, 0, 0, 0.30) !important;
}
.text-avatar-blue {
  color: #629BF6 !important;
}

// ===== 背景颜色工具类 =====
.bg-orange-dot {
  background-color: #F68F46 !important;
}
.bg-red-dot {
  background-color: #F96F6E !important;
}
.bg-blue-dot {
  background-color: #629BF6 !important;
}
.bg-green-dot {
  background-color: #18AF66 !important;
}
.bg-purple-dot {
  background-color: #9765F2 !important;
}
.bg-yellow-dot {
  background-color: #FF912B !important;
}
.bg-cyan-dot {
  background-color: #00C1D4 !important;
}
.bg-gray-dot {
  background-color: #C7CBDC !important;
}
.bg-blue-deep {
  background-color: #1C61F6 !important;
}
.bg-blue-deep-hover {
  background-color: #2F80FF !important;
}
.bg-blue-deep-pressed {
  background-color: #004DF2 !important;
}
.bg-gray {
  background-color: #C7CBDC !important;
}
.bg-red-assist {
  background-color: #ED4A3F !important;
}
.bg-red-assist-hover {
  background-color: #F45E54 !important;
}
.bg-red-assist-pressed {
  background-color: #DC372C !important;
}
.bg-orange-assist {
  background-color: #FF912B !important;
}
.bg-yellow-assist {
  background-color: #FFF3DD !important;
}
.bg-green-assist {
  background-color: #18AF66 !important;
}
.bg-purple-assist {
  background-color: #9765F2 !important;
}
.bg-blue-tag {
  background-color: #DFEEFF !important;
}
.bg-blue-tag-border {
  background-color: #8BAFFF !important;
}
.bg-red-tag {
  background-color: #FFE9E7 !important;
}
.bg-red-tag-border {
  background-color: #F3A09A !important;
}
.bg-green-tag {
  background-color: #E3FAE8 !important;
}
.bg-green-tag-border {
  background-color: #80C790 !important;
}
.bg-yellow-tag {
  background-color: #FFF3DD !important;
}
.bg-yellow-tag-border {
  background-color: #F4C05F !important;
}
.bg-cyan-tag {
  background-color: #D5FAF6 !important;
}
.bg-cyan-tag-border {
  background-color: #7BBEB6 !important;
}
.bg-purple-tag {
  background-color: #FAEBFA !important;
}
.bg-purple-tag-border {
  background-color: #E7A9EA !important;
}
.bg-blue-purple-tag {
  background-color: #EFEFFD !important;
}
.bg-blue-purple-tag-border {
  background-color: #AEADFF !important;
}
.bg-orange-tag {
  background-color: #FFF1EA !important;
}
.bg-orange-tag-border {
  background-color: #FFB28A !important;
}
.bg-gray-tag {
  background-color: #E9E9E9 !important;
}
.bg-flow-gray-tag-border {
  background-color: rgba(0, 0, 0, 0.25) !important;
}
.bg-flow-blue-tag {
  background-color: #DFEEFF !important;
}
.bg-flow-gray-tag {
  background-color: #E8ECF3 !important;
}
.bg-title-deep-gray {
  background-color: rgba(0, 0, 0, 0.85) !important;
}
.bg-text-deep-gray {
  background-color: rgba(0, 0, 0, 0.65) !important;
}
.bg-text-middle-gray {
  background-color: rgba(0, 0, 0, 0.45) !important;
}
.bg-text-light-gray {
  background-color: rgba(0, 0, 0, 0.25) !important;
}
.bg-white {
  background-color: #FFFFFF !important;
}
.bg-left-bg {
  background-color: #E4EDFC !important;
}
.bg-right-bg {
  background-color: #F3F6FD !important;
}
.bg-tag-gray {
  background-color: #EDF0F5 !important;
}
.bg-line-gray {
  background-color: #E8ECF3 !important;
}
.bg-line-gray-2 {
  background-color: #E9E9E9 !important;
}
.bg-table-hover-bg {
  background-color: #F7F8FA !important;
}
.bg-table-hover-light {
  background-color: #FAFBFC !important;
}
.bg-gray-button {
  background-color: #F2F3F5 !important;
}
.bg-text-border {
  background-color: #E1E1E1 !important;
}
.bg-text-border-hover {
  background-color: #C7CBDC !important;
}
.bg-mask {
  background-color: rgba(0, 0, 0, 0.30) !important;
}
.bg-avatar-blue {
  background-color: #629BF6 !important;
}

// ===== 边框颜色工具类 =====
.border-orange-dot {
  border-color: #F68F46 !important;
}
.border-red-dot {
  border-color: #F96F6E !important;
}
.border-blue-dot {
  border-color: #629BF6 !important;
}
.border-green-dot {
  border-color: #18AF66 !important;
}
.border-purple-dot {
  border-color: #9765F2 !important;
}
.border-yellow-dot {
  border-color: #FF912B !important;
}
.border-cyan-dot {
  border-color: #00C1D4 !important;
}
.border-gray-dot {
  border-color: #C7CBDC !important;
}
.border-blue-deep {
  border-color: #1C61F6 !important;
}
.border-blue-deep-hover {
  border-color: #2F80FF !important;
}
.border-blue-deep-pressed {
  border-color: #004DF2 !important;
}
.border-gray {
  border-color: #C7CBDC !important;
}
.border-red-assist {
  border-color: #ED4A3F !important;
}
.border-red-assist-hover {
  border-color: #F45E54 !important;
}
.border-red-assist-pressed {
  border-color: #DC372C !important;
}
.border-orange-assist {
  border-color: #FF912B !important;
}
.border-yellow-assist {
  border-color: #FFF3DD !important;
}
.border-green-assist {
  border-color: #18AF66 !important;
}
.border-purple-assist {
  border-color: #9765F2 !important;
}
.border-blue-tag {
  border-color: #DFEEFF !important;
}
.border-blue-tag-border {
  border-color: #8BAFFF !important;
}
.border-red-tag {
  border-color: #FFE9E7 !important;
}
.border-red-tag-border {
  border-color: #F3A09A !important;
}
.border-green-tag {
  border-color: #E3FAE8 !important;
}
.border-green-tag-border {
  border-color: #80C790 !important;
}
.border-yellow-tag {
  border-color: #FFF3DD !important;
}
.border-yellow-tag-border {
  border-color: #F4C05F !important;
}
.border-cyan-tag {
  border-color: #D5FAF6 !important;
}
.border-cyan-tag-border {
  border-color: #7BBEB6 !important;
}
.border-purple-tag {
  border-color: #FAEBFA !important;
}
.border-purple-tag-border {
  border-color: #E7A9EA !important;
}
.border-blue-purple-tag {
  border-color: #EFEFFD !important;
}
.border-blue-purple-tag-border {
  border-color: #AEADFF !important;
}
.border-orange-tag {
  border-color: #FFF1EA !important;
}
.border-orange-tag-border {
  border-color: #FFB28A !important;
}
.border-gray-tag {
  border-color: #E9E9E9 !important;
}
.border-flow-gray-tag-border {
  border-color: rgba(0, 0, 0, 0.25) !important;
}
.border-flow-blue-tag {
  border-color: #DFEEFF !important;
}
.border-flow-gray-tag {
  border-color: #E8ECF3 !important;
}
.border-title-deep-gray {
  border-color: rgba(0, 0, 0, 0.85) !important;
}
.border-text-deep-gray {
  border-color: rgba(0, 0, 0, 0.65) !important;
}
.border-text-middle-gray {
  border-color: rgba(0, 0, 0, 0.45) !important;
}
.border-text-light-gray {
  border-color: rgba(0, 0, 0, 0.25) !important;
}
.border-white {
  border-color: #FFFFFF !important;
}
.border-left-bg {
  border-color: #E4EDFC !important;
}
.border-right-bg {
  border-color: #F3F6FD !important;
}
.border-tag-gray {
  border-color: #EDF0F5 !important;
}
.border-line-gray {
  border-color: #E8ECF3 !important;
}
.border-line-gray-2 {
  border-color: #E9E9E9 !important;
}
.border-table-hover-bg {
  border-color: #F7F8FA !important;
}
.border-table-hover-light {
  border-color: #FAFBFC !important;
}
.border-gray-button {
  border-color: #F2F3F5 !important;
}
.border-text-border {
  border-color: #E1E1E1 !important;
}
.border-text-border-hover {
  border-color: #C7CBDC !important;
}
.border-mask {
  border-color: rgba(0, 0, 0, 0.30) !important;
}
.border-avatar-blue {
  border-color: #629BF6 !important;
}

// ===== 阴影颜色工具类 =====
.shadow-orange-dot {
  box-shadow: 0 2px 8px #F68F4633 !important;
}
.shadow-red-dot {
  box-shadow: 0 2px 8px #F96F6E33 !important;
}
.shadow-blue-dot {
  box-shadow: 0 2px 8px #629BF633 !important;
}
.shadow-green-dot {
  box-shadow: 0 2px 8px #18AF6633 !important;
}
.shadow-purple-dot {
  box-shadow: 0 2px 8px #9765F233 !important;
}
.shadow-yellow-dot {
  box-shadow: 0 2px 8px #FF912B33 !important;
}
.shadow-cyan-dot {
  box-shadow: 0 2px 8px #00C1D433 !important;
}
.shadow-gray-dot {
  box-shadow: 0 2px 8px #C7CBDC33 !important;
}
.shadow-blue-deep {
  box-shadow: 0 2px 8px #1C61F633 !important;
}
.shadow-blue-deep-hover {
  box-shadow: 0 2px 8px #2F80FF33 !important;
}
.shadow-blue-deep-pressed {
  box-shadow: 0 2px 8px #004DF233 !important;
}
.shadow-gray {
  box-shadow: 0 2px 8px #C7CBDC33 !important;
}
.shadow-red-assist {
  box-shadow: 0 2px 8px #ED4A3F33 !important;
}
.shadow-red-assist-hover {
  box-shadow: 0 2px 8px #F45E5433 !important;
}
.shadow-red-assist-pressed {
  box-shadow: 0 2px 8px #DC372C33 !important;
}
.shadow-orange-assist {
  box-shadow: 0 2px 8px #FF912B33 !important;
}
.shadow-yellow-assist {
  box-shadow: 0 2px 8px #FFF3DD33 !important;
}
.shadow-green-assist {
  box-shadow: 0 2px 8px #18AF6633 !important;
}
.shadow-purple-assist {
  box-shadow: 0 2px 8px #9765F233 !important;
}
.shadow-blue-tag {
  box-shadow: 0 2px 8px #DFEEFF33 !important;
}
.shadow-blue-tag-border {
  box-shadow: 0 2px 8px #8BAFFF33 !important;
}
.shadow-red-tag {
  box-shadow: 0 2px 8px #FFE9E733 !important;
}
.shadow-red-tag-border {
  box-shadow: 0 2px 8px #F3A09A33 !important;
}
.shadow-green-tag {
  box-shadow: 0 2px 8px #E3FAE833 !important;
}
.shadow-green-tag-border {
  box-shadow: 0 2px 8px #80C79033 !important;
}
.shadow-yellow-tag {
  box-shadow: 0 2px 8px #FFF3DD33 !important;
}
.shadow-yellow-tag-border {
  box-shadow: 0 2px 8px #F4C05F33 !important;
}
.shadow-cyan-tag {
  box-shadow: 0 2px 8px #D5FAF633 !important;
}
.shadow-cyan-tag-border {
  box-shadow: 0 2px 8px #7BBEB633 !important;
}
.shadow-purple-tag {
  box-shadow: 0 2px 8px #FAEBFA33 !important;
}
.shadow-purple-tag-border {
  box-shadow: 0 2px 8px #E7A9EA33 !important;
}
.shadow-blue-purple-tag {
  box-shadow: 0 2px 8px #EFEFFD33 !important;
}
.shadow-blue-purple-tag-border {
  box-shadow: 0 2px 8px #AEADFF33 !important;
}
.shadow-orange-tag {
  box-shadow: 0 2px 8px #FFF1EA33 !important;
}
.shadow-orange-tag-border {
  box-shadow: 0 2px 8px #FFB28A33 !important;
}
.shadow-gray-tag {
  box-shadow: 0 2px 8px #E9E9E933 !important;
}
.shadow-flow-gray-tag-border {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25)33 !important;
}
.shadow-flow-blue-tag {
  box-shadow: 0 2px 8px #DFEEFF33 !important;
}
.shadow-flow-gray-tag {
  box-shadow: 0 2px 8px #E8ECF333 !important;
}
.shadow-title-deep-gray {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.85)33 !important;
}
.shadow-text-deep-gray {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.65)33 !important;
}
.shadow-text-middle-gray {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45)33 !important;
}
.shadow-text-light-gray {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25)33 !important;
}
.shadow-white {
  box-shadow: 0 2px 8px #FFFFFF33 !important;
}
.shadow-left-bg {
  box-shadow: 0 2px 8px #E4EDFC33 !important;
}
.shadow-right-bg {
  box-shadow: 0 2px 8px #F3F6FD33 !important;
}
.shadow-tag-gray {
  box-shadow: 0 2px 8px #EDF0F533 !important;
}
.shadow-line-gray {
  box-shadow: 0 2px 8px #E8ECF333 !important;
}
.shadow-line-gray-2 {
  box-shadow: 0 2px 8px #E9E9E933 !important;
}
.shadow-table-hover-bg {
  box-shadow: 0 2px 8px #F7F8FA33 !important;
}
.shadow-table-hover-light {
  box-shadow: 0 2px 8px #FAFBFC33 !important;
}
.shadow-gray-button {
  box-shadow: 0 2px 8px #F2F3F533 !important;
}
.shadow-text-border {
  box-shadow: 0 2px 8px #E1E1E133 !important;
}
.shadow-text-border-hover {
  box-shadow: 0 2px 8px #C7CBDC33 !important;
}
.shadow-mask {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.30)33 !important;
}
.shadow-avatar-blue {
  box-shadow: 0 2px 8px #629BF633 !important;
}
