// 自动生成的样式索引文件
// 请勿手动修改，运行 npm run generate:styles 重新生成

@use './variables.scss' as *;
@use './utilities.scss' as *;
@use 'sass:map';

// 导出颜色映射函数
@function get-color($key) {
  @if map.has-key($color-map, $key) {
    @return map.get($color-map, $key);
  } @else {
    @warn "颜色 '#{$key}' 不存在于 $color-map 中";
    @return null;
  }
}

// 导出项目设置函数
@function get-setting($key) {
  @if map.has-key($project-settings, $key) {
    @return map.get($project-settings, $key);
  } @else {
    @warn "设置 '#{$key}' 不存在于 $project-settings 中";
    @return null;
  }
}

// Mixin: 应用主题颜色
@mixin theme-color($property, $color-key) {
  #{$property}: get-color($color-key);
}

// Mixin: 应用项目设置
@mixin apply-setting($property, $setting-key) {
  #{$property}: get-setting($setting-key);
}

// 常用的项目设置 Mixin
@mixin border-radius($radius: null) {
  @if $radius {
    border-radius: $radius;
  } @else {
    border-radius: get-setting('borderRadius');
  }
}
