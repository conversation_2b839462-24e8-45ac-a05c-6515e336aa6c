import { defineMock } from '@alova/mock'
import { resultSuccess, resultError, MockStorage } from '../util'

// 初始菜单数据
const initialMenuData = [
  {
    id: 3,
    parent_id: 0,
    platform: 'admin',
    title: 'Dashboard',
    subtitle: 'dashboard',
    name: 'dashboard',
    path: '/dashboard',
    redirect: '/dashboard/console',
    component: 'LAYOUT',
    menu_type: 'menu',
    icon: 'DashboardOutlined',
    target: '_self',
    sort: 1,
    hidden: 0,
    enabled: 1,
    permissions: ['dashboard_read', 'dashboard_write'],
    children: [
      {
        id: 31,
        parent_id: 3,
        platform: 'admin',
        title: '主控台',
        subtitle: 'console',
        name: 'dashboard_console',
        path: '/dashboard/console',
        redirect: '',
        component: '/dashboard/console/console',
        menu_type: 'menu',
        icon: null,
        target: '_self',
        sort: 2,
        hidden: 0,
        enabled: 1,
        permissions: ['console_read'],
      },
    ],
  },
]

// 创建菜单存储实例
const menuStorage = new MockStorage('menu_data', initialMenuData)

// 递归查找菜单
const findMenuById = (list: any[], id: number): any => {
  for (const item of list) {
    if (item.id === id) return item
    if (item.children) {
      const found = findMenuById(item.children, id)
      if (found) return found
    }
  }
  return null
}

// 递归删除菜单
const deleteMenuById = (list: any[], id: number): boolean => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].id === id) {
      list.splice(i, 1)
      return true
    }
    if (list[i].children?.length) {
      if (deleteMenuById(list[i].children, id)) {
        return true
      }
    }
  }
  return false
}

// 生成新的ID
const generateId = (list: any[]): number => {
  let maxId = 0
  const findMaxId = (items: any[]) => {
    items.forEach((item) => {
      maxId = Math.max(maxId, item.id)
      if (item.children) findMaxId(item.children)
    })
  }
  findMaxId(list)
  return maxId + 1
}

export default defineMock({
  // 获取菜单列表
  '/api/menu/list': () => resultSuccess({ list: menuStorage.getData() }),

  // 创建菜单
  '[POST]/api/menu': (params: { data: any }) => {
    const menuData = menuStorage.getData()
    const newMenu = {
      ...params.data,
      id: generateId(menuData),
      children: [],
    }

    if (params.data.parent_id === 0) {
      menuData.push(newMenu)
    } else {
      const parentMenu = findMenuById(menuData, params.data.parent_id)
      if (!parentMenu) {
        return resultError('父级菜单不存在')
      }
      if (!parentMenu.children) parentMenu.children = []
      parentMenu.children.push(newMenu)
    }

    // 保存到 localStorage
    menuStorage.saveData(menuData)

    return resultSuccess({
      id: newMenu.id,
      parent_id: newMenu.parent_id,
      title: newMenu.title,
      path: newMenu.path,
      menu_type: newMenu.menu_type,
      enabled: newMenu.enabled,
    })
  },

  // 更新菜单
  '[PUT]/api/menu/{id}': (params: { params: { id: number }; data: any }) => {
    const menuData = menuStorage.getData()
    const menu = findMenuById(menuData, Number(params.params.id))

    if (!menu) {
      return resultError('菜单不存在')
    }

    // 更新菜单数据
    Object.assign(menu, params.data)

    // 保存到 localStorage
    menuStorage.saveData(menuData)

    return resultSuccess({
      id: menu.id,
      title: menu.title,
      path: menu.path,
    })
  },

  // 删除菜单
  '[DELETE]/api/menu/{id}': (params: { params: { id: number } }) => {
    const menuData = menuStorage.getData()
    if (!deleteMenuById(menuData, Number(params.params.id))) {
      return resultError('菜单不存在')
    }
    // 保存到 localStorage
    menuStorage.saveData(menuData)
    return resultSuccess({ message: '菜单删除成功' })
  },
})
