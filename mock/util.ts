import { StorageKeys } from './enums/keys'

export function resultSuccess(result, { message = 'ok' } = {}) {
  return {
    code: 0,
    data: result,
    result,
    msg: message,
    message,
    type: 'success',
  }
}

export function resultPageSuccess<T = any>(
  page: number,
  pageSize: number,
  list: T[],
  { message = 'ok' } = {},
) {
  const pageData = pagination(page, pageSize, list)

  return {
    ...resultSuccess({
      page,
      pageSize,
      pageCount: list.length,
      list: pageData,
    }),
    message,
    msg: message,
  }
}

export function resultError(message = 'Request failed', { code = -1, result = null } = {}) {
  return {
    code,
    data: result,
    result,
    msg: message,
    message,
    type: 'error',
  }
}

export function pagination<T = any>(pageNo: number, pageSize: number, array: T[]): T[] {
  const offset = (pageNo - 1) * Number(pageSize)
  const ret =
    offset + Number(pageSize) >= array.length
      ? array.slice(offset, array.length)
      : array.slice(offset, offset + Number(pageSize))
  return ret
}

/**
 * @param {Number} times 回调函数需要执行的次数
 * @param {Function} callback 回调函数
 */
export function doCustomTimes(times: number, callback: any) {
  let i = -1
  while (++i < times) {
    callback(i)
  }
}

export interface requestParams {
  method: string
  body: any
  headers?: { token?: string; authorization?: string }
  query: any
}

/**
 * @description 本函数用于从request数据中获取token，请根据项目的实际情况修改
 */
export function getRequestToken({ headers }: requestParams): string | undefined {
  return headers?.token || headers?.authorization?.replace('Bearer ', '')
}

// 从 localStorage 获取 token 信息
export const getTokenStore = () => {
  const storeStr = localStorage.getItem(StorageKeys.TOKEN_STORE)
  return storeStr ? new Map(JSON.parse(storeStr)) : new Map()
}

// 保存 token 信息到 localStorage
export const saveTokenStore = (tokenStore: Map<string, any>) => {
  localStorage.setItem(StorageKeys.TOKEN_STORE, JSON.stringify(Array.from(tokenStore.entries())))
}

// 清除 token 信息
export const clearTokenStore = () => {
  localStorage.removeItem(StorageKeys.TOKEN_STORE)
}

// 添加调试日志
export const log = (api: string, params: any, result: any) => {
  console.log(`[Mock API] ${api}`, {
    params,
    result,
    tokenStore: Array.from(getTokenStore().entries()),
  })
}

/**
 * Mock 数据本地存储工具
 * @param key 存储键名
 * @param initialData 初始数据
 */
export class MockStorage<T> {
  private key: string
  private initialData: T
  private prefix: string

  constructor(key: string, initialData: T) {
    this.key = key
    this.initialData = initialData
    this.prefix = '__mock'
  }

  private get storageKey(): string {
    return `${this.prefix}_${this.key}`
  }

  // 获取数据
  getData(): T {
    console.log(this.storageKey)
    const storedData = localStorage.getItem(this.storageKey)
    if (storedData) {
      const data = JSON.parse(storedData)
      console.log(`[MockStorage] Get ${this.key}:`, data)
      return data
    }
    console.log(`[MockStorage] Initialize ${this.key}:`, this.initialData)
    this.saveData(this.initialData)
    return this.initialData
  }

  // 保存数据
  saveData(data: T): void {
    console.log(`[MockStorage] Save ${this.key}:`, data)
    localStorage.setItem(this.storageKey, JSON.stringify(data))
  }

  // 重置数据
  resetData(): void {
    console.log(`[MockStorage] Reset ${this.key}`)
    this.saveData(this.initialData)
  }

  // 清除数据
  clearData(): void {
    console.log(`[MockStorage] Clear ${this.key}`)
    localStorage.removeItem(this.storageKey)
  }
}
