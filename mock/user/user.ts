import { UserInfo } from '@/api/system/user'
import { defineMock } from '@alova/mock'
import { faker } from '@faker-js/faker'
import dayjs from 'dayjs'
import { resultSuccess, resultError } from '../util'
import { ErrorCode } from '../enums/errorCode'
import { getTokenStore, log, clearTokenStore } from '../util'

const userInfo: UserInfo = {
  id: '1',
  avatar: 'https://q1.qlogo.cn/g?b=qq&nk=*********&s=640',
  birthday: faker.date.birthdate(),
  email: '<EMAIL>',
  username: '管理员',
  account: 'admin',
  sex: '1',
  role: 'admin',
  permissions: [
    {
      label: '主控台',
      value: 'dashboard_console',
    },
    {
      label: '监控页',
      value: 'dashboard_monitor',
    },
    {
      label: '工作台',
      value: 'dashboard_workplace',
    },
    {
      label: '基础列表',
      value: 'basic_list',
    },
    {
      label: '基础列表删除',
      value: 'basic_list_delete',
    },
  ],
  status: 1,
  createDate: faker.date.anytime(),
}

const userList = Array.from({ length: 10 }).map(() => {
  const key = faker.number.int({ min: 2, max: 6 })
  return {
    id: faker.string.uuid(),
    avatar: `https://img.naiveadmin.com/assets/avatar/avatar-${key}.jpg?v=${faker.string.numeric(
      4,
    )}`,
    birthday: faker.date.birthdate(),
    email: faker.internet.email(),
    username: faker.internet.userName(),
    account: faker.person.firstName(),
    sex: faker.person.sexType(),
    mobile: faker.helpers.fromRegExp(`1898888${faker.string.numeric(4)}`),
    role: faker.helpers.arrayElement(['admin', 'root']),
    permissions: [],
    status: faker.helpers.arrayElement(['close', 'refuse', 'pass']),
    createDate: dayjs(faker.date.anytime()).format('YYYY-MM-DD HH:mm'),
  }
})

export default defineMock({
  // 用户信息
  '/api/account/info': (params) => {
    const authorization = params.headers?.Authorization
    if (!authorization) {
      return resultError('缺少 token', { code: ErrorCode.TOKEN_EXPIRED })
    }

    const token = authorization.replace('Bearer ', '')
    // 从 localStorage 获取 token 信息
    const tokenStore = getTokenStore()
    const tokenInfo = tokenStore.get(token)

    if (!tokenInfo) {
      return resultError('token 无效', { code: ErrorCode.TOKEN_EXPIRED })
    }

    if (Math.floor(Date.now() / 1000) > tokenInfo.expired) {
      return resultError('token 已过期', { code: ErrorCode.TOKEN_EXPIRED })
    }

    const result = resultSuccess(userInfo)
    log('/account/info', params, result)
    return result
  },

  // 用户列表
  '/api/user_list': ({ query }) => {
    const { page = 1, pageSize = 10, name } = query
    const list = userList
    // 并非真实，只是为了模拟搜索结果
    const count = name ? 30 : 60
    const result = resultSuccess({
      page: Number(page),
      pageSize: Number(pageSize),
      pageCount: count,
      total: count * Number(pageSize),
      list,
    })
    log('/api/user_list', { query }, result)
    return result
  },

  // 退出
  '[POST]/api/logout': () => {
    clearTokenStore()
    const result = resultSuccess({})
    log('/api/logout', {}, result)
    return result
  },
})
