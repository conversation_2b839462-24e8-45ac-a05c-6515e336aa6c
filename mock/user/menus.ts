import { defineMock } from '@alova/mock'
import { resultSuccess } from '../util'

//超级管理员
const adminMenusList = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: 'LAYOUT',
    redirect: '/dashboard/console',
    meta: {
      icon: 'DashboardOutlined',
      title: 'Dashboard',
    },
    children: [
      {
        path: 'console',
        name: 'dashboard_console',
        component: '/dashboard/console/console',
        meta: {
          title: '主控台',
        },
      },
      {
        path: 'monitor',
        name: 'dashboard_monitor',
        component: '/dashboard/monitor/monitor',
        meta: {
          title: '监控页',
        },
      },
      {
        path: 'workplace',
        name: 'dashboard_workplace',
        component: '/dashboard/workplace/workplace',
        meta: {
          hidden: true,
          title: '工作台',
        },
      },
    ],
  },
  {
    path: '/list',
    name: 'List',
    component: 'LAYOUT',
    redirect: '/list/basic-list',
    meta: {
      icon: 'TableOutlined',
      title: '列表页面',
    },
    children: [
      {
        path: 'basic-list',
        name: 'basic-list',
        component: '/list/basicList/index',
        meta: {
          title: '基础列表',
        },
      },
    ],
  },
]

//普通管理员
const ordinaryMenusList = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: 'LAYOUT',
    redirect: '/dashboard/console',
    meta: {
      icon: 'DashboardOutlined',
      title: 'Dashboard',
    },
    children: [
      {
        path: 'console',
        name: 'dashboard_console',
        component: '/dashboard/console/console',
        meta: {
          title: '主控台',
        },
      },
      {
        path: 'monitor',
        name: 'dashboard_monitor',
        component: '/dashboard/monitor/monitor',
        meta: {
          title: '监控页',
        },
      },
      {
        path: 'workplace',
        name: 'dashboard_workplace',
        component: '/dashboard/workplace/workplace',
        meta: {
          hidden: true,
          title: '工作台',
        },
      },
    ],
  },
]

// 新增的动态菜单数据
const dynamicMenus = [
  {
    path: '/dashboard',
    component: 'LAYOUT',
    redirect: '/dashboard/console',
    name: 'Home',
    meta: {
      title: '首页',
      icon: 'HomeOutlined',
      sort: 0,
    },
    children: [
      {
        path: 'console',
        name: 'dashboard_console',
        component: '/dashboard/console/console',
        meta: {
          title: '主控台',
          icon: 'DashboardOutlined',
          affix: true,
        },
      },
      {
        path: 'workplace',
        name: 'dashboard_workplace',
        component: '/dashboard/workplace/workplace',
        meta: {
          title: '工作台',
          icon: 'DashboardOutlined',
        },
      },
    ],
  },
  {
    path: '/system',
    component: 'LAYOUT',
    redirect: '/system/menu',
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'SettingOutlined',
      sort: 1,
    },
    children: [
      {
        path: 'user',
        name: 'system_user',
        component: '/system/user/user',
        meta: {
          title: '用户管理',
          icon: 'UserOutlined',
        },
        children: [
          {
            path: 'list',
            name: 'system_user_list',
            component: '/system/user/list',
            meta: {
              title: '用户列表',
              icon: 'UnorderedListOutlined',
            },
          },
          {
            path: 'add',
            name: 'system_user_add',
            component: '/system/user/add',
            meta: {
              title: '添加用户',
              icon: 'PlusOutlined',
            },
          },
          {
            path: 'edit',
            name: 'system_user_edit',
            component: '/system/user/edit',
            meta: {
              title: '编辑用户',
              icon: 'EditOutlined',
              hidden: true,
            },
          },
        ],
      },
      {
        path: 'menu',
        name: 'system_menu',
        component: '/system/menu/index',
        meta: {
          title: '菜单管理',
          icon: 'MenuOutlined',
        },
      },
      {
        path: 'role',
        name: 'system_role',
        component: '/system/role/role',
        meta: {
          title: '角色管理',
          icon: 'TeamOutlined',
        },
        children: [
          {
            path: 'list',
            name: 'system_role_list',
            component: '/system/role/list',
            meta: {
              title: '角色列表',
              icon: 'UnorderedListOutlined',
            },
          },
          {
            path: 'permission',
            name: 'system_role_permission',
            component: '/system/role/permission',
            meta: {
              title: '权限配置',
              icon: 'SafetyOutlined',
            },
          },
        ],
      },
    ],
  },
]

export default defineMock({
  '/api/routes': () => {
    //此处随机了，为了模拟不同角色权限
    const randomNum = Math.floor(Math.random() * 2 + 1)
    return randomNum === 1 ? resultSuccess(adminMenusList) : resultSuccess(ordinaryMenusList)
  },
  '/api/auth/menu': () => resultSuccess(dynamicMenus),
})
