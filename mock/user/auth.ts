import { defineMock } from '@alova/mock'
import { resultSuccess, resultError } from '../util'
import { ErrorCode } from '../enums/errorCode'
import { getTokenStore, saveTokenStore, log } from '../util'

// 模拟 token 过期时间为 10 秒，方便测试
const TOKEN_EXPIRE_TIME = 2000

function generateJwtToken(username: string) {
  const now = Math.floor(Date.now() / 1000)
  const payload = {
    iss: 'qimall',
    aud: 'https://qijianshigw.com',
    jti: `AM${Math.random().toString(36).substring(2)}${Date.now()}`,
    iat: now,
    nbf: now,
    exp: now + TOKEN_EXPIRE_TIME,
    admin: {
      id: 1,
      username,
      nickname: '超级管理员',
      admin_type: 1,
    },
  }

  // 生成 JWT 格式的 token（这里是模拟的固定结构）
  const header = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9'
  const encodedPayload = btoa(unescape(encodeURIComponent(JSON.stringify(payload))))
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
  const signature = '2DtGygKJw7AUQ-bcGmZ55Xhx2iy-nHbNARLCJ30kMrg'

  return `${header}.${encodedPayload}.${signature}`
}

export default defineMock({
  // 登录
  '[POST]/api/auth/login': (params) => {
    const { username, password } = params.data

    if (!username || !password) {
      const result = resultError('请输入用户名和密码')
      return result
    }

    if (username === 'admin' && password === '123456') {
      const token = generateJwtToken(username)
      const expired = Math.floor(Date.now() / 1000) + TOKEN_EXPIRE_TIME

      // 存储 token 信息
      const tokenStore = getTokenStore()
      tokenStore.set(token, {
        token,
        expired,
      })
      saveTokenStore(tokenStore)

      const result = resultSuccess({
        user: username,
        token,
        expired,
      })
      log('/api/auth/login', params, result)
      return result
    }

    const result = resultError('账号或密码错误')
    log('/api/auth/login', params, result)
    return result
  },

  // 刷新 token
  '/api/auth/reload-token': (params) => {
    const authorization = params.headers?.Authorization
    if (!authorization) {
      return resultError('缺少 token', { code: ErrorCode.TOKEN_EXPIRED })
    }

    const token = authorization.replace('Bearer ', '')
    const tokenStore = getTokenStore()
    const tokenInfo = tokenStore.get(token)

    if (!tokenInfo) {
      return resultError('token 无效', { code: ErrorCode.TOKEN_EXPIRED })
    }

    const currentTime = Math.floor(Date.now() / 1000)

    if (currentTime < tokenInfo.expired) {
      // 生成新的 token
      const newToken = generateJwtToken('admin')
      const newExpired = currentTime + TOKEN_EXPIRE_TIME

      // 更新存储的 token 信息
      tokenStore.set(newToken, {
        token: newToken,
        expired: newExpired,
      })
      tokenStore.delete(token)
      saveTokenStore(tokenStore)

      const result = resultSuccess({
        user: 'admin',
        token: newToken,
        expired: newExpired,
      })
      log('/api/auth/reload-token', params, result)
      return result
    }

    const result = resultError('token 已过期', { code: ErrorCode.TOKEN_EXPIRED })
    log('/api/auth/reload-token', params, result)
    return result
  },
})
